CREATE TABLE public."AddOns" (
	add_on_id uuid DEFAULT gen_random_uuid() NOT NULL,
	add_on_name varchar NOT NULL,
	add_on_type varchar NOT NULL,
	add_on_price numeric NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	fk_branch_id varchar NULL,
	modified_at timestamp NULL,
	CONSTRAINT add_ons_pk PRIMARY KEY (add_on_id)
);

CREATE TABLE public."Branches" (
	fk_restaurant_id varchar NOT NULL,
	branch_id varchar NOT NULL,
	branch_name varchar NOT NULL,
	branch_address varchar NOT NULL,
	phone_number varchar NOT NULL,
	branch_timings jsonb NULL,
	status bool DEFAULT false NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	branch_logo varchar NULL,
	branch_description text NULL,
	branch_maps_url varchar NULL,
	branch_category_order _varchar NULL,
	branch_promotional_banner _varchar NULL,
	branch_min_cart_amount numeric NULL,
	branch_location jsonb NULL,
	branch_preparation_time int4 NULL,
	inbox_enabled bool DEFAULT false NULL,
	branch_payment_modes _varchar DEFAULT '{cash,card}'::character varying[] NULL,
	payment_methods jsonb NULL,
	payment_webhook_secret varchar NULL,
	auto_refundable bool DEFAULT true NULL,
	trn_number int8 NULL,
	pre_orders bool DEFAULT false NULL,
	branch_emirate varchar NULL,
	average_spend float8 NULL,
	branch_tags _varchar NULL,
	branch_display_name varchar NULL,
	branch_timezone varchar DEFAULT 'Asia/Dubai'::character varying NULL,
	cancellation_number varchar DEFAULT ''::character varying NULL,
	delivery_module bool DEFAULT false NULL,
	break_timings jsonb NULL,
	break_status_switch bool DEFAULT true NULL,
	break_override_until timestamp NULL,
	CONSTRAINT branches_pk PRIMARY KEY (branch_id)
);

CREATE TABLE public."Categories" (
	category_id uuid DEFAULT gen_random_uuid() NOT NULL,
	category_name varchar NOT NULL,
	category_description text NULL,
	category_availability varchar NOT NULL,
	category_availability_timings jsonb NULL,
	status bool DEFAULT false NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	fk_branch_id varchar NULL,
	CONSTRAINT category_pk PRIMARY KEY (category_id)
);


CREATE TABLE public."Clubs" (
	club_id varchar NOT NULL,
	club_name varchar NOT NULL,
	take_booking bool NOT NULL,
	booking_link varchar NULL,
	phone_number varchar NOT NULL,
	club_logo_url varchar NULL,
	club_bg_img_url varchar NULL,
	club_location varchar NULL,
	club_location_url varchar NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	booking_policy _text NULL,
	club_social_links jsonb NULL,
	club_cancellation_number varchar NULL,
	club_timings jsonb NULL,
	club_emails jsonb NULL,
	club_whatsapp_id varchar NULL,
	club_whatsapp_token varchar NULL,
	payment_method varchar NULL,
	payment_api_key varchar NULL,
	payment_webhook_secret varchar NULL,
	club_sender_email varchar NULL,
	club_email_password varchar NULL,
	club_business_account_id varchar NULL,
	stripe_subscription_id varchar NULL,
	stripe_customer_id varchar NULL,
	subscription_status bool DEFAULT false NOT NULL,
	club_whatsapp_app_id varchar NULL,
	enable_support bool DEFAULT false NOT NULL,
	parent_club varchar NULL,
	inbox_access_enabled bool DEFAULT false NULL,
	network_outlet_id varchar NULL,
	club_partial_payment int4 DEFAULT 25 NULL,
	subscription_graceperiod int4 DEFAULT 15 NOT NULL,
	auto_refundable bool DEFAULT true NULL,
	payment_methods jsonb NULL,
	club_emirate varchar NULL,
	average_spend int4 NULL,
	club_tags _text NULL,
	is_only_for_listing bool DEFAULT false NULL,
	CONSTRAINT "Clubs_pkey" PRIMARY KEY (club_id)
);


CREATE TABLE public."DeliveryZones" (
	zone_id uuid DEFAULT gen_random_uuid() NOT NULL,
	fk_branch_id varchar NULL,
	coordinates jsonb NULL,
	zone_name varchar NULL,
	delivery_fee numeric NULL,
	status bool DEFAULT true NULL,
	fk_restaurant_id varchar NULL,
	fk_shop_id varchar NULL,
	min_cart_amount numeric NULL,
	CONSTRAINT deliveryzones_pk PRIMARY KEY (zone_id)
);


CREATE TABLE public."FoodDiscounts" (
	discount_id uuid DEFAULT gen_random_uuid() NOT NULL,
	branch_ids _varchar NULL,
	discount_name varchar NOT NULL,
	discount_code varchar NULL,
	uses_per_customer int4 NOT NULL,
	applicable_customer varchar NULL,
	applicable_category _varchar NULL,
	applicable_items _varchar NULL,
	discount_type varchar NOT NULL,
	discount_value numeric NOT NULL,
	discount_date json NULL,
	discount_duration_day jsonb NULL,
	status bool DEFAULT false NOT NULL,
	created_by varchar NULL,
	fk_restaurant_id varchar NULL,
	usage_method varchar NULL,
	max_amount numeric NULL,
	actual_usage int4 DEFAULT 0 NOT NULL,
	total_uses int4 NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	min_amount numeric NULL,
	CONSTRAINT discounts_pk PRIMARY KEY (discount_id)
);


CREATE TABLE public."Items" (
	item_id uuid DEFAULT gen_random_uuid() NOT NULL,
	fk_branch_id varchar NULL,
	item_name varchar NOT NULL,
	item_description text NULL,
	item_type varchar DEFAULT 'veg'::character varying NOT NULL,
	fk_category_id uuid NULL,
	item_price numeric NOT NULL,
	item_image_link varchar NULL,
	item_status varchar NOT NULL,
	item_variants jsonb NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	item_add_ons_group jsonb NULL,
	item_combos jsonb NULL,
	CONSTRAINT items_pk PRIMARY KEY (item_id)
);



-- public."Restaurants" definition

-- Drop table

-- DROP TABLE public."Restaurants";

CREATE TABLE public."Restaurants" (
	restaurant_id varchar NOT NULL,
	restaurant_name varchar NOT NULL,
	restaurant_logo_url varchar NULL,
	restaurant_bg_img_url varchar NULL,
	restaurant_social_links jsonb NULL,
	restaurant_cancellation_number varchar NULL,
	restaurant_timings json NULL,
	restaurant_whatsapp_id varchar NULL,
	restaurant_whatsapp_token varchar NULL,
	restaurant_whatsapp_app_id varchar NULL,
	restaurant_business_account_id varchar NULL,
	take_orders bool DEFAULT false NOT NULL,
	phone_number varchar NOT NULL,
	order_policy _text NULL,
	payment_methods jsonb NULL,
	stripe_subscription_id varchar NULL,
	stripe_customer_id varchar NULL,
	subscription_status bool DEFAULT true NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	order_link varchar NULL,
	inbox_access_enabled bool DEFAULT false NULL,
	enable_support bool DEFAULT true NOT NULL,
	wa_address_form_id varchar NULL,
	payment_webhook_secret varchar NULL,
	restaurant_auto_accept bool DEFAULT false NULL,
	restaurant_menu_type varchar DEFAULT 'non-veg'::character varying NULL,
	restaurant_emails _varchar NULL,
	restaurant_email_password varchar NULL,
	restaurant_sender_email varchar NULL,
	subscription_graceperiod int4 DEFAULT 15 NOT NULL,
	restaurant_auto_out_for_delivery bool DEFAULT false NULL,
	customer_review_link varchar NULL,
	is_only_for_listing bool DEFAULT false NULL,
	CONSTRAINT restaurants_pk PRIMARY KEY (restaurant_id)
);


-- public."ShopAddOns" definition

-- Drop table

-- DROP TABLE public."ShopAddOns";

CREATE TABLE public."ShopAddOns" (
	add_on_id uuid DEFAULT gen_random_uuid() NOT NULL,
	add_on_name varchar NOT NULL,
	add_on_type varchar NOT NULL,
	add_on_price numeric NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	fk_branch_id varchar NULL,
	modified_at timestamp NULL,
	CONSTRAINT shop_add_ons_pk PRIMARY KEY (add_on_id)
);


-- public."ShopBranches" definition

-- Drop table

-- DROP TABLE public."ShopBranches";

CREATE TABLE public."ShopBranches" (
	fk_shop_id varchar NOT NULL,
	branch_id varchar NOT NULL,
	branch_name varchar NOT NULL,
	branch_address varchar NOT NULL,
	phone_number varchar NOT NULL,
	branch_timings jsonb NULL,
	status bool DEFAULT false NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	branch_logo varchar NULL,
	branch_description text NULL,
	branch_maps_url varchar NULL,
	branch_category_order _varchar NULL,
	branch_promotional_banner _varchar NULL,
	branch_min_cart_amount numeric NULL,
	branch_location jsonb NULL,
	inbox_enabled bool DEFAULT false NULL,
	branch_payment_modes _varchar DEFAULT '{cash,card,online}'::character varying[] NULL,
	payment_methods jsonb NULL,
	payment_webhook_secret varchar NULL,
	auto_refundable bool DEFAULT true NULL,
	trn_number int8 NULL,
	branch_emirate varchar NULL,
	average_spend float8 NULL,
	branch_tags _varchar NULL,
	branch_display_name varchar NULL,
	branch_timezone varchar DEFAULT 'Asia/Dubai'::character varying NULL,
	cancellation_number varchar DEFAULT ''::character varying NULL,
	break_timings jsonb NULL,
	break_status_switch bool DEFAULT true NULL,
	break_override_until timestamp NULL,
	CONSTRAINT shop_branches_pk PRIMARY KEY (branch_id)
);


-- public."ShopCategories" definition

-- Drop table

-- DROP TABLE public."ShopCategories";

CREATE TABLE public."ShopCategories" (
	category_id uuid DEFAULT gen_random_uuid() NOT NULL,
	category_name varchar NOT NULL,
	category_description text NULL,
	category_availability varchar NOT NULL,
	category_availability_timings jsonb NULL,
	status bool DEFAULT false NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	fk_branch_id varchar NULL,
	CONSTRAINT shop_category_pk PRIMARY KEY (category_id)
);


-- public."ShopDeliveryZones" definition

-- Drop table

-- DROP TABLE public."ShopDeliveryZones";

CREATE TABLE public."ShopDeliveryZones" (
	zone_id uuid DEFAULT gen_random_uuid() NOT NULL,
	fk_branch_id varchar NULL,
	coordinates jsonb NULL,
	zone_name varchar NULL,
	delivery_fee numeric NULL,
	status bool DEFAULT true NULL,
	fk_shop_id varchar NULL,
	min_cart_amount numeric NULL,
	CONSTRAINT shop_deliveryzones_pk PRIMARY KEY (zone_id)
);


-- public."ShopDiscounts" definition

-- Drop table

-- DROP TABLE public."ShopDiscounts";

CREATE TABLE public."ShopDiscounts" (
	discount_id uuid DEFAULT gen_random_uuid() NOT NULL,
	branch_ids _varchar NULL,
	discount_name varchar NOT NULL,
	discount_code varchar NULL,
	uses_per_customer int4 NOT NULL,
	applicable_customer varchar NULL,
	applicable_category _varchar NULL,
	applicable_items _varchar NULL,
	discount_type varchar NOT NULL,
	discount_value numeric NOT NULL,
	discount_date json NULL,
	discount_duration_day jsonb NULL,
	status bool DEFAULT false NOT NULL,
	created_by varchar NULL,
	fk_shop_id varchar NULL,
	usage_method varchar NULL,
	max_amount numeric NULL,
	actual_usage int4 DEFAULT 0 NOT NULL,
	total_uses int4 NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	min_amount numeric NULL,
	CONSTRAINT shop_discounts_pk PRIMARY KEY (discount_id)
);


-- public."ShopItems" definition

-- Drop table

-- DROP TABLE public."ShopItems";

CREATE TABLE public."ShopItems" (
	item_id uuid DEFAULT gen_random_uuid() NOT NULL,
	fk_branch_id varchar NULL,
	item_name varchar NOT NULL,
	item_description text NULL,
	item_type varchar NOT NULL,
	fk_category_id uuid NULL,
	item_price numeric NOT NULL,
	item_image_links _varchar NULL,
	item_status varchar NOT NULL,
	item_variants jsonb NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	item_add_ons_group jsonb NULL,
	item_quantity int4 DEFAULT 0 NULL,
	item_combos jsonb NULL,
	CONSTRAINT shop_items_pk PRIMARY KEY (item_id)
);

CREATE TABLE public."Shops" (
	shop_id varchar NOT NULL,
	shop_name varchar NOT NULL,
	shop_logo_url varchar NULL,
	shop_bg_img_url varchar NULL,
	shop_social_links jsonb NULL,
	shop_cancellation_number varchar NULL,
	shop_timings json NULL,
	shop_whatsapp_id varchar NULL,
	shop_whatsapp_token varchar NULL,
	shop_whatsapp_app_id varchar NULL,
	shop_business_account_id varchar NULL,
	take_orders bool DEFAULT false NOT NULL,
	phone_number varchar NOT NULL,
	order_policy _text NULL,
	payment_methods jsonb NULL,
	stripe_subscription_id varchar NULL,
	stripe_customer_id varchar NULL,
	subscription_status bool DEFAULT true NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	order_link varchar NULL,
	inbox_access_enabled bool DEFAULT false NULL,
	enable_support bool DEFAULT true NOT NULL,
	wa_address_form_id varchar NULL,
	payment_webhook_secret varchar NULL,
	shop_auto_accept bool DEFAULT true NULL,
	shop_emails jsonb NULL,
	shop_sender_email varchar NULL,
	shop_email_password varchar NULL,
	generate_quotation bool DEFAULT false NULL,
	subscription_graceperiod int4 DEFAULT 15 NOT NULL,
	is_only_for_listing bool DEFAULT false NULL,
	CONSTRAINT shops_pk PRIMARY KEY (shop_id)
);



-- public."Discounts" definition

-- Drop table

-- DROP TABLE public."Discounts";

CREATE TABLE public."Discounts" (
	discount_id uuid DEFAULT gen_random_uuid() NOT NULL,
	fk_club_id varchar NOT NULL,
	discount_name varchar NOT NULL,
	discount_code varchar NOT NULL,
	uses_per_customer int4 NOT NULL,
	method_usage varchar NULL,
	applicable_customer varchar NULL,
	discount_type varchar NULL,
	discount_value numeric NULL,
	min_amount numeric NULL,
	max_amount numeric NULL,
	discount_date jsonb NULL,
	discount_duration_day jsonb NULL,
	total_uses int4 NOT NULL,
	actual_usage int4 DEFAULT 0 NOT NULL,
	status bool DEFAULT true NOT NULL,
	facility_ids _varchar NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT "Discounts_pkey" PRIMARY KEY (discount_id),
	CONSTRAINT "Discounts_fk_club_id_fkey" FOREIGN KEY (fk_club_id) REFERENCES public."Clubs"(club_id)
);

CREATE TABLE public."Facilities" (
	fk_club_id varchar NOT NULL,
	facility_id varchar NOT NULL,
	facility_name varchar NOT NULL,
	facility_category varchar NOT NULL,
	facility_type varchar NOT NULL,
	time_slot_duration varchar NOT NULL,
	court_types _text NOT NULL,
	pricing jsonb NULL,
	facility_timing jsonb NULL,
	status bool DEFAULT false NOT NULL,
	pricing_option varchar NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	break_timing jsonb NULL,
	partial_payment_status bool DEFAULT false NOT NULL,
	banner_images _varchar NULL,
	pay_at_venue_status bool DEFAULT false NOT NULL,
	facility_timezone varchar DEFAULT 'Asia/Dubai'::character varying NULL,
	facility_min_duration int4 DEFAULT 60 NOT NULL,
	facility_group_id varchar NULL,
	facility_banner_link varchar NULL,
	CONSTRAINT "Facilities_pkey" PRIMARY KEY (facility_id),
	CONSTRAINT "Facilities_fk_club_id_fkey" FOREIGN KEY (fk_club_id) REFERENCES public."Clubs"(club_id)
);



CREATE TABLE public."Courts" (
	court_id uuid DEFAULT gen_random_uuid() NOT NULL,
	fk_facility_id varchar NOT NULL,
	court_name varchar NOT NULL,
	status bool DEFAULT true NOT NULL,
	created_at varchar DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT "Courts_pkey" PRIMARY KEY (court_id),
	CONSTRAINT "Courts_fk_facility_id_fkey" FOREIGN KEY (fk_facility_id) REFERENCES public."Facilities"(facility_id) ON DELETE CASCADE
);


-- public."Customer_Discounts" definition

-- Drop table

-- DROP TABLE public."Customer_Discounts";

CREATE TABLE public."Customer_Discounts" (
	fk_customer_id uuid NULL,
	fk_discount_id uuid NULL,
	no_of_usage int4 DEFAULT 1 NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT unique_customer_discount UNIQUE (fk_customer_id, fk_discount_id),
	CONSTRAINT "Customer_Discounts_fk_customer_id_fkey" FOREIGN KEY (fk_customer_id) REFERENCES public."Customers"(customer_id),
	CONSTRAINT "Customer_Discounts_fk_discount_id_fkey" FOREIGN KEY (fk_discount_id) REFERENCES public."Discounts"(discount_id)
);


-- public."Slots" definition

-- Drop table

-- DROP TABLE public."Slots";

CREATE TABLE public."Slots" (
	slot_id uuid DEFAULT gen_random_uuid() NOT NULL,
	fk_booking_id uuid NOT NULL,
	slot_date varchar NOT NULL,
	slot_time varchar NOT NULL,
	court varchar NOT NULL,
	slot_price numeric NOT NULL,
	fk_facility_id varchar NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	match_id uuid NULL,
	CONSTRAINT "Slots_pkey" PRIMARY KEY (slot_id),
	CONSTRAINT slots_unique UNIQUE (slot_date, slot_time, court, fk_facility_id),
	CONSTRAINT "Slots_fk_booking_id_fkey" FOREIGN KEY (fk_booking_id) REFERENCES public."Bookings"(booking_id) ON DELETE CASCADE,
	CONSTRAINT slots_fk_facilities_id_fkey FOREIGN KEY (fk_facility_id) REFERENCES public."Facilities"(facility_id)
);
CREATE UNIQUE INDEX unique_slots ON public."Slots" USING btree (slot_date, slot_time, court, fk_facility_id);