"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var index_exports = {};
__export(index_exports, {
  AccessDeniedException: () => AccessDeniedException,
  AgreementStatus: () => AgreementStatus,
  ApplicationType: () => ApplicationType,
  AttributeType: () => AttributeType,
  AuthorizationStatus: () => AuthorizationStatus,
  AutomatedEvaluationConfigFilterSensitiveLog: () => AutomatedEvaluationConfigFilterSensitiveLog,
  AutomatedEvaluationCustomMetricConfigFilterSensitiveLog: () => AutomatedEvaluationCustomMetricConfigFilterSensitiveLog,
  AutomatedEvaluationCustomMetricSource: () => AutomatedEvaluationCustomMetricSource,
  AutomatedEvaluationCustomMetricSourceFilterSensitiveLog: () => AutomatedEvaluationCustomMetricSourceFilterSensitiveLog,
  BatchDeleteEvaluationJobCommand: () => BatchDeleteEvaluationJobCommand,
  BatchDeleteEvaluationJobErrorFilterSensitiveLog: () => BatchDeleteEvaluationJobErrorFilterSensitiveLog,
  BatchDeleteEvaluationJobItemFilterSensitiveLog: () => BatchDeleteEvaluationJobItemFilterSensitiveLog,
  BatchDeleteEvaluationJobRequestFilterSensitiveLog: () => BatchDeleteEvaluationJobRequestFilterSensitiveLog,
  BatchDeleteEvaluationJobResponseFilterSensitiveLog: () => BatchDeleteEvaluationJobResponseFilterSensitiveLog,
  Bedrock: () => Bedrock,
  BedrockClient: () => BedrockClient,
  BedrockServiceException: () => BedrockServiceException,
  ByteContentDocFilterSensitiveLog: () => ByteContentDocFilterSensitiveLog,
  CommitmentDuration: () => CommitmentDuration,
  ConflictException: () => ConflictException,
  CreateCustomModelCommand: () => CreateCustomModelCommand,
  CreateEvaluationJobCommand: () => CreateEvaluationJobCommand,
  CreateEvaluationJobRequestFilterSensitiveLog: () => CreateEvaluationJobRequestFilterSensitiveLog,
  CreateFoundationModelAgreementCommand: () => CreateFoundationModelAgreementCommand,
  CreateGuardrailCommand: () => CreateGuardrailCommand,
  CreateGuardrailRequestFilterSensitiveLog: () => CreateGuardrailRequestFilterSensitiveLog,
  CreateGuardrailVersionCommand: () => CreateGuardrailVersionCommand,
  CreateGuardrailVersionRequestFilterSensitiveLog: () => CreateGuardrailVersionRequestFilterSensitiveLog,
  CreateInferenceProfileCommand: () => CreateInferenceProfileCommand,
  CreateInferenceProfileRequestFilterSensitiveLog: () => CreateInferenceProfileRequestFilterSensitiveLog,
  CreateMarketplaceModelEndpointCommand: () => CreateMarketplaceModelEndpointCommand,
  CreateModelCopyJobCommand: () => CreateModelCopyJobCommand,
  CreateModelCustomizationJobCommand: () => CreateModelCustomizationJobCommand,
  CreateModelCustomizationJobRequestFilterSensitiveLog: () => CreateModelCustomizationJobRequestFilterSensitiveLog,
  CreateModelImportJobCommand: () => CreateModelImportJobCommand,
  CreateModelInvocationJobCommand: () => CreateModelInvocationJobCommand,
  CreatePromptRouterCommand: () => CreatePromptRouterCommand,
  CreatePromptRouterRequestFilterSensitiveLog: () => CreatePromptRouterRequestFilterSensitiveLog,
  CreateProvisionedModelThroughputCommand: () => CreateProvisionedModelThroughputCommand,
  CustomMetricDefinitionFilterSensitiveLog: () => CustomMetricDefinitionFilterSensitiveLog,
  CustomizationConfig: () => CustomizationConfig,
  CustomizationType: () => CustomizationType,
  DeleteCustomModelCommand: () => DeleteCustomModelCommand,
  DeleteFoundationModelAgreementCommand: () => DeleteFoundationModelAgreementCommand,
  DeleteGuardrailCommand: () => DeleteGuardrailCommand,
  DeleteImportedModelCommand: () => DeleteImportedModelCommand,
  DeleteInferenceProfileCommand: () => DeleteInferenceProfileCommand,
  DeleteMarketplaceModelEndpointCommand: () => DeleteMarketplaceModelEndpointCommand,
  DeleteModelInvocationLoggingConfigurationCommand: () => DeleteModelInvocationLoggingConfigurationCommand,
  DeletePromptRouterCommand: () => DeletePromptRouterCommand,
  DeleteProvisionedModelThroughputCommand: () => DeleteProvisionedModelThroughputCommand,
  DeregisterMarketplaceModelEndpointCommand: () => DeregisterMarketplaceModelEndpointCommand,
  EndpointConfig: () => EndpointConfig,
  EntitlementAvailability: () => EntitlementAvailability,
  EvaluationBedrockModelFilterSensitiveLog: () => EvaluationBedrockModelFilterSensitiveLog,
  EvaluationConfig: () => EvaluationConfig,
  EvaluationConfigFilterSensitiveLog: () => EvaluationConfigFilterSensitiveLog,
  EvaluationDatasetFilterSensitiveLog: () => EvaluationDatasetFilterSensitiveLog,
  EvaluationDatasetLocation: () => EvaluationDatasetLocation,
  EvaluationDatasetMetricConfigFilterSensitiveLog: () => EvaluationDatasetMetricConfigFilterSensitiveLog,
  EvaluationInferenceConfig: () => EvaluationInferenceConfig,
  EvaluationInferenceConfigFilterSensitiveLog: () => EvaluationInferenceConfigFilterSensitiveLog,
  EvaluationJobStatus: () => EvaluationJobStatus,
  EvaluationJobType: () => EvaluationJobType,
  EvaluationModelConfig: () => EvaluationModelConfig,
  EvaluationModelConfigFilterSensitiveLog: () => EvaluationModelConfigFilterSensitiveLog,
  EvaluationPrecomputedRagSourceConfig: () => EvaluationPrecomputedRagSourceConfig,
  EvaluationTaskType: () => EvaluationTaskType,
  EvaluatorModelConfig: () => EvaluatorModelConfig,
  ExternalSourceFilterSensitiveLog: () => ExternalSourceFilterSensitiveLog,
  ExternalSourceType: () => ExternalSourceType,
  ExternalSourcesGenerationConfigurationFilterSensitiveLog: () => ExternalSourcesGenerationConfigurationFilterSensitiveLog,
  ExternalSourcesRetrieveAndGenerateConfigurationFilterSensitiveLog: () => ExternalSourcesRetrieveAndGenerateConfigurationFilterSensitiveLog,
  FineTuningJobStatus: () => FineTuningJobStatus,
  FoundationModelLifecycleStatus: () => FoundationModelLifecycleStatus,
  GenerationConfigurationFilterSensitiveLog: () => GenerationConfigurationFilterSensitiveLog,
  GetCustomModelCommand: () => GetCustomModelCommand,
  GetCustomModelResponseFilterSensitiveLog: () => GetCustomModelResponseFilterSensitiveLog,
  GetEvaluationJobCommand: () => GetEvaluationJobCommand,
  GetEvaluationJobRequestFilterSensitiveLog: () => GetEvaluationJobRequestFilterSensitiveLog,
  GetEvaluationJobResponseFilterSensitiveLog: () => GetEvaluationJobResponseFilterSensitiveLog,
  GetFoundationModelAvailabilityCommand: () => GetFoundationModelAvailabilityCommand,
  GetFoundationModelCommand: () => GetFoundationModelCommand,
  GetGuardrailCommand: () => GetGuardrailCommand,
  GetGuardrailResponseFilterSensitiveLog: () => GetGuardrailResponseFilterSensitiveLog,
  GetImportedModelCommand: () => GetImportedModelCommand,
  GetInferenceProfileCommand: () => GetInferenceProfileCommand,
  GetInferenceProfileResponseFilterSensitiveLog: () => GetInferenceProfileResponseFilterSensitiveLog,
  GetMarketplaceModelEndpointCommand: () => GetMarketplaceModelEndpointCommand,
  GetModelCopyJobCommand: () => GetModelCopyJobCommand,
  GetModelCustomizationJobCommand: () => GetModelCustomizationJobCommand,
  GetModelCustomizationJobResponseFilterSensitiveLog: () => GetModelCustomizationJobResponseFilterSensitiveLog,
  GetModelImportJobCommand: () => GetModelImportJobCommand,
  GetModelInvocationJobCommand: () => GetModelInvocationJobCommand,
  GetModelInvocationJobResponseFilterSensitiveLog: () => GetModelInvocationJobResponseFilterSensitiveLog,
  GetModelInvocationLoggingConfigurationCommand: () => GetModelInvocationLoggingConfigurationCommand,
  GetPromptRouterCommand: () => GetPromptRouterCommand,
  GetPromptRouterResponseFilterSensitiveLog: () => GetPromptRouterResponseFilterSensitiveLog,
  GetProvisionedModelThroughputCommand: () => GetProvisionedModelThroughputCommand,
  GetUseCaseForModelAccessCommand: () => GetUseCaseForModelAccessCommand,
  GuardrailContentFilterAction: () => GuardrailContentFilterAction,
  GuardrailContentFilterConfigFilterSensitiveLog: () => GuardrailContentFilterConfigFilterSensitiveLog,
  GuardrailContentFilterFilterSensitiveLog: () => GuardrailContentFilterFilterSensitiveLog,
  GuardrailContentFilterType: () => GuardrailContentFilterType,
  GuardrailContentFiltersTierConfigFilterSensitiveLog: () => GuardrailContentFiltersTierConfigFilterSensitiveLog,
  GuardrailContentFiltersTierFilterSensitiveLog: () => GuardrailContentFiltersTierFilterSensitiveLog,
  GuardrailContentFiltersTierName: () => GuardrailContentFiltersTierName,
  GuardrailContentPolicyConfigFilterSensitiveLog: () => GuardrailContentPolicyConfigFilterSensitiveLog,
  GuardrailContentPolicyFilterSensitiveLog: () => GuardrailContentPolicyFilterSensitiveLog,
  GuardrailContextualGroundingAction: () => GuardrailContextualGroundingAction,
  GuardrailContextualGroundingFilterConfigFilterSensitiveLog: () => GuardrailContextualGroundingFilterConfigFilterSensitiveLog,
  GuardrailContextualGroundingFilterFilterSensitiveLog: () => GuardrailContextualGroundingFilterFilterSensitiveLog,
  GuardrailContextualGroundingFilterType: () => GuardrailContextualGroundingFilterType,
  GuardrailContextualGroundingPolicyConfigFilterSensitiveLog: () => GuardrailContextualGroundingPolicyConfigFilterSensitiveLog,
  GuardrailContextualGroundingPolicyFilterSensitiveLog: () => GuardrailContextualGroundingPolicyFilterSensitiveLog,
  GuardrailFilterStrength: () => GuardrailFilterStrength,
  GuardrailManagedWordsConfigFilterSensitiveLog: () => GuardrailManagedWordsConfigFilterSensitiveLog,
  GuardrailManagedWordsFilterSensitiveLog: () => GuardrailManagedWordsFilterSensitiveLog,
  GuardrailManagedWordsType: () => GuardrailManagedWordsType,
  GuardrailModality: () => GuardrailModality,
  GuardrailPiiEntityType: () => GuardrailPiiEntityType,
  GuardrailSensitiveInformationAction: () => GuardrailSensitiveInformationAction,
  GuardrailStatus: () => GuardrailStatus,
  GuardrailSummaryFilterSensitiveLog: () => GuardrailSummaryFilterSensitiveLog,
  GuardrailTopicAction: () => GuardrailTopicAction,
  GuardrailTopicConfigFilterSensitiveLog: () => GuardrailTopicConfigFilterSensitiveLog,
  GuardrailTopicFilterSensitiveLog: () => GuardrailTopicFilterSensitiveLog,
  GuardrailTopicPolicyConfigFilterSensitiveLog: () => GuardrailTopicPolicyConfigFilterSensitiveLog,
  GuardrailTopicPolicyFilterSensitiveLog: () => GuardrailTopicPolicyFilterSensitiveLog,
  GuardrailTopicType: () => GuardrailTopicType,
  GuardrailTopicsTierConfigFilterSensitiveLog: () => GuardrailTopicsTierConfigFilterSensitiveLog,
  GuardrailTopicsTierFilterSensitiveLog: () => GuardrailTopicsTierFilterSensitiveLog,
  GuardrailTopicsTierName: () => GuardrailTopicsTierName,
  GuardrailWordAction: () => GuardrailWordAction,
  GuardrailWordConfigFilterSensitiveLog: () => GuardrailWordConfigFilterSensitiveLog,
  GuardrailWordFilterSensitiveLog: () => GuardrailWordFilterSensitiveLog,
  GuardrailWordPolicyConfigFilterSensitiveLog: () => GuardrailWordPolicyConfigFilterSensitiveLog,
  GuardrailWordPolicyFilterSensitiveLog: () => GuardrailWordPolicyFilterSensitiveLog,
  HumanEvaluationConfigFilterSensitiveLog: () => HumanEvaluationConfigFilterSensitiveLog,
  HumanEvaluationCustomMetricFilterSensitiveLog: () => HumanEvaluationCustomMetricFilterSensitiveLog,
  HumanWorkflowConfigFilterSensitiveLog: () => HumanWorkflowConfigFilterSensitiveLog,
  ImplicitFilterConfigurationFilterSensitiveLog: () => ImplicitFilterConfigurationFilterSensitiveLog,
  InferenceProfileModelSource: () => InferenceProfileModelSource,
  InferenceProfileStatus: () => InferenceProfileStatus,
  InferenceProfileSummaryFilterSensitiveLog: () => InferenceProfileSummaryFilterSensitiveLog,
  InferenceProfileType: () => InferenceProfileType,
  InferenceType: () => InferenceType,
  InternalServerException: () => InternalServerException,
  InvocationLogSource: () => InvocationLogSource,
  InvocationLogsConfigFilterSensitiveLog: () => InvocationLogsConfigFilterSensitiveLog,
  JobStatusDetails: () => JobStatusDetails,
  KnowledgeBaseConfig: () => KnowledgeBaseConfig,
  KnowledgeBaseConfigFilterSensitiveLog: () => KnowledgeBaseConfigFilterSensitiveLog,
  KnowledgeBaseRetrievalConfigurationFilterSensitiveLog: () => KnowledgeBaseRetrievalConfigurationFilterSensitiveLog,
  KnowledgeBaseRetrieveAndGenerateConfigurationFilterSensitiveLog: () => KnowledgeBaseRetrieveAndGenerateConfigurationFilterSensitiveLog,
  KnowledgeBaseVectorSearchConfigurationFilterSensitiveLog: () => KnowledgeBaseVectorSearchConfigurationFilterSensitiveLog,
  ListCustomModelsCommand: () => ListCustomModelsCommand,
  ListEvaluationJobsCommand: () => ListEvaluationJobsCommand,
  ListFoundationModelAgreementOffersCommand: () => ListFoundationModelAgreementOffersCommand,
  ListFoundationModelsCommand: () => ListFoundationModelsCommand,
  ListGuardrailsCommand: () => ListGuardrailsCommand,
  ListGuardrailsResponseFilterSensitiveLog: () => ListGuardrailsResponseFilterSensitiveLog,
  ListImportedModelsCommand: () => ListImportedModelsCommand,
  ListInferenceProfilesCommand: () => ListInferenceProfilesCommand,
  ListInferenceProfilesResponseFilterSensitiveLog: () => ListInferenceProfilesResponseFilterSensitiveLog,
  ListMarketplaceModelEndpointsCommand: () => ListMarketplaceModelEndpointsCommand,
  ListModelCopyJobsCommand: () => ListModelCopyJobsCommand,
  ListModelCustomizationJobsCommand: () => ListModelCustomizationJobsCommand,
  ListModelImportJobsCommand: () => ListModelImportJobsCommand,
  ListModelInvocationJobsCommand: () => ListModelInvocationJobsCommand,
  ListModelInvocationJobsResponseFilterSensitiveLog: () => ListModelInvocationJobsResponseFilterSensitiveLog,
  ListPromptRoutersCommand: () => ListPromptRoutersCommand,
  ListPromptRoutersResponseFilterSensitiveLog: () => ListPromptRoutersResponseFilterSensitiveLog,
  ListProvisionedModelThroughputsCommand: () => ListProvisionedModelThroughputsCommand,
  ListTagsForResourceCommand: () => ListTagsForResourceCommand,
  MetadataAttributeSchemaFilterSensitiveLog: () => MetadataAttributeSchemaFilterSensitiveLog,
  MetadataConfigurationForRerankingFilterSensitiveLog: () => MetadataConfigurationForRerankingFilterSensitiveLog,
  ModelCopyJobStatus: () => ModelCopyJobStatus,
  ModelCustomization: () => ModelCustomization,
  ModelCustomizationJobStatus: () => ModelCustomizationJobStatus,
  ModelDataSource: () => ModelDataSource,
  ModelImportJobStatus: () => ModelImportJobStatus,
  ModelInvocationJobInputDataConfig: () => ModelInvocationJobInputDataConfig,
  ModelInvocationJobOutputDataConfig: () => ModelInvocationJobOutputDataConfig,
  ModelInvocationJobStatus: () => ModelInvocationJobStatus,
  ModelInvocationJobSummaryFilterSensitiveLog: () => ModelInvocationJobSummaryFilterSensitiveLog,
  ModelModality: () => ModelModality,
  ModelStatus: () => ModelStatus,
  OfferType: () => OfferType,
  PerformanceConfigLatency: () => PerformanceConfigLatency,
  PromptRouterStatus: () => PromptRouterStatus,
  PromptRouterSummaryFilterSensitiveLog: () => PromptRouterSummaryFilterSensitiveLog,
  PromptRouterType: () => PromptRouterType,
  PromptTemplateFilterSensitiveLog: () => PromptTemplateFilterSensitiveLog,
  ProvisionedModelStatus: () => ProvisionedModelStatus,
  PutModelInvocationLoggingConfigurationCommand: () => PutModelInvocationLoggingConfigurationCommand,
  PutUseCaseForModelAccessCommand: () => PutUseCaseForModelAccessCommand,
  QueryTransformationType: () => QueryTransformationType,
  RAGConfig: () => RAGConfig,
  RAGConfigFilterSensitiveLog: () => RAGConfigFilterSensitiveLog,
  RatingScaleItemValue: () => RatingScaleItemValue,
  RegionAvailability: () => RegionAvailability,
  RegisterMarketplaceModelEndpointCommand: () => RegisterMarketplaceModelEndpointCommand,
  RequestMetadataBaseFiltersFilterSensitiveLog: () => RequestMetadataBaseFiltersFilterSensitiveLog,
  RequestMetadataFilters: () => RequestMetadataFilters,
  RequestMetadataFiltersFilterSensitiveLog: () => RequestMetadataFiltersFilterSensitiveLog,
  RerankingMetadataSelectionMode: () => RerankingMetadataSelectionMode,
  RerankingMetadataSelectiveModeConfiguration: () => RerankingMetadataSelectiveModeConfiguration,
  RerankingMetadataSelectiveModeConfigurationFilterSensitiveLog: () => RerankingMetadataSelectiveModeConfigurationFilterSensitiveLog,
  ResourceNotFoundException: () => ResourceNotFoundException,
  RetrievalFilter: () => RetrievalFilter,
  RetrievalFilterFilterSensitiveLog: () => RetrievalFilterFilterSensitiveLog,
  RetrieveAndGenerateConfigurationFilterSensitiveLog: () => RetrieveAndGenerateConfigurationFilterSensitiveLog,
  RetrieveAndGenerateType: () => RetrieveAndGenerateType,
  RetrieveConfigFilterSensitiveLog: () => RetrieveConfigFilterSensitiveLog,
  S3InputFormat: () => S3InputFormat,
  SearchType: () => SearchType,
  ServiceQuotaExceededException: () => ServiceQuotaExceededException,
  ServiceUnavailableException: () => ServiceUnavailableException,
  SortByProvisionedModels: () => SortByProvisionedModels,
  SortJobsBy: () => SortJobsBy,
  SortModelsBy: () => SortModelsBy,
  SortOrder: () => SortOrder,
  Status: () => Status,
  StopEvaluationJobCommand: () => StopEvaluationJobCommand,
  StopEvaluationJobRequestFilterSensitiveLog: () => StopEvaluationJobRequestFilterSensitiveLog,
  StopModelCustomizationJobCommand: () => StopModelCustomizationJobCommand,
  StopModelInvocationJobCommand: () => StopModelInvocationJobCommand,
  TagResourceCommand: () => TagResourceCommand,
  ThrottlingException: () => ThrottlingException,
  TooManyTagsException: () => TooManyTagsException,
  TrainingDataConfigFilterSensitiveLog: () => TrainingDataConfigFilterSensitiveLog,
  UntagResourceCommand: () => UntagResourceCommand,
  UpdateGuardrailCommand: () => UpdateGuardrailCommand,
  UpdateGuardrailRequestFilterSensitiveLog: () => UpdateGuardrailRequestFilterSensitiveLog,
  UpdateMarketplaceModelEndpointCommand: () => UpdateMarketplaceModelEndpointCommand,
  UpdateProvisionedModelThroughputCommand: () => UpdateProvisionedModelThroughputCommand,
  ValidationException: () => ValidationException,
  VectorSearchBedrockRerankingConfigurationFilterSensitiveLog: () => VectorSearchBedrockRerankingConfigurationFilterSensitiveLog,
  VectorSearchRerankingConfigurationFilterSensitiveLog: () => VectorSearchRerankingConfigurationFilterSensitiveLog,
  VectorSearchRerankingConfigurationType: () => VectorSearchRerankingConfigurationType,
  __Client: () => import_smithy_client.Client,
  paginateListCustomModels: () => paginateListCustomModels,
  paginateListEvaluationJobs: () => paginateListEvaluationJobs,
  paginateListGuardrails: () => paginateListGuardrails,
  paginateListImportedModels: () => paginateListImportedModels,
  paginateListInferenceProfiles: () => paginateListInferenceProfiles,
  paginateListMarketplaceModelEndpoints: () => paginateListMarketplaceModelEndpoints,
  paginateListModelCopyJobs: () => paginateListModelCopyJobs,
  paginateListModelCustomizationJobs: () => paginateListModelCustomizationJobs,
  paginateListModelImportJobs: () => paginateListModelImportJobs,
  paginateListModelInvocationJobs: () => paginateListModelInvocationJobs,
  paginateListPromptRouters: () => paginateListPromptRouters,
  paginateListProvisionedModelThroughputs: () => paginateListProvisionedModelThroughputs
});
module.exports = __toCommonJS(index_exports);

// src/BedrockClient.ts
var import_middleware_host_header = require("@aws-sdk/middleware-host-header");
var import_middleware_logger = require("@aws-sdk/middleware-logger");
var import_middleware_recursion_detection = require("@aws-sdk/middleware-recursion-detection");
var import_middleware_user_agent = require("@aws-sdk/middleware-user-agent");
var import_config_resolver = require("@smithy/config-resolver");
var import_core = require("@smithy/core");
var import_middleware_content_length = require("@smithy/middleware-content-length");
var import_middleware_endpoint = require("@smithy/middleware-endpoint");
var import_middleware_retry = require("@smithy/middleware-retry");

var import_httpAuthSchemeProvider = require("./auth/httpAuthSchemeProvider");

// src/endpoint/EndpointParameters.ts
var resolveClientEndpointParameters = /* @__PURE__ */ __name((options) => {
  return Object.assign(options, {
    useDualstackEndpoint: options.useDualstackEndpoint ?? false,
    useFipsEndpoint: options.useFipsEndpoint ?? false,
    defaultSigningName: "bedrock"
  });
}, "resolveClientEndpointParameters");
var commonParams = {
  UseFIPS: { type: "builtInParams", name: "useFipsEndpoint" },
  Endpoint: { type: "builtInParams", name: "endpoint" },
  Region: { type: "builtInParams", name: "region" },
  UseDualStack: { type: "builtInParams", name: "useDualstackEndpoint" }
};

// src/BedrockClient.ts
var import_runtimeConfig = require("././runtimeConfig");

// src/runtimeExtensions.ts
var import_region_config_resolver = require("@aws-sdk/region-config-resolver");
var import_protocol_http = require("@smithy/protocol-http");
var import_smithy_client = require("@smithy/smithy-client");

// src/auth/httpAuthExtensionConfiguration.ts
var getHttpAuthExtensionConfiguration = /* @__PURE__ */ __name((runtimeConfig) => {
  const _httpAuthSchemes = runtimeConfig.httpAuthSchemes;
  let _httpAuthSchemeProvider = runtimeConfig.httpAuthSchemeProvider;
  let _credentials = runtimeConfig.credentials;
  let _token = runtimeConfig.token;
  return {
    setHttpAuthScheme(httpAuthScheme) {
      const index = _httpAuthSchemes.findIndex((scheme) => scheme.schemeId === httpAuthScheme.schemeId);
      if (index === -1) {
        _httpAuthSchemes.push(httpAuthScheme);
      } else {
        _httpAuthSchemes.splice(index, 1, httpAuthScheme);
      }
    },
    httpAuthSchemes() {
      return _httpAuthSchemes;
    },
    setHttpAuthSchemeProvider(httpAuthSchemeProvider) {
      _httpAuthSchemeProvider = httpAuthSchemeProvider;
    },
    httpAuthSchemeProvider() {
      return _httpAuthSchemeProvider;
    },
    setCredentials(credentials) {
      _credentials = credentials;
    },
    credentials() {
      return _credentials;
    },
    setToken(token) {
      _token = token;
    },
    token() {
      return _token;
    }
  };
}, "getHttpAuthExtensionConfiguration");
var resolveHttpAuthRuntimeConfig = /* @__PURE__ */ __name((config) => {
  return {
    httpAuthSchemes: config.httpAuthSchemes(),
    httpAuthSchemeProvider: config.httpAuthSchemeProvider(),
    credentials: config.credentials(),
    token: config.token()
  };
}, "resolveHttpAuthRuntimeConfig");

// src/runtimeExtensions.ts
var resolveRuntimeExtensions = /* @__PURE__ */ __name((runtimeConfig, extensions) => {
  const extensionConfiguration = Object.assign(
    (0, import_region_config_resolver.getAwsRegionExtensionConfiguration)(runtimeConfig),
    (0, import_smithy_client.getDefaultExtensionConfiguration)(runtimeConfig),
    (0, import_protocol_http.getHttpHandlerExtensionConfiguration)(runtimeConfig),
    getHttpAuthExtensionConfiguration(runtimeConfig)
  );
  extensions.forEach((extension) => extension.configure(extensionConfiguration));
  return Object.assign(
    runtimeConfig,
    (0, import_region_config_resolver.resolveAwsRegionExtensionConfiguration)(extensionConfiguration),
    (0, import_smithy_client.resolveDefaultRuntimeConfig)(extensionConfiguration),
    (0, import_protocol_http.resolveHttpHandlerRuntimeConfig)(extensionConfiguration),
    resolveHttpAuthRuntimeConfig(extensionConfiguration)
  );
}, "resolveRuntimeExtensions");

// src/BedrockClient.ts
var BedrockClient = class extends import_smithy_client.Client {
  static {
    __name(this, "BedrockClient");
  }
  /**
   * The resolved configuration of BedrockClient class. This is resolved and normalized from the {@link BedrockClientConfig | constructor configuration interface}.
   */
  config;
  constructor(...[configuration]) {
    const _config_0 = (0, import_runtimeConfig.getRuntimeConfig)(configuration || {});
    super(_config_0);
    this.initConfig = _config_0;
    const _config_1 = resolveClientEndpointParameters(_config_0);
    const _config_2 = (0, import_middleware_user_agent.resolveUserAgentConfig)(_config_1);
    const _config_3 = (0, import_middleware_retry.resolveRetryConfig)(_config_2);
    const _config_4 = (0, import_config_resolver.resolveRegionConfig)(_config_3);
    const _config_5 = (0, import_middleware_host_header.resolveHostHeaderConfig)(_config_4);
    const _config_6 = (0, import_middleware_endpoint.resolveEndpointConfig)(_config_5);
    const _config_7 = (0, import_httpAuthSchemeProvider.resolveHttpAuthSchemeConfig)(_config_6);
    const _config_8 = resolveRuntimeExtensions(_config_7, configuration?.extensions || []);
    this.config = _config_8;
    this.middlewareStack.use((0, import_middleware_user_agent.getUserAgentPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_retry.getRetryPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_content_length.getContentLengthPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_host_header.getHostHeaderPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_logger.getLoggerPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_recursion_detection.getRecursionDetectionPlugin)(this.config));
    this.middlewareStack.use(
      (0, import_core.getHttpAuthSchemeEndpointRuleSetPlugin)(this.config, {
        httpAuthSchemeParametersProvider: import_httpAuthSchemeProvider.defaultBedrockHttpAuthSchemeParametersProvider,
        identityProviderConfigProvider: /* @__PURE__ */ __name(async (config) => new import_core.DefaultIdentityProviderConfig({
          "aws.auth#sigv4": config.credentials,
          "smithy.api#httpBearerAuth": config.token
        }), "identityProviderConfigProvider")
      })
    );
    this.middlewareStack.use((0, import_core.getHttpSigningPlugin)(this.config));
  }
  /**
   * Destroy underlying resources, like sockets. It's usually not necessary to do this.
   * However in Node.js, it's best to explicitly shut down the client's agent when it is no longer needed.
   * Otherwise, sockets might stay open for quite a long time before the server terminates them.
   */
  destroy() {
    super.destroy();
  }
};

// src/Bedrock.ts


// src/commands/BatchDeleteEvaluationJobCommand.ts

var import_middleware_serde = require("@smithy/middleware-serde");


// src/models/models_0.ts


// src/models/BedrockServiceException.ts

var BedrockServiceException = class _BedrockServiceException extends import_smithy_client.ServiceException {
  static {
    __name(this, "BedrockServiceException");
  }
  /**
   * @internal
   */
  constructor(options) {
    super(options);
    Object.setPrototypeOf(this, _BedrockServiceException.prototype);
  }
};

// src/models/models_0.ts
var AccessDeniedException = class _AccessDeniedException extends BedrockServiceException {
  static {
    __name(this, "AccessDeniedException");
  }
  name = "AccessDeniedException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "AccessDeniedException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _AccessDeniedException.prototype);
  }
};
var AgreementStatus = {
  AVAILABLE: "AVAILABLE",
  ERROR: "ERROR",
  NOT_AVAILABLE: "NOT_AVAILABLE",
  PENDING: "PENDING"
};
var InternalServerException = class _InternalServerException extends BedrockServiceException {
  static {
    __name(this, "InternalServerException");
  }
  name = "InternalServerException";
  $fault = "server";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InternalServerException",
      $fault: "server",
      ...opts
    });
    Object.setPrototypeOf(this, _InternalServerException.prototype);
  }
};
var ResourceNotFoundException = class _ResourceNotFoundException extends BedrockServiceException {
  static {
    __name(this, "ResourceNotFoundException");
  }
  name = "ResourceNotFoundException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ResourceNotFoundException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ResourceNotFoundException.prototype);
  }
};
var ThrottlingException = class _ThrottlingException extends BedrockServiceException {
  static {
    __name(this, "ThrottlingException");
  }
  name = "ThrottlingException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ThrottlingException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ThrottlingException.prototype);
  }
};
var ValidationException = class _ValidationException extends BedrockServiceException {
  static {
    __name(this, "ValidationException");
  }
  name = "ValidationException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ValidationException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ValidationException.prototype);
  }
};
var ConflictException = class _ConflictException extends BedrockServiceException {
  static {
    __name(this, "ConflictException");
  }
  name = "ConflictException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ConflictException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ConflictException.prototype);
  }
};
var EndpointConfig;
((EndpointConfig3) => {
  EndpointConfig3.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.sageMaker !== void 0) return visitor.sageMaker(value.sageMaker);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(EndpointConfig || (EndpointConfig = {}));
var Status = {
  INCOMPATIBLE_ENDPOINT: "INCOMPATIBLE_ENDPOINT",
  REGISTERED: "REGISTERED"
};
var ServiceQuotaExceededException = class _ServiceQuotaExceededException extends BedrockServiceException {
  static {
    __name(this, "ServiceQuotaExceededException");
  }
  name = "ServiceQuotaExceededException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ServiceQuotaExceededException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ServiceQuotaExceededException.prototype);
  }
};
var ServiceUnavailableException = class _ServiceUnavailableException extends BedrockServiceException {
  static {
    __name(this, "ServiceUnavailableException");
  }
  name = "ServiceUnavailableException";
  $fault = "server";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ServiceUnavailableException",
      $fault: "server",
      ...opts
    });
    Object.setPrototypeOf(this, _ServiceUnavailableException.prototype);
  }
};
var ModelDataSource;
((ModelDataSource3) => {
  ModelDataSource3.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.s3DataSource !== void 0) return visitor.s3DataSource(value.s3DataSource);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(ModelDataSource || (ModelDataSource = {}));
var TooManyTagsException = class _TooManyTagsException extends BedrockServiceException {
  static {
    __name(this, "TooManyTagsException");
  }
  name = "TooManyTagsException";
  $fault = "client";
  /**
   * <p>The name of the resource with too many tags.</p>
   * @public
   */
  resourceName;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "TooManyTagsException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _TooManyTagsException.prototype);
    this.resourceName = opts.resourceName;
  }
};
var CustomizationConfig;
((CustomizationConfig4) => {
  CustomizationConfig4.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.distillationConfig !== void 0) return visitor.distillationConfig(value.distillationConfig);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(CustomizationConfig || (CustomizationConfig = {}));
var CustomizationType = {
  CONTINUED_PRE_TRAINING: "CONTINUED_PRE_TRAINING",
  DISTILLATION: "DISTILLATION",
  FINE_TUNING: "FINE_TUNING",
  IMPORTED: "IMPORTED"
};
var ModelStatus = {
  ACTIVE: "Active",
  CREATING: "Creating",
  FAILED: "Failed"
};
var InvocationLogSource;
((InvocationLogSource3) => {
  InvocationLogSource3.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.s3Uri !== void 0) return visitor.s3Uri(value.s3Uri);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(InvocationLogSource || (InvocationLogSource = {}));
var RequestMetadataFilters;
((RequestMetadataFilters3) => {
  RequestMetadataFilters3.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.equals !== void 0) return visitor.equals(value.equals);
    if (value.notEquals !== void 0) return visitor.notEquals(value.notEquals);
    if (value.andAll !== void 0) return visitor.andAll(value.andAll);
    if (value.orAll !== void 0) return visitor.orAll(value.orAll);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(RequestMetadataFilters || (RequestMetadataFilters = {}));
var SortModelsBy = {
  CREATION_TIME: "CreationTime"
};
var SortOrder = {
  ASCENDING: "Ascending",
  DESCENDING: "Descending"
};
var EvaluationJobStatus = {
  COMPLETED: "Completed",
  DELETING: "Deleting",
  FAILED: "Failed",
  IN_PROGRESS: "InProgress",
  STOPPED: "Stopped",
  STOPPING: "Stopping"
};
var ApplicationType = {
  MODEL_EVALUATION: "ModelEvaluation",
  RAG_EVALUATION: "RagEvaluation"
};
var RatingScaleItemValue;
((RatingScaleItemValue2) => {
  RatingScaleItemValue2.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.stringValue !== void 0) return visitor.stringValue(value.stringValue);
    if (value.floatValue !== void 0) return visitor.floatValue(value.floatValue);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(RatingScaleItemValue || (RatingScaleItemValue = {}));
var AutomatedEvaluationCustomMetricSource;
((AutomatedEvaluationCustomMetricSource2) => {
  AutomatedEvaluationCustomMetricSource2.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.customMetricDefinition !== void 0) return visitor.customMetricDefinition(value.customMetricDefinition);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(AutomatedEvaluationCustomMetricSource || (AutomatedEvaluationCustomMetricSource = {}));
var EvaluationDatasetLocation;
((EvaluationDatasetLocation3) => {
  EvaluationDatasetLocation3.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.s3Uri !== void 0) return visitor.s3Uri(value.s3Uri);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(EvaluationDatasetLocation || (EvaluationDatasetLocation = {}));
var EvaluationTaskType = {
  CLASSIFICATION: "Classification",
  CUSTOM: "Custom",
  GENERATION: "Generation",
  QUESTION_AND_ANSWER: "QuestionAndAnswer",
  SUMMARIZATION: "Summarization"
};
var EvaluatorModelConfig;
((EvaluatorModelConfig3) => {
  EvaluatorModelConfig3.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.bedrockEvaluatorModels !== void 0) return visitor.bedrockEvaluatorModels(value.bedrockEvaluatorModels);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(EvaluatorModelConfig || (EvaluatorModelConfig = {}));
var EvaluationConfig;
((EvaluationConfig3) => {
  EvaluationConfig3.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.automated !== void 0) return visitor.automated(value.automated);
    if (value.human !== void 0) return visitor.human(value.human);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(EvaluationConfig || (EvaluationConfig = {}));
var PerformanceConfigLatency = {
  OPTIMIZED: "optimized",
  STANDARD: "standard"
};
var EvaluationModelConfig;
((EvaluationModelConfig4) => {
  EvaluationModelConfig4.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.bedrockModel !== void 0) return visitor.bedrockModel(value.bedrockModel);
    if (value.precomputedInferenceSource !== void 0)
      return visitor.precomputedInferenceSource(value.precomputedInferenceSource);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(EvaluationModelConfig || (EvaluationModelConfig = {}));
var ExternalSourceType = {
  BYTE_CONTENT: "BYTE_CONTENT",
  S3: "S3"
};
var QueryTransformationType = {
  QUERY_DECOMPOSITION: "QUERY_DECOMPOSITION"
};
var AttributeType = {
  BOOLEAN: "BOOLEAN",
  NUMBER: "NUMBER",
  STRING: "STRING",
  STRING_LIST: "STRING_LIST"
};
var SearchType = {
  HYBRID: "HYBRID",
  SEMANTIC: "SEMANTIC"
};
var RerankingMetadataSelectionMode = {
  ALL: "ALL",
  SELECTIVE: "SELECTIVE"
};
var RerankingMetadataSelectiveModeConfiguration;
((RerankingMetadataSelectiveModeConfiguration3) => {
  RerankingMetadataSelectiveModeConfiguration3.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.fieldsToInclude !== void 0) return visitor.fieldsToInclude(value.fieldsToInclude);
    if (value.fieldsToExclude !== void 0) return visitor.fieldsToExclude(value.fieldsToExclude);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(RerankingMetadataSelectiveModeConfiguration || (RerankingMetadataSelectiveModeConfiguration = {}));
var VectorSearchRerankingConfigurationType = {
  BEDROCK_RERANKING_MODEL: "BEDROCK_RERANKING_MODEL"
};
var RetrieveAndGenerateType = {
  EXTERNAL_SOURCES: "EXTERNAL_SOURCES",
  KNOWLEDGE_BASE: "KNOWLEDGE_BASE"
};
var EvaluationPrecomputedRagSourceConfig;
((EvaluationPrecomputedRagSourceConfig4) => {
  EvaluationPrecomputedRagSourceConfig4.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.retrieveSourceConfig !== void 0) return visitor.retrieveSourceConfig(value.retrieveSourceConfig);
    if (value.retrieveAndGenerateSourceConfig !== void 0)
      return visitor.retrieveAndGenerateSourceConfig(value.retrieveAndGenerateSourceConfig);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(EvaluationPrecomputedRagSourceConfig || (EvaluationPrecomputedRagSourceConfig = {}));
var EvaluationJobType = {
  AUTOMATED: "Automated",
  HUMAN: "Human"
};
var SortJobsBy = {
  CREATION_TIME: "CreationTime"
};
var GuardrailContentFilterAction = {
  BLOCK: "BLOCK",
  NONE: "NONE"
};
var GuardrailModality = {
  IMAGE: "IMAGE",
  TEXT: "TEXT"
};
var GuardrailFilterStrength = {
  HIGH: "HIGH",
  LOW: "LOW",
  MEDIUM: "MEDIUM",
  NONE: "NONE"
};
var GuardrailContentFilterType = {
  HATE: "HATE",
  INSULTS: "INSULTS",
  MISCONDUCT: "MISCONDUCT",
  PROMPT_ATTACK: "PROMPT_ATTACK",
  SEXUAL: "SEXUAL",
  VIOLENCE: "VIOLENCE"
};
var GuardrailContentFiltersTierName = {
  CLASSIC: "CLASSIC",
  STANDARD: "STANDARD"
};
var GuardrailContextualGroundingAction = {
  BLOCK: "BLOCK",
  NONE: "NONE"
};
var GuardrailContextualGroundingFilterType = {
  GROUNDING: "GROUNDING",
  RELEVANCE: "RELEVANCE"
};
var GuardrailSensitiveInformationAction = {
  ANONYMIZE: "ANONYMIZE",
  BLOCK: "BLOCK",
  NONE: "NONE"
};
var GuardrailPiiEntityType = {
  ADDRESS: "ADDRESS",
  AGE: "AGE",
  AWS_ACCESS_KEY: "AWS_ACCESS_KEY",
  AWS_SECRET_KEY: "AWS_SECRET_KEY",
  CA_HEALTH_NUMBER: "CA_HEALTH_NUMBER",
  CA_SOCIAL_INSURANCE_NUMBER: "CA_SOCIAL_INSURANCE_NUMBER",
  CREDIT_DEBIT_CARD_CVV: "CREDIT_DEBIT_CARD_CVV",
  CREDIT_DEBIT_CARD_EXPIRY: "CREDIT_DEBIT_CARD_EXPIRY",
  CREDIT_DEBIT_CARD_NUMBER: "CREDIT_DEBIT_CARD_NUMBER",
  DRIVER_ID: "DRIVER_ID",
  EMAIL: "EMAIL",
  INTERNATIONAL_BANK_ACCOUNT_NUMBER: "INTERNATIONAL_BANK_ACCOUNT_NUMBER",
  IP_ADDRESS: "IP_ADDRESS",
  LICENSE_PLATE: "LICENSE_PLATE",
  MAC_ADDRESS: "MAC_ADDRESS",
  NAME: "NAME",
  PASSWORD: "PASSWORD",
  PHONE: "PHONE",
  PIN: "PIN",
  SWIFT_CODE: "SWIFT_CODE",
  UK_NATIONAL_HEALTH_SERVICE_NUMBER: "UK_NATIONAL_HEALTH_SERVICE_NUMBER",
  UK_NATIONAL_INSURANCE_NUMBER: "UK_NATIONAL_INSURANCE_NUMBER",
  UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER: "UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER",
  URL: "URL",
  USERNAME: "USERNAME",
  US_BANK_ACCOUNT_NUMBER: "US_BANK_ACCOUNT_NUMBER",
  US_BANK_ROUTING_NUMBER: "US_BANK_ROUTING_NUMBER",
  US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER: "US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER",
  US_PASSPORT_NUMBER: "US_PASSPORT_NUMBER",
  US_SOCIAL_SECURITY_NUMBER: "US_SOCIAL_SECURITY_NUMBER",
  VEHICLE_IDENTIFICATION_NUMBER: "VEHICLE_IDENTIFICATION_NUMBER"
};
var GuardrailTopicsTierName = {
  CLASSIC: "CLASSIC",
  STANDARD: "STANDARD"
};
var GuardrailTopicAction = {
  BLOCK: "BLOCK",
  NONE: "NONE"
};
var GuardrailTopicType = {
  DENY: "DENY"
};
var GuardrailWordAction = {
  BLOCK: "BLOCK",
  NONE: "NONE"
};
var GuardrailManagedWordsType = {
  PROFANITY: "PROFANITY"
};
var GuardrailStatus = {
  CREATING: "CREATING",
  DELETING: "DELETING",
  FAILED: "FAILED",
  READY: "READY",
  UPDATING: "UPDATING",
  VERSIONING: "VERSIONING"
};
var InferenceProfileModelSource;
((InferenceProfileModelSource3) => {
  InferenceProfileModelSource3.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.copyFrom !== void 0) return visitor.copyFrom(value.copyFrom);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(InferenceProfileModelSource || (InferenceProfileModelSource = {}));
var InferenceProfileStatus = {
  ACTIVE: "ACTIVE"
};
var InferenceProfileType = {
  APPLICATION: "APPLICATION",
  SYSTEM_DEFINED: "SYSTEM_DEFINED"
};
var ModelCopyJobStatus = {
  COMPLETED: "Completed",
  FAILED: "Failed",
  IN_PROGRESS: "InProgress"
};
var ModelImportJobStatus = {
  COMPLETED: "Completed",
  FAILED: "Failed",
  IN_PROGRESS: "InProgress"
};
var S3InputFormat = {
  JSONL: "JSONL"
};
var ModelInvocationJobInputDataConfig;
((ModelInvocationJobInputDataConfig3) => {
  ModelInvocationJobInputDataConfig3.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.s3InputDataConfig !== void 0) return visitor.s3InputDataConfig(value.s3InputDataConfig);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(ModelInvocationJobInputDataConfig || (ModelInvocationJobInputDataConfig = {}));
var ModelInvocationJobOutputDataConfig;
((ModelInvocationJobOutputDataConfig3) => {
  ModelInvocationJobOutputDataConfig3.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.s3OutputDataConfig !== void 0) return visitor.s3OutputDataConfig(value.s3OutputDataConfig);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(ModelInvocationJobOutputDataConfig || (ModelInvocationJobOutputDataConfig = {}));
var ModelInvocationJobStatus = {
  COMPLETED: "Completed",
  EXPIRED: "Expired",
  FAILED: "Failed",
  IN_PROGRESS: "InProgress",
  PARTIALLY_COMPLETED: "PartiallyCompleted",
  SCHEDULED: "Scheduled",
  STOPPED: "Stopped",
  STOPPING: "Stopping",
  SUBMITTED: "Submitted",
  VALIDATING: "Validating"
};
var ModelCustomization = {
  CONTINUED_PRE_TRAINING: "CONTINUED_PRE_TRAINING",
  DISTILLATION: "DISTILLATION",
  FINE_TUNING: "FINE_TUNING"
};
var InferenceType = {
  ON_DEMAND: "ON_DEMAND",
  PROVISIONED: "PROVISIONED"
};
var ModelModality = {
  EMBEDDING: "EMBEDDING",
  IMAGE: "IMAGE",
  TEXT: "TEXT"
};
var FoundationModelLifecycleStatus = {
  ACTIVE: "ACTIVE",
  LEGACY: "LEGACY"
};
var PromptRouterStatus = {
  AVAILABLE: "AVAILABLE"
};
var PromptRouterType = {
  CUSTOM: "custom",
  DEFAULT: "default"
};
var CommitmentDuration = {
  ONE_MONTH: "OneMonth",
  SIX_MONTHS: "SixMonths"
};
var ProvisionedModelStatus = {
  CREATING: "Creating",
  FAILED: "Failed",
  IN_SERVICE: "InService",
  UPDATING: "Updating"
};
var SortByProvisionedModels = {
  CREATION_TIME: "CreationTime"
};
var AuthorizationStatus = {
  AUTHORIZED: "AUTHORIZED",
  NOT_AUTHORIZED: "NOT_AUTHORIZED"
};
var RequestMetadataBaseFiltersFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.equals && { equals: import_smithy_client.SENSITIVE_STRING },
  ...obj.notEquals && { notEquals: import_smithy_client.SENSITIVE_STRING }
}), "RequestMetadataBaseFiltersFilterSensitiveLog");
var RequestMetadataFiltersFilterSensitiveLog = /* @__PURE__ */ __name((obj) => {
  if (obj.equals !== void 0) return { equals: import_smithy_client.SENSITIVE_STRING };
  if (obj.notEquals !== void 0) return { notEquals: import_smithy_client.SENSITIVE_STRING };
  if (obj.andAll !== void 0)
    return { andAll: obj.andAll.map((item) => RequestMetadataBaseFiltersFilterSensitiveLog(item)) };
  if (obj.orAll !== void 0)
    return { orAll: obj.orAll.map((item) => RequestMetadataBaseFiltersFilterSensitiveLog(item)) };
  if (obj.$unknown !== void 0) return { [obj.$unknown[0]]: "UNKNOWN" };
}, "RequestMetadataFiltersFilterSensitiveLog");
var InvocationLogsConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.invocationLogSource && { invocationLogSource: obj.invocationLogSource },
  ...obj.requestMetadataFilters && {
    requestMetadataFilters: RequestMetadataFiltersFilterSensitiveLog(obj.requestMetadataFilters)
  }
}), "InvocationLogsConfigFilterSensitiveLog");
var TrainingDataConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.invocationLogsConfig && {
    invocationLogsConfig: InvocationLogsConfigFilterSensitiveLog(obj.invocationLogsConfig)
  }
}), "TrainingDataConfigFilterSensitiveLog");
var GetCustomModelResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.trainingDataConfig && { trainingDataConfig: TrainingDataConfigFilterSensitiveLog(obj.trainingDataConfig) },
  ...obj.customizationConfig && { customizationConfig: obj.customizationConfig }
}), "GetCustomModelResponseFilterSensitiveLog");
var BatchDeleteEvaluationJobRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.jobIdentifiers && { jobIdentifiers: import_smithy_client.SENSITIVE_STRING }
}), "BatchDeleteEvaluationJobRequestFilterSensitiveLog");
var BatchDeleteEvaluationJobErrorFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.jobIdentifier && { jobIdentifier: import_smithy_client.SENSITIVE_STRING }
}), "BatchDeleteEvaluationJobErrorFilterSensitiveLog");
var BatchDeleteEvaluationJobItemFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.jobIdentifier && { jobIdentifier: import_smithy_client.SENSITIVE_STRING }
}), "BatchDeleteEvaluationJobItemFilterSensitiveLog");
var BatchDeleteEvaluationJobResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.errors && { errors: obj.errors.map((item) => BatchDeleteEvaluationJobErrorFilterSensitiveLog(item)) },
  ...obj.evaluationJobs && {
    evaluationJobs: obj.evaluationJobs.map((item) => BatchDeleteEvaluationJobItemFilterSensitiveLog(item))
  }
}), "BatchDeleteEvaluationJobResponseFilterSensitiveLog");
var CustomMetricDefinitionFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.name && { name: import_smithy_client.SENSITIVE_STRING },
  ...obj.ratingScale && { ratingScale: obj.ratingScale.map((item) => item) }
}), "CustomMetricDefinitionFilterSensitiveLog");
var AutomatedEvaluationCustomMetricSourceFilterSensitiveLog = /* @__PURE__ */ __name((obj) => {
  if (obj.customMetricDefinition !== void 0) return { customMetricDefinition: import_smithy_client.SENSITIVE_STRING };
  if (obj.$unknown !== void 0) return { [obj.$unknown[0]]: "UNKNOWN" };
}, "AutomatedEvaluationCustomMetricSourceFilterSensitiveLog");
var AutomatedEvaluationCustomMetricConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.customMetrics && {
    customMetrics: obj.customMetrics.map((item) => AutomatedEvaluationCustomMetricSourceFilterSensitiveLog(item))
  }
}), "AutomatedEvaluationCustomMetricConfigFilterSensitiveLog");
var EvaluationDatasetFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.name && { name: import_smithy_client.SENSITIVE_STRING },
  ...obj.datasetLocation && { datasetLocation: obj.datasetLocation }
}), "EvaluationDatasetFilterSensitiveLog");
var EvaluationDatasetMetricConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.dataset && { dataset: EvaluationDatasetFilterSensitiveLog(obj.dataset) },
  ...obj.metricNames && { metricNames: import_smithy_client.SENSITIVE_STRING }
}), "EvaluationDatasetMetricConfigFilterSensitiveLog");
var AutomatedEvaluationConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.datasetMetricConfigs && {
    datasetMetricConfigs: obj.datasetMetricConfigs.map((item) => EvaluationDatasetMetricConfigFilterSensitiveLog(item))
  },
  ...obj.evaluatorModelConfig && { evaluatorModelConfig: obj.evaluatorModelConfig },
  ...obj.customMetricConfig && {
    customMetricConfig: AutomatedEvaluationCustomMetricConfigFilterSensitiveLog(obj.customMetricConfig)
  }
}), "AutomatedEvaluationConfigFilterSensitiveLog");
var HumanEvaluationCustomMetricFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.name && { name: import_smithy_client.SENSITIVE_STRING },
  ...obj.description && { description: import_smithy_client.SENSITIVE_STRING }
}), "HumanEvaluationCustomMetricFilterSensitiveLog");
var HumanWorkflowConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.instructions && { instructions: import_smithy_client.SENSITIVE_STRING }
}), "HumanWorkflowConfigFilterSensitiveLog");
var HumanEvaluationConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.humanWorkflowConfig && {
    humanWorkflowConfig: HumanWorkflowConfigFilterSensitiveLog(obj.humanWorkflowConfig)
  },
  ...obj.customMetrics && {
    customMetrics: obj.customMetrics.map((item) => HumanEvaluationCustomMetricFilterSensitiveLog(item))
  },
  ...obj.datasetMetricConfigs && {
    datasetMetricConfigs: obj.datasetMetricConfigs.map((item) => EvaluationDatasetMetricConfigFilterSensitiveLog(item))
  }
}), "HumanEvaluationConfigFilterSensitiveLog");
var EvaluationConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => {
  if (obj.automated !== void 0) return { automated: AutomatedEvaluationConfigFilterSensitiveLog(obj.automated) };
  if (obj.human !== void 0) return { human: HumanEvaluationConfigFilterSensitiveLog(obj.human) };
  if (obj.$unknown !== void 0) return { [obj.$unknown[0]]: "UNKNOWN" };
}, "EvaluationConfigFilterSensitiveLog");
var EvaluationBedrockModelFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.inferenceParams && { inferenceParams: import_smithy_client.SENSITIVE_STRING }
}), "EvaluationBedrockModelFilterSensitiveLog");
var EvaluationModelConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => {
  if (obj.bedrockModel !== void 0)
    return { bedrockModel: EvaluationBedrockModelFilterSensitiveLog(obj.bedrockModel) };
  if (obj.precomputedInferenceSource !== void 0)
    return { precomputedInferenceSource: obj.precomputedInferenceSource };
  if (obj.$unknown !== void 0) return { [obj.$unknown[0]]: "UNKNOWN" };
}, "EvaluationModelConfigFilterSensitiveLog");
var PromptTemplateFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.textPromptTemplate && { textPromptTemplate: import_smithy_client.SENSITIVE_STRING }
}), "PromptTemplateFilterSensitiveLog");
var ExternalSourcesGenerationConfigurationFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.promptTemplate && { promptTemplate: PromptTemplateFilterSensitiveLog(obj.promptTemplate) }
}), "ExternalSourcesGenerationConfigurationFilterSensitiveLog");
var ByteContentDocFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.identifier && { identifier: import_smithy_client.SENSITIVE_STRING },
  ...obj.data && { data: import_smithy_client.SENSITIVE_STRING }
}), "ByteContentDocFilterSensitiveLog");
var ExternalSourceFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.byteContent && { byteContent: ByteContentDocFilterSensitiveLog(obj.byteContent) }
}), "ExternalSourceFilterSensitiveLog");
var ExternalSourcesRetrieveAndGenerateConfigurationFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.sources && { sources: obj.sources.map((item) => ExternalSourceFilterSensitiveLog(item)) },
  ...obj.generationConfiguration && {
    generationConfiguration: ExternalSourcesGenerationConfigurationFilterSensitiveLog(obj.generationConfiguration)
  }
}), "ExternalSourcesRetrieveAndGenerateConfigurationFilterSensitiveLog");
var GenerationConfigurationFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.promptTemplate && { promptTemplate: PromptTemplateFilterSensitiveLog(obj.promptTemplate) }
}), "GenerationConfigurationFilterSensitiveLog");
var MetadataAttributeSchemaFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj
}), "MetadataAttributeSchemaFilterSensitiveLog");
var ImplicitFilterConfigurationFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.metadataAttributes && { metadataAttributes: import_smithy_client.SENSITIVE_STRING }
}), "ImplicitFilterConfigurationFilterSensitiveLog");
var RerankingMetadataSelectiveModeConfigurationFilterSensitiveLog = /* @__PURE__ */ __name((obj) => {
  if (obj.fieldsToInclude !== void 0) return { fieldsToInclude: import_smithy_client.SENSITIVE_STRING };
  if (obj.fieldsToExclude !== void 0) return { fieldsToExclude: import_smithy_client.SENSITIVE_STRING };
  if (obj.$unknown !== void 0) return { [obj.$unknown[0]]: "UNKNOWN" };
}, "RerankingMetadataSelectiveModeConfigurationFilterSensitiveLog");
var MetadataConfigurationForRerankingFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.selectiveModeConfiguration && {
    selectiveModeConfiguration: RerankingMetadataSelectiveModeConfigurationFilterSensitiveLog(
      obj.selectiveModeConfiguration
    )
  }
}), "MetadataConfigurationForRerankingFilterSensitiveLog");
var VectorSearchBedrockRerankingConfigurationFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.metadataConfiguration && {
    metadataConfiguration: MetadataConfigurationForRerankingFilterSensitiveLog(obj.metadataConfiguration)
  }
}), "VectorSearchBedrockRerankingConfigurationFilterSensitiveLog");
var VectorSearchRerankingConfigurationFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.bedrockRerankingConfiguration && {
    bedrockRerankingConfiguration: VectorSearchBedrockRerankingConfigurationFilterSensitiveLog(
      obj.bedrockRerankingConfiguration
    )
  }
}), "VectorSearchRerankingConfigurationFilterSensitiveLog");
var GetEvaluationJobRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.jobIdentifier && { jobIdentifier: import_smithy_client.SENSITIVE_STRING }
}), "GetEvaluationJobRequestFilterSensitiveLog");
var StopEvaluationJobRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.jobIdentifier && { jobIdentifier: import_smithy_client.SENSITIVE_STRING }
}), "StopEvaluationJobRequestFilterSensitiveLog");
var GuardrailContentFilterConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.inputModalities && { inputModalities: import_smithy_client.SENSITIVE_STRING },
  ...obj.outputModalities && { outputModalities: import_smithy_client.SENSITIVE_STRING },
  ...obj.inputAction && { inputAction: import_smithy_client.SENSITIVE_STRING },
  ...obj.outputAction && { outputAction: import_smithy_client.SENSITIVE_STRING }
}), "GuardrailContentFilterConfigFilterSensitiveLog");
var GuardrailContentFiltersTierConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.tierName && { tierName: import_smithy_client.SENSITIVE_STRING }
}), "GuardrailContentFiltersTierConfigFilterSensitiveLog");
var GuardrailContentPolicyConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.filtersConfig && {
    filtersConfig: obj.filtersConfig.map((item) => GuardrailContentFilterConfigFilterSensitiveLog(item))
  },
  ...obj.tierConfig && { tierConfig: GuardrailContentFiltersTierConfigFilterSensitiveLog(obj.tierConfig) }
}), "GuardrailContentPolicyConfigFilterSensitiveLog");
var GuardrailContextualGroundingFilterConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.action && { action: import_smithy_client.SENSITIVE_STRING }
}), "GuardrailContextualGroundingFilterConfigFilterSensitiveLog");
var GuardrailContextualGroundingPolicyConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.filtersConfig && {
    filtersConfig: obj.filtersConfig.map((item) => GuardrailContextualGroundingFilterConfigFilterSensitiveLog(item))
  }
}), "GuardrailContextualGroundingPolicyConfigFilterSensitiveLog");
var GuardrailTopicsTierConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.tierName && { tierName: import_smithy_client.SENSITIVE_STRING }
}), "GuardrailTopicsTierConfigFilterSensitiveLog");
var GuardrailTopicConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.name && { name: import_smithy_client.SENSITIVE_STRING },
  ...obj.definition && { definition: import_smithy_client.SENSITIVE_STRING },
  ...obj.examples && { examples: import_smithy_client.SENSITIVE_STRING },
  ...obj.inputAction && { inputAction: import_smithy_client.SENSITIVE_STRING },
  ...obj.outputAction && { outputAction: import_smithy_client.SENSITIVE_STRING }
}), "GuardrailTopicConfigFilterSensitiveLog");
var GuardrailTopicPolicyConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.topicsConfig && {
    topicsConfig: obj.topicsConfig.map((item) => GuardrailTopicConfigFilterSensitiveLog(item))
  },
  ...obj.tierConfig && { tierConfig: GuardrailTopicsTierConfigFilterSensitiveLog(obj.tierConfig) }
}), "GuardrailTopicPolicyConfigFilterSensitiveLog");
var GuardrailManagedWordsConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.inputAction && { inputAction: import_smithy_client.SENSITIVE_STRING },
  ...obj.outputAction && { outputAction: import_smithy_client.SENSITIVE_STRING }
}), "GuardrailManagedWordsConfigFilterSensitiveLog");
var GuardrailWordConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.inputAction && { inputAction: import_smithy_client.SENSITIVE_STRING },
  ...obj.outputAction && { outputAction: import_smithy_client.SENSITIVE_STRING }
}), "GuardrailWordConfigFilterSensitiveLog");
var GuardrailWordPolicyConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.wordsConfig && { wordsConfig: obj.wordsConfig.map((item) => GuardrailWordConfigFilterSensitiveLog(item)) },
  ...obj.managedWordListsConfig && {
    managedWordListsConfig: obj.managedWordListsConfig.map(
      (item) => GuardrailManagedWordsConfigFilterSensitiveLog(item)
    )
  }
}), "GuardrailWordPolicyConfigFilterSensitiveLog");
var CreateGuardrailRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.name && { name: import_smithy_client.SENSITIVE_STRING },
  ...obj.description && { description: import_smithy_client.SENSITIVE_STRING },
  ...obj.topicPolicyConfig && {
    topicPolicyConfig: GuardrailTopicPolicyConfigFilterSensitiveLog(obj.topicPolicyConfig)
  },
  ...obj.contentPolicyConfig && {
    contentPolicyConfig: GuardrailContentPolicyConfigFilterSensitiveLog(obj.contentPolicyConfig)
  },
  ...obj.wordPolicyConfig && { wordPolicyConfig: GuardrailWordPolicyConfigFilterSensitiveLog(obj.wordPolicyConfig) },
  ...obj.contextualGroundingPolicyConfig && {
    contextualGroundingPolicyConfig: GuardrailContextualGroundingPolicyConfigFilterSensitiveLog(
      obj.contextualGroundingPolicyConfig
    )
  },
  ...obj.blockedInputMessaging && { blockedInputMessaging: import_smithy_client.SENSITIVE_STRING },
  ...obj.blockedOutputsMessaging && { blockedOutputsMessaging: import_smithy_client.SENSITIVE_STRING }
}), "CreateGuardrailRequestFilterSensitiveLog");
var CreateGuardrailVersionRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.description && { description: import_smithy_client.SENSITIVE_STRING }
}), "CreateGuardrailVersionRequestFilterSensitiveLog");
var GuardrailContentFilterFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.inputModalities && { inputModalities: import_smithy_client.SENSITIVE_STRING },
  ...obj.outputModalities && { outputModalities: import_smithy_client.SENSITIVE_STRING },
  ...obj.inputAction && { inputAction: import_smithy_client.SENSITIVE_STRING },
  ...obj.outputAction && { outputAction: import_smithy_client.SENSITIVE_STRING }
}), "GuardrailContentFilterFilterSensitiveLog");
var GuardrailContentFiltersTierFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.tierName && { tierName: import_smithy_client.SENSITIVE_STRING }
}), "GuardrailContentFiltersTierFilterSensitiveLog");
var GuardrailContentPolicyFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.filters && { filters: obj.filters.map((item) => GuardrailContentFilterFilterSensitiveLog(item)) },
  ...obj.tier && { tier: GuardrailContentFiltersTierFilterSensitiveLog(obj.tier) }
}), "GuardrailContentPolicyFilterSensitiveLog");
var GuardrailContextualGroundingFilterFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.action && { action: import_smithy_client.SENSITIVE_STRING }
}), "GuardrailContextualGroundingFilterFilterSensitiveLog");
var GuardrailContextualGroundingPolicyFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.filters && {
    filters: obj.filters.map((item) => GuardrailContextualGroundingFilterFilterSensitiveLog(item))
  }
}), "GuardrailContextualGroundingPolicyFilterSensitiveLog");
var GuardrailTopicsTierFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.tierName && { tierName: import_smithy_client.SENSITIVE_STRING }
}), "GuardrailTopicsTierFilterSensitiveLog");
var GuardrailTopicFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.name && { name: import_smithy_client.SENSITIVE_STRING },
  ...obj.definition && { definition: import_smithy_client.SENSITIVE_STRING },
  ...obj.examples && { examples: import_smithy_client.SENSITIVE_STRING },
  ...obj.inputAction && { inputAction: import_smithy_client.SENSITIVE_STRING },
  ...obj.outputAction && { outputAction: import_smithy_client.SENSITIVE_STRING }
}), "GuardrailTopicFilterSensitiveLog");
var GuardrailTopicPolicyFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.topics && { topics: obj.topics.map((item) => GuardrailTopicFilterSensitiveLog(item)) },
  ...obj.tier && { tier: GuardrailTopicsTierFilterSensitiveLog(obj.tier) }
}), "GuardrailTopicPolicyFilterSensitiveLog");
var GuardrailManagedWordsFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.inputAction && { inputAction: import_smithy_client.SENSITIVE_STRING },
  ...obj.outputAction && { outputAction: import_smithy_client.SENSITIVE_STRING }
}), "GuardrailManagedWordsFilterSensitiveLog");
var GuardrailWordFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.inputAction && { inputAction: import_smithy_client.SENSITIVE_STRING },
  ...obj.outputAction && { outputAction: import_smithy_client.SENSITIVE_STRING }
}), "GuardrailWordFilterSensitiveLog");
var GuardrailWordPolicyFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.words && { words: obj.words.map((item) => GuardrailWordFilterSensitiveLog(item)) },
  ...obj.managedWordLists && {
    managedWordLists: obj.managedWordLists.map((item) => GuardrailManagedWordsFilterSensitiveLog(item))
  }
}), "GuardrailWordPolicyFilterSensitiveLog");
var GetGuardrailResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.name && { name: import_smithy_client.SENSITIVE_STRING },
  ...obj.description && { description: import_smithy_client.SENSITIVE_STRING },
  ...obj.topicPolicy && { topicPolicy: GuardrailTopicPolicyFilterSensitiveLog(obj.topicPolicy) },
  ...obj.contentPolicy && { contentPolicy: GuardrailContentPolicyFilterSensitiveLog(obj.contentPolicy) },
  ...obj.wordPolicy && { wordPolicy: GuardrailWordPolicyFilterSensitiveLog(obj.wordPolicy) },
  ...obj.contextualGroundingPolicy && {
    contextualGroundingPolicy: GuardrailContextualGroundingPolicyFilterSensitiveLog(obj.contextualGroundingPolicy)
  },
  ...obj.statusReasons && { statusReasons: import_smithy_client.SENSITIVE_STRING },
  ...obj.failureRecommendations && { failureRecommendations: import_smithy_client.SENSITIVE_STRING },
  ...obj.blockedInputMessaging && { blockedInputMessaging: import_smithy_client.SENSITIVE_STRING },
  ...obj.blockedOutputsMessaging && { blockedOutputsMessaging: import_smithy_client.SENSITIVE_STRING }
}), "GetGuardrailResponseFilterSensitiveLog");
var GuardrailSummaryFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.name && { name: import_smithy_client.SENSITIVE_STRING },
  ...obj.description && { description: import_smithy_client.SENSITIVE_STRING }
}), "GuardrailSummaryFilterSensitiveLog");
var ListGuardrailsResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.guardrails && { guardrails: obj.guardrails.map((item) => GuardrailSummaryFilterSensitiveLog(item)) }
}), "ListGuardrailsResponseFilterSensitiveLog");
var UpdateGuardrailRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.name && { name: import_smithy_client.SENSITIVE_STRING },
  ...obj.description && { description: import_smithy_client.SENSITIVE_STRING },
  ...obj.topicPolicyConfig && {
    topicPolicyConfig: GuardrailTopicPolicyConfigFilterSensitiveLog(obj.topicPolicyConfig)
  },
  ...obj.contentPolicyConfig && {
    contentPolicyConfig: GuardrailContentPolicyConfigFilterSensitiveLog(obj.contentPolicyConfig)
  },
  ...obj.wordPolicyConfig && { wordPolicyConfig: GuardrailWordPolicyConfigFilterSensitiveLog(obj.wordPolicyConfig) },
  ...obj.contextualGroundingPolicyConfig && {
    contextualGroundingPolicyConfig: GuardrailContextualGroundingPolicyConfigFilterSensitiveLog(
      obj.contextualGroundingPolicyConfig
    )
  },
  ...obj.blockedInputMessaging && { blockedInputMessaging: import_smithy_client.SENSITIVE_STRING },
  ...obj.blockedOutputsMessaging && { blockedOutputsMessaging: import_smithy_client.SENSITIVE_STRING }
}), "UpdateGuardrailRequestFilterSensitiveLog");
var CreateInferenceProfileRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.description && { description: import_smithy_client.SENSITIVE_STRING },
  ...obj.modelSource && { modelSource: obj.modelSource }
}), "CreateInferenceProfileRequestFilterSensitiveLog");
var GetInferenceProfileResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.description && { description: import_smithy_client.SENSITIVE_STRING }
}), "GetInferenceProfileResponseFilterSensitiveLog");
var InferenceProfileSummaryFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.description && { description: import_smithy_client.SENSITIVE_STRING }
}), "InferenceProfileSummaryFilterSensitiveLog");
var ListInferenceProfilesResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.inferenceProfileSummaries && {
    inferenceProfileSummaries: obj.inferenceProfileSummaries.map(
      (item) => InferenceProfileSummaryFilterSensitiveLog(item)
    )
  }
}), "ListInferenceProfilesResponseFilterSensitiveLog");
var GetModelInvocationJobResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.message && { message: import_smithy_client.SENSITIVE_STRING },
  ...obj.inputDataConfig && { inputDataConfig: obj.inputDataConfig },
  ...obj.outputDataConfig && { outputDataConfig: obj.outputDataConfig }
}), "GetModelInvocationJobResponseFilterSensitiveLog");
var ModelInvocationJobSummaryFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.message && { message: import_smithy_client.SENSITIVE_STRING },
  ...obj.inputDataConfig && { inputDataConfig: obj.inputDataConfig },
  ...obj.outputDataConfig && { outputDataConfig: obj.outputDataConfig }
}), "ModelInvocationJobSummaryFilterSensitiveLog");
var ListModelInvocationJobsResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.invocationJobSummaries && {
    invocationJobSummaries: obj.invocationJobSummaries.map((item) => ModelInvocationJobSummaryFilterSensitiveLog(item))
  }
}), "ListModelInvocationJobsResponseFilterSensitiveLog");
var CreatePromptRouterRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.description && { description: import_smithy_client.SENSITIVE_STRING }
}), "CreatePromptRouterRequestFilterSensitiveLog");
var GetPromptRouterResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.description && { description: import_smithy_client.SENSITIVE_STRING }
}), "GetPromptRouterResponseFilterSensitiveLog");
var PromptRouterSummaryFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.description && { description: import_smithy_client.SENSITIVE_STRING }
}), "PromptRouterSummaryFilterSensitiveLog");
var ListPromptRoutersResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.promptRouterSummaries && {
    promptRouterSummaries: obj.promptRouterSummaries.map((item) => PromptRouterSummaryFilterSensitiveLog(item))
  }
}), "ListPromptRoutersResponseFilterSensitiveLog");

// src/protocols/Aws_restJson1.ts
var import_core2 = require("@aws-sdk/core");


var import_uuid = require("uuid");

// src/models/models_1.ts

var EntitlementAvailability = {
  AVAILABLE: "AVAILABLE",
  NOT_AVAILABLE: "NOT_AVAILABLE"
};
var RegionAvailability = {
  AVAILABLE: "AVAILABLE",
  NOT_AVAILABLE: "NOT_AVAILABLE"
};
var OfferType = {
  ALL: "ALL",
  PUBLIC: "PUBLIC"
};
var ModelCustomizationJobStatus = {
  COMPLETED: "Completed",
  FAILED: "Failed",
  IN_PROGRESS: "InProgress",
  STOPPED: "Stopped",
  STOPPING: "Stopping"
};
var JobStatusDetails = {
  COMPLETED: "Completed",
  FAILED: "Failed",
  IN_PROGRESS: "InProgress",
  NOT_STARTED: "NotStarted",
  STOPPED: "Stopped",
  STOPPING: "Stopping"
};
var FineTuningJobStatus = {
  COMPLETED: "Completed",
  FAILED: "Failed",
  IN_PROGRESS: "InProgress",
  STOPPED: "Stopped",
  STOPPING: "Stopping"
};
var RetrievalFilter;
((RetrievalFilter2) => {
  RetrievalFilter2.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.equals !== void 0) return visitor.equals(value.equals);
    if (value.notEquals !== void 0) return visitor.notEquals(value.notEquals);
    if (value.greaterThan !== void 0) return visitor.greaterThan(value.greaterThan);
    if (value.greaterThanOrEquals !== void 0) return visitor.greaterThanOrEquals(value.greaterThanOrEquals);
    if (value.lessThan !== void 0) return visitor.lessThan(value.lessThan);
    if (value.lessThanOrEquals !== void 0) return visitor.lessThanOrEquals(value.lessThanOrEquals);
    if (value.in !== void 0) return visitor.in(value.in);
    if (value.notIn !== void 0) return visitor.notIn(value.notIn);
    if (value.startsWith !== void 0) return visitor.startsWith(value.startsWith);
    if (value.listContains !== void 0) return visitor.listContains(value.listContains);
    if (value.stringContains !== void 0) return visitor.stringContains(value.stringContains);
    if (value.andAll !== void 0) return visitor.andAll(value.andAll);
    if (value.orAll !== void 0) return visitor.orAll(value.orAll);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(RetrievalFilter || (RetrievalFilter = {}));
var KnowledgeBaseConfig;
((KnowledgeBaseConfig2) => {
  KnowledgeBaseConfig2.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.retrieveConfig !== void 0) return visitor.retrieveConfig(value.retrieveConfig);
    if (value.retrieveAndGenerateConfig !== void 0)
      return visitor.retrieveAndGenerateConfig(value.retrieveAndGenerateConfig);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(KnowledgeBaseConfig || (KnowledgeBaseConfig = {}));
var RAGConfig;
((RAGConfig2) => {
  RAGConfig2.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.knowledgeBaseConfig !== void 0) return visitor.knowledgeBaseConfig(value.knowledgeBaseConfig);
    if (value.precomputedRagSourceConfig !== void 0)
      return visitor.precomputedRagSourceConfig(value.precomputedRagSourceConfig);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(RAGConfig || (RAGConfig = {}));
var EvaluationInferenceConfig;
((EvaluationInferenceConfig2) => {
  EvaluationInferenceConfig2.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.models !== void 0) return visitor.models(value.models);
    if (value.ragConfigs !== void 0) return visitor.ragConfigs(value.ragConfigs);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(EvaluationInferenceConfig || (EvaluationInferenceConfig = {}));
var CreateModelCustomizationJobRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.trainingDataConfig && { trainingDataConfig: TrainingDataConfigFilterSensitiveLog(obj.trainingDataConfig) },
  ...obj.customizationConfig && { customizationConfig: obj.customizationConfig }
}), "CreateModelCustomizationJobRequestFilterSensitiveLog");
var GetModelCustomizationJobResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.trainingDataConfig && { trainingDataConfig: TrainingDataConfigFilterSensitiveLog(obj.trainingDataConfig) },
  ...obj.customizationConfig && { customizationConfig: obj.customizationConfig }
}), "GetModelCustomizationJobResponseFilterSensitiveLog");
var RetrievalFilterFilterSensitiveLog = /* @__PURE__ */ __name((obj) => {
  if (obj.equals !== void 0) return { equals: obj.equals };
  if (obj.notEquals !== void 0) return { notEquals: obj.notEquals };
  if (obj.greaterThan !== void 0) return { greaterThan: obj.greaterThan };
  if (obj.greaterThanOrEquals !== void 0) return { greaterThanOrEquals: obj.greaterThanOrEquals };
  if (obj.lessThan !== void 0) return { lessThan: obj.lessThan };
  if (obj.lessThanOrEquals !== void 0) return { lessThanOrEquals: obj.lessThanOrEquals };
  if (obj.in !== void 0) return { in: obj.in };
  if (obj.notIn !== void 0) return { notIn: obj.notIn };
  if (obj.startsWith !== void 0) return { startsWith: obj.startsWith };
  if (obj.listContains !== void 0) return { listContains: obj.listContains };
  if (obj.stringContains !== void 0) return { stringContains: obj.stringContains };
  if (obj.andAll !== void 0) return { andAll: import_smithy_client.SENSITIVE_STRING };
  if (obj.orAll !== void 0) return { orAll: import_smithy_client.SENSITIVE_STRING };
  if (obj.$unknown !== void 0) return { [obj.$unknown[0]]: "UNKNOWN" };
}, "RetrievalFilterFilterSensitiveLog");
var KnowledgeBaseVectorSearchConfigurationFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.filter && { filter: import_smithy_client.SENSITIVE_STRING },
  ...obj.implicitFilterConfiguration && {
    implicitFilterConfiguration: ImplicitFilterConfigurationFilterSensitiveLog(obj.implicitFilterConfiguration)
  },
  ...obj.rerankingConfiguration && {
    rerankingConfiguration: VectorSearchRerankingConfigurationFilterSensitiveLog(obj.rerankingConfiguration)
  }
}), "KnowledgeBaseVectorSearchConfigurationFilterSensitiveLog");
var KnowledgeBaseRetrievalConfigurationFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.vectorSearchConfiguration && {
    vectorSearchConfiguration: KnowledgeBaseVectorSearchConfigurationFilterSensitiveLog(obj.vectorSearchConfiguration)
  }
}), "KnowledgeBaseRetrievalConfigurationFilterSensitiveLog");
var KnowledgeBaseRetrieveAndGenerateConfigurationFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.retrievalConfiguration && {
    retrievalConfiguration: KnowledgeBaseRetrievalConfigurationFilterSensitiveLog(obj.retrievalConfiguration)
  },
  ...obj.generationConfiguration && {
    generationConfiguration: GenerationConfigurationFilterSensitiveLog(obj.generationConfiguration)
  }
}), "KnowledgeBaseRetrieveAndGenerateConfigurationFilterSensitiveLog");
var RetrieveConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.knowledgeBaseRetrievalConfiguration && {
    knowledgeBaseRetrievalConfiguration: KnowledgeBaseRetrievalConfigurationFilterSensitiveLog(
      obj.knowledgeBaseRetrievalConfiguration
    )
  }
}), "RetrieveConfigFilterSensitiveLog");
var RetrieveAndGenerateConfigurationFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.knowledgeBaseConfiguration && {
    knowledgeBaseConfiguration: KnowledgeBaseRetrieveAndGenerateConfigurationFilterSensitiveLog(
      obj.knowledgeBaseConfiguration
    )
  },
  ...obj.externalSourcesConfiguration && {
    externalSourcesConfiguration: ExternalSourcesRetrieveAndGenerateConfigurationFilterSensitiveLog(
      obj.externalSourcesConfiguration
    )
  }
}), "RetrieveAndGenerateConfigurationFilterSensitiveLog");
var KnowledgeBaseConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => {
  if (obj.retrieveConfig !== void 0) return { retrieveConfig: RetrieveConfigFilterSensitiveLog(obj.retrieveConfig) };
  if (obj.retrieveAndGenerateConfig !== void 0)
    return {
      retrieveAndGenerateConfig: RetrieveAndGenerateConfigurationFilterSensitiveLog(obj.retrieveAndGenerateConfig)
    };
  if (obj.$unknown !== void 0) return { [obj.$unknown[0]]: "UNKNOWN" };
}, "KnowledgeBaseConfigFilterSensitiveLog");
var RAGConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => {
  if (obj.knowledgeBaseConfig !== void 0)
    return { knowledgeBaseConfig: KnowledgeBaseConfigFilterSensitiveLog(obj.knowledgeBaseConfig) };
  if (obj.precomputedRagSourceConfig !== void 0)
    return { precomputedRagSourceConfig: obj.precomputedRagSourceConfig };
  if (obj.$unknown !== void 0) return { [obj.$unknown[0]]: "UNKNOWN" };
}, "RAGConfigFilterSensitiveLog");
var EvaluationInferenceConfigFilterSensitiveLog = /* @__PURE__ */ __name((obj) => {
  if (obj.models !== void 0)
    return { models: obj.models.map((item) => EvaluationModelConfigFilterSensitiveLog(item)) };
  if (obj.ragConfigs !== void 0)
    return { ragConfigs: obj.ragConfigs.map((item) => RAGConfigFilterSensitiveLog(item)) };
  if (obj.$unknown !== void 0) return { [obj.$unknown[0]]: "UNKNOWN" };
}, "EvaluationInferenceConfigFilterSensitiveLog");
var CreateEvaluationJobRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.jobDescription && { jobDescription: import_smithy_client.SENSITIVE_STRING },
  ...obj.evaluationConfig && { evaluationConfig: EvaluationConfigFilterSensitiveLog(obj.evaluationConfig) },
  ...obj.inferenceConfig && { inferenceConfig: EvaluationInferenceConfigFilterSensitiveLog(obj.inferenceConfig) }
}), "CreateEvaluationJobRequestFilterSensitiveLog");
var GetEvaluationJobResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.jobDescription && { jobDescription: import_smithy_client.SENSITIVE_STRING },
  ...obj.evaluationConfig && { evaluationConfig: EvaluationConfigFilterSensitiveLog(obj.evaluationConfig) },
  ...obj.inferenceConfig && { inferenceConfig: EvaluationInferenceConfigFilterSensitiveLog(obj.inferenceConfig) }
}), "GetEvaluationJobResponseFilterSensitiveLog");

// src/protocols/Aws_restJson1.ts
var se_BatchDeleteEvaluationJobCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/evaluation-jobs/batch-delete");
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      jobIdentifiers: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "jobIdentifiers")
    })
  );
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_BatchDeleteEvaluationJobCommand");
var se_CreateCustomModelCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/custom-models/create-custom-model");
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      clientRequestToken: [true, (_) => _ ?? (0, import_uuid.v4)()],
      modelKmsKeyArn: [],
      modelName: [],
      modelSourceConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "modelSourceConfig"),
      modelTags: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "modelTags"),
      roleArn: []
    })
  );
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_CreateCustomModelCommand");
var se_CreateEvaluationJobCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/evaluation-jobs");
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      applicationType: [],
      clientRequestToken: [true, (_) => _ ?? (0, import_uuid.v4)()],
      customerEncryptionKeyId: [],
      evaluationConfig: /* @__PURE__ */ __name((_) => se_EvaluationConfig(_, context), "evaluationConfig"),
      inferenceConfig: /* @__PURE__ */ __name((_) => se_EvaluationInferenceConfig(_, context), "inferenceConfig"),
      jobDescription: [],
      jobName: [],
      jobTags: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "jobTags"),
      outputDataConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "outputDataConfig"),
      roleArn: []
    })
  );
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_CreateEvaluationJobCommand");
var se_CreateFoundationModelAgreementCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/create-foundation-model-agreement");
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      modelId: [],
      offerToken: []
    })
  );
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_CreateFoundationModelAgreementCommand");
var se_CreateGuardrailCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/guardrails");
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      blockedInputMessaging: [],
      blockedOutputsMessaging: [],
      clientRequestToken: [true, (_) => _ ?? (0, import_uuid.v4)()],
      contentPolicyConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "contentPolicyConfig"),
      contextualGroundingPolicyConfig: /* @__PURE__ */ __name((_) => se_GuardrailContextualGroundingPolicyConfig(_, context), "contextualGroundingPolicyConfig"),
      crossRegionConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "crossRegionConfig"),
      description: [],
      kmsKeyId: [],
      name: [],
      sensitiveInformationPolicyConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "sensitiveInformationPolicyConfig"),
      tags: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "tags"),
      topicPolicyConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "topicPolicyConfig"),
      wordPolicyConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "wordPolicyConfig")
    })
  );
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_CreateGuardrailCommand");
var se_CreateGuardrailVersionCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/guardrails/{guardrailIdentifier}");
  b.p("guardrailIdentifier", () => input.guardrailIdentifier, "{guardrailIdentifier}", false);
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      clientRequestToken: [true, (_) => _ ?? (0, import_uuid.v4)()],
      description: []
    })
  );
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_CreateGuardrailVersionCommand");
var se_CreateInferenceProfileCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/inference-profiles");
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      clientRequestToken: [true, (_) => _ ?? (0, import_uuid.v4)()],
      description: [],
      inferenceProfileName: [],
      modelSource: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "modelSource"),
      tags: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "tags")
    })
  );
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_CreateInferenceProfileCommand");
var se_CreateMarketplaceModelEndpointCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/marketplace-model/endpoints");
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      acceptEula: [],
      clientRequestToken: [true, (_) => _ ?? (0, import_uuid.v4)()],
      endpointConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "endpointConfig"),
      endpointName: [],
      modelSourceIdentifier: [],
      tags: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "tags")
    })
  );
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_CreateMarketplaceModelEndpointCommand");
var se_CreateModelCopyJobCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/model-copy-jobs");
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      clientRequestToken: [true, (_) => _ ?? (0, import_uuid.v4)()],
      modelKmsKeyId: [],
      sourceModelArn: [],
      targetModelName: [],
      targetModelTags: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "targetModelTags")
    })
  );
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_CreateModelCopyJobCommand");
var se_CreateModelCustomizationJobCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/model-customization-jobs");
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      baseModelIdentifier: [],
      clientRequestToken: [true, (_) => _ ?? (0, import_uuid.v4)()],
      customModelKmsKeyId: [],
      customModelName: [],
      customModelTags: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "customModelTags"),
      customizationConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "customizationConfig"),
      customizationType: [],
      hyperParameters: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "hyperParameters"),
      jobName: [],
      jobTags: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "jobTags"),
      outputDataConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "outputDataConfig"),
      roleArn: [],
      trainingDataConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "trainingDataConfig"),
      validationDataConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "validationDataConfig"),
      vpcConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "vpcConfig")
    })
  );
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_CreateModelCustomizationJobCommand");
var se_CreateModelImportJobCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/model-import-jobs");
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      clientRequestToken: [],
      importedModelKmsKeyId: [],
      importedModelName: [],
      importedModelTags: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "importedModelTags"),
      jobName: [],
      jobTags: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "jobTags"),
      modelDataSource: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "modelDataSource"),
      roleArn: [],
      vpcConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "vpcConfig")
    })
  );
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_CreateModelImportJobCommand");
var se_CreateModelInvocationJobCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/model-invocation-job");
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      clientRequestToken: [true, (_) => _ ?? (0, import_uuid.v4)()],
      inputDataConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "inputDataConfig"),
      jobName: [],
      modelId: [],
      outputDataConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "outputDataConfig"),
      roleArn: [],
      tags: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "tags"),
      timeoutDurationInHours: [],
      vpcConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "vpcConfig")
    })
  );
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_CreateModelInvocationJobCommand");
var se_CreatePromptRouterCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/prompt-routers");
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      clientRequestToken: [true, (_) => _ ?? (0, import_uuid.v4)()],
      description: [],
      fallbackModel: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "fallbackModel"),
      models: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "models"),
      promptRouterName: [],
      routingCriteria: /* @__PURE__ */ __name((_) => se_RoutingCriteria(_, context), "routingCriteria"),
      tags: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "tags")
    })
  );
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_CreatePromptRouterCommand");
var se_CreateProvisionedModelThroughputCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/provisioned-model-throughput");
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      clientRequestToken: [true, (_) => _ ?? (0, import_uuid.v4)()],
      commitmentDuration: [],
      modelId: [],
      modelUnits: [],
      provisionedModelName: [],
      tags: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "tags")
    })
  );
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_CreateProvisionedModelThroughputCommand");
var se_DeleteCustomModelCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/custom-models/{modelIdentifier}");
  b.p("modelIdentifier", () => input.modelIdentifier, "{modelIdentifier}", false);
  let body;
  b.m("DELETE").h(headers).b(body);
  return b.build();
}, "se_DeleteCustomModelCommand");
var se_DeleteFoundationModelAgreementCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/delete-foundation-model-agreement");
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      modelId: []
    })
  );
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_DeleteFoundationModelAgreementCommand");
var se_DeleteGuardrailCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/guardrails/{guardrailIdentifier}");
  b.p("guardrailIdentifier", () => input.guardrailIdentifier, "{guardrailIdentifier}", false);
  const query = (0, import_smithy_client.map)({
    [_gV]: [, input[_gV]]
  });
  let body;
  b.m("DELETE").h(headers).q(query).b(body);
  return b.build();
}, "se_DeleteGuardrailCommand");
var se_DeleteImportedModelCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/imported-models/{modelIdentifier}");
  b.p("modelIdentifier", () => input.modelIdentifier, "{modelIdentifier}", false);
  let body;
  b.m("DELETE").h(headers).b(body);
  return b.build();
}, "se_DeleteImportedModelCommand");
var se_DeleteInferenceProfileCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/inference-profiles/{inferenceProfileIdentifier}");
  b.p("inferenceProfileIdentifier", () => input.inferenceProfileIdentifier, "{inferenceProfileIdentifier}", false);
  let body;
  b.m("DELETE").h(headers).b(body);
  return b.build();
}, "se_DeleteInferenceProfileCommand");
var se_DeleteMarketplaceModelEndpointCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/marketplace-model/endpoints/{endpointArn}");
  b.p("endpointArn", () => input.endpointArn, "{endpointArn}", false);
  let body;
  b.m("DELETE").h(headers).b(body);
  return b.build();
}, "se_DeleteMarketplaceModelEndpointCommand");
var se_DeleteModelInvocationLoggingConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/logging/modelinvocations");
  let body;
  b.m("DELETE").h(headers).b(body);
  return b.build();
}, "se_DeleteModelInvocationLoggingConfigurationCommand");
var se_DeletePromptRouterCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/prompt-routers/{promptRouterArn}");
  b.p("promptRouterArn", () => input.promptRouterArn, "{promptRouterArn}", false);
  let body;
  b.m("DELETE").h(headers).b(body);
  return b.build();
}, "se_DeletePromptRouterCommand");
var se_DeleteProvisionedModelThroughputCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/provisioned-model-throughput/{provisionedModelId}");
  b.p("provisionedModelId", () => input.provisionedModelId, "{provisionedModelId}", false);
  let body;
  b.m("DELETE").h(headers).b(body);
  return b.build();
}, "se_DeleteProvisionedModelThroughputCommand");
var se_DeregisterMarketplaceModelEndpointCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/marketplace-model/endpoints/{endpointArn}/registration");
  b.p("endpointArn", () => input.endpointArn, "{endpointArn}", false);
  let body;
  b.m("DELETE").h(headers).b(body);
  return b.build();
}, "se_DeregisterMarketplaceModelEndpointCommand");
var se_GetCustomModelCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/custom-models/{modelIdentifier}");
  b.p("modelIdentifier", () => input.modelIdentifier, "{modelIdentifier}", false);
  let body;
  b.m("GET").h(headers).b(body);
  return b.build();
}, "se_GetCustomModelCommand");
var se_GetEvaluationJobCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/evaluation-jobs/{jobIdentifier}");
  b.p("jobIdentifier", () => input.jobIdentifier, "{jobIdentifier}", false);
  let body;
  b.m("GET").h(headers).b(body);
  return b.build();
}, "se_GetEvaluationJobCommand");
var se_GetFoundationModelCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/foundation-models/{modelIdentifier}");
  b.p("modelIdentifier", () => input.modelIdentifier, "{modelIdentifier}", false);
  let body;
  b.m("GET").h(headers).b(body);
  return b.build();
}, "se_GetFoundationModelCommand");
var se_GetFoundationModelAvailabilityCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/foundation-model-availability/{modelId}");
  b.p("modelId", () => input.modelId, "{modelId}", false);
  let body;
  b.m("GET").h(headers).b(body);
  return b.build();
}, "se_GetFoundationModelAvailabilityCommand");
var se_GetGuardrailCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/guardrails/{guardrailIdentifier}");
  b.p("guardrailIdentifier", () => input.guardrailIdentifier, "{guardrailIdentifier}", false);
  const query = (0, import_smithy_client.map)({
    [_gV]: [, input[_gV]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetGuardrailCommand");
var se_GetImportedModelCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/imported-models/{modelIdentifier}");
  b.p("modelIdentifier", () => input.modelIdentifier, "{modelIdentifier}", false);
  let body;
  b.m("GET").h(headers).b(body);
  return b.build();
}, "se_GetImportedModelCommand");
var se_GetInferenceProfileCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/inference-profiles/{inferenceProfileIdentifier}");
  b.p("inferenceProfileIdentifier", () => input.inferenceProfileIdentifier, "{inferenceProfileIdentifier}", false);
  let body;
  b.m("GET").h(headers).b(body);
  return b.build();
}, "se_GetInferenceProfileCommand");
var se_GetMarketplaceModelEndpointCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/marketplace-model/endpoints/{endpointArn}");
  b.p("endpointArn", () => input.endpointArn, "{endpointArn}", false);
  let body;
  b.m("GET").h(headers).b(body);
  return b.build();
}, "se_GetMarketplaceModelEndpointCommand");
var se_GetModelCopyJobCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/model-copy-jobs/{jobArn}");
  b.p("jobArn", () => input.jobArn, "{jobArn}", false);
  let body;
  b.m("GET").h(headers).b(body);
  return b.build();
}, "se_GetModelCopyJobCommand");
var se_GetModelCustomizationJobCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/model-customization-jobs/{jobIdentifier}");
  b.p("jobIdentifier", () => input.jobIdentifier, "{jobIdentifier}", false);
  let body;
  b.m("GET").h(headers).b(body);
  return b.build();
}, "se_GetModelCustomizationJobCommand");
var se_GetModelImportJobCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/model-import-jobs/{jobIdentifier}");
  b.p("jobIdentifier", () => input.jobIdentifier, "{jobIdentifier}", false);
  let body;
  b.m("GET").h(headers).b(body);
  return b.build();
}, "se_GetModelImportJobCommand");
var se_GetModelInvocationJobCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/model-invocation-job/{jobIdentifier}");
  b.p("jobIdentifier", () => input.jobIdentifier, "{jobIdentifier}", false);
  let body;
  b.m("GET").h(headers).b(body);
  return b.build();
}, "se_GetModelInvocationJobCommand");
var se_GetModelInvocationLoggingConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/logging/modelinvocations");
  let body;
  b.m("GET").h(headers).b(body);
  return b.build();
}, "se_GetModelInvocationLoggingConfigurationCommand");
var se_GetPromptRouterCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/prompt-routers/{promptRouterArn}");
  b.p("promptRouterArn", () => input.promptRouterArn, "{promptRouterArn}", false);
  let body;
  b.m("GET").h(headers).b(body);
  return b.build();
}, "se_GetPromptRouterCommand");
var se_GetProvisionedModelThroughputCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/provisioned-model-throughput/{provisionedModelId}");
  b.p("provisionedModelId", () => input.provisionedModelId, "{provisionedModelId}", false);
  let body;
  b.m("GET").h(headers).b(body);
  return b.build();
}, "se_GetProvisionedModelThroughputCommand");
var se_GetUseCaseForModelAccessCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/use-case-for-model-access");
  let body;
  b.m("GET").h(headers).b(body);
  return b.build();
}, "se_GetUseCaseForModelAccessCommand");
var se_ListCustomModelsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/custom-models");
  const query = (0, import_smithy_client.map)({
    [_cTB]: [() => input.creationTimeBefore !== void 0, () => (0, import_smithy_client.serializeDateTime)(input[_cTB]).toString()],
    [_cTA]: [() => input.creationTimeAfter !== void 0, () => (0, import_smithy_client.serializeDateTime)(input[_cTA]).toString()],
    [_nC]: [, input[_nC]],
    [_bMAE]: [, input[_bMAE]],
    [_fMAE]: [, input[_fMAE]],
    [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
    [_nT]: [, input[_nT]],
    [_sB]: [, input[_sB]],
    [_sO]: [, input[_sO]],
    [_iO]: [() => input.isOwned !== void 0, () => input[_iO].toString()],
    [_mS]: [, input[_mS]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListCustomModelsCommand");
var se_ListEvaluationJobsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/evaluation-jobs");
  const query = (0, import_smithy_client.map)({
    [_cTA]: [() => input.creationTimeAfter !== void 0, () => (0, import_smithy_client.serializeDateTime)(input[_cTA]).toString()],
    [_cTB]: [() => input.creationTimeBefore !== void 0, () => (0, import_smithy_client.serializeDateTime)(input[_cTB]).toString()],
    [_sE]: [, input[_sE]],
    [_aTE]: [, input[_aTE]],
    [_nC]: [, input[_nC]],
    [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
    [_nT]: [, input[_nT]],
    [_sB]: [, input[_sB]],
    [_sO]: [, input[_sO]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListEvaluationJobsCommand");
var se_ListFoundationModelAgreementOffersCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/list-foundation-model-agreement-offers/{modelId}");
  b.p("modelId", () => input.modelId, "{modelId}", false);
  const query = (0, import_smithy_client.map)({
    [_oT]: [, input[_oT]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListFoundationModelAgreementOffersCommand");
var se_ListFoundationModelsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/foundation-models");
  const query = (0, import_smithy_client.map)({
    [_bP]: [, input[_bP]],
    [_bCT]: [, input[_bCT]],
    [_bOM]: [, input[_bOM]],
    [_bIT]: [, input[_bIT]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListFoundationModelsCommand");
var se_ListGuardrailsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/guardrails");
  const query = (0, import_smithy_client.map)({
    [_gI]: [, input[_gI]],
    [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
    [_nT]: [, input[_nT]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListGuardrailsCommand");
var se_ListImportedModelsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/imported-models");
  const query = (0, import_smithy_client.map)({
    [_cTB]: [() => input.creationTimeBefore !== void 0, () => (0, import_smithy_client.serializeDateTime)(input[_cTB]).toString()],
    [_cTA]: [() => input.creationTimeAfter !== void 0, () => (0, import_smithy_client.serializeDateTime)(input[_cTA]).toString()],
    [_nC]: [, input[_nC]],
    [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
    [_nT]: [, input[_nT]],
    [_sB]: [, input[_sB]],
    [_sO]: [, input[_sO]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListImportedModelsCommand");
var se_ListInferenceProfilesCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/inference-profiles");
  const query = (0, import_smithy_client.map)({
    [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
    [_nT]: [, input[_nT]],
    [_t]: [, input[_tE]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListInferenceProfilesCommand");
var se_ListMarketplaceModelEndpointsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/marketplace-model/endpoints");
  const query = (0, import_smithy_client.map)({
    [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
    [_nT]: [, input[_nT]],
    [_mSI]: [, input[_mSE]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListMarketplaceModelEndpointsCommand");
var se_ListModelCopyJobsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/model-copy-jobs");
  const query = (0, import_smithy_client.map)({
    [_cTA]: [() => input.creationTimeAfter !== void 0, () => (0, import_smithy_client.serializeDateTime)(input[_cTA]).toString()],
    [_cTB]: [() => input.creationTimeBefore !== void 0, () => (0, import_smithy_client.serializeDateTime)(input[_cTB]).toString()],
    [_sE]: [, input[_sE]],
    [_sAE]: [, input[_sAE]],
    [_sMAE]: [, input[_sMAE]],
    [_oMNC]: [, input[_tMNC]],
    [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
    [_nT]: [, input[_nT]],
    [_sB]: [, input[_sB]],
    [_sO]: [, input[_sO]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListModelCopyJobsCommand");
var se_ListModelCustomizationJobsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/model-customization-jobs");
  const query = (0, import_smithy_client.map)({
    [_cTA]: [() => input.creationTimeAfter !== void 0, () => (0, import_smithy_client.serializeDateTime)(input[_cTA]).toString()],
    [_cTB]: [() => input.creationTimeBefore !== void 0, () => (0, import_smithy_client.serializeDateTime)(input[_cTB]).toString()],
    [_sE]: [, input[_sE]],
    [_nC]: [, input[_nC]],
    [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
    [_nT]: [, input[_nT]],
    [_sB]: [, input[_sB]],
    [_sO]: [, input[_sO]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListModelCustomizationJobsCommand");
var se_ListModelImportJobsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/model-import-jobs");
  const query = (0, import_smithy_client.map)({
    [_cTA]: [() => input.creationTimeAfter !== void 0, () => (0, import_smithy_client.serializeDateTime)(input[_cTA]).toString()],
    [_cTB]: [() => input.creationTimeBefore !== void 0, () => (0, import_smithy_client.serializeDateTime)(input[_cTB]).toString()],
    [_sE]: [, input[_sE]],
    [_nC]: [, input[_nC]],
    [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
    [_nT]: [, input[_nT]],
    [_sB]: [, input[_sB]],
    [_sO]: [, input[_sO]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListModelImportJobsCommand");
var se_ListModelInvocationJobsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/model-invocation-jobs");
  const query = (0, import_smithy_client.map)({
    [_sTA]: [() => input.submitTimeAfter !== void 0, () => (0, import_smithy_client.serializeDateTime)(input[_sTA]).toString()],
    [_sTB]: [() => input.submitTimeBefore !== void 0, () => (0, import_smithy_client.serializeDateTime)(input[_sTB]).toString()],
    [_sE]: [, input[_sE]],
    [_nC]: [, input[_nC]],
    [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
    [_nT]: [, input[_nT]],
    [_sB]: [, input[_sB]],
    [_sO]: [, input[_sO]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListModelInvocationJobsCommand");
var se_ListPromptRoutersCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/prompt-routers");
  const query = (0, import_smithy_client.map)({
    [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
    [_nT]: [, input[_nT]],
    [_t]: [, input[_t]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListPromptRoutersCommand");
var se_ListProvisionedModelThroughputsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/provisioned-model-throughputs");
  const query = (0, import_smithy_client.map)({
    [_cTA]: [() => input.creationTimeAfter !== void 0, () => (0, import_smithy_client.serializeDateTime)(input[_cTA]).toString()],
    [_cTB]: [() => input.creationTimeBefore !== void 0, () => (0, import_smithy_client.serializeDateTime)(input[_cTB]).toString()],
    [_sE]: [, input[_sE]],
    [_mAE]: [, input[_mAE]],
    [_nC]: [, input[_nC]],
    [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
    [_nT]: [, input[_nT]],
    [_sB]: [, input[_sB]],
    [_sO]: [, input[_sO]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListProvisionedModelThroughputsCommand");
var se_ListTagsForResourceCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/listTagsForResource");
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      resourceARN: []
    })
  );
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_ListTagsForResourceCommand");
var se_PutModelInvocationLoggingConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/logging/modelinvocations");
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      loggingConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "loggingConfig")
    })
  );
  b.m("PUT").h(headers).b(body);
  return b.build();
}, "se_PutModelInvocationLoggingConfigurationCommand");
var se_PutUseCaseForModelAccessCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/use-case-for-model-access");
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      formData: /* @__PURE__ */ __name((_) => context.base64Encoder(_), "formData")
    })
  );
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_PutUseCaseForModelAccessCommand");
var se_RegisterMarketplaceModelEndpointCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/marketplace-model/endpoints/{endpointIdentifier}/registration");
  b.p("endpointIdentifier", () => input.endpointIdentifier, "{endpointIdentifier}", false);
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      modelSourceIdentifier: []
    })
  );
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_RegisterMarketplaceModelEndpointCommand");
var se_StopEvaluationJobCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/evaluation-job/{jobIdentifier}/stop");
  b.p("jobIdentifier", () => input.jobIdentifier, "{jobIdentifier}", false);
  let body;
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_StopEvaluationJobCommand");
var se_StopModelCustomizationJobCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/model-customization-jobs/{jobIdentifier}/stop");
  b.p("jobIdentifier", () => input.jobIdentifier, "{jobIdentifier}", false);
  let body;
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_StopModelCustomizationJobCommand");
var se_StopModelInvocationJobCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/model-invocation-job/{jobIdentifier}/stop");
  b.p("jobIdentifier", () => input.jobIdentifier, "{jobIdentifier}", false);
  let body;
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_StopModelInvocationJobCommand");
var se_TagResourceCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/tagResource");
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      resourceARN: [],
      tags: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "tags")
    })
  );
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_TagResourceCommand");
var se_UntagResourceCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/untagResource");
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      resourceARN: [],
      tagKeys: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "tagKeys")
    })
  );
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_UntagResourceCommand");
var se_UpdateGuardrailCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/guardrails/{guardrailIdentifier}");
  b.p("guardrailIdentifier", () => input.guardrailIdentifier, "{guardrailIdentifier}", false);
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      blockedInputMessaging: [],
      blockedOutputsMessaging: [],
      contentPolicyConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "contentPolicyConfig"),
      contextualGroundingPolicyConfig: /* @__PURE__ */ __name((_) => se_GuardrailContextualGroundingPolicyConfig(_, context), "contextualGroundingPolicyConfig"),
      crossRegionConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "crossRegionConfig"),
      description: [],
      kmsKeyId: [],
      name: [],
      sensitiveInformationPolicyConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "sensitiveInformationPolicyConfig"),
      topicPolicyConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "topicPolicyConfig"),
      wordPolicyConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "wordPolicyConfig")
    })
  );
  b.m("PUT").h(headers).b(body);
  return b.build();
}, "se_UpdateGuardrailCommand");
var se_UpdateMarketplaceModelEndpointCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/marketplace-model/endpoints/{endpointArn}");
  b.p("endpointArn", () => input.endpointArn, "{endpointArn}", false);
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      clientRequestToken: [true, (_) => _ ?? (0, import_uuid.v4)()],
      endpointConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)(_), "endpointConfig")
    })
  );
  b.m("PATCH").h(headers).b(body);
  return b.build();
}, "se_UpdateMarketplaceModelEndpointCommand");
var se_UpdateProvisionedModelThroughputCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/json"
  };
  b.bp("/provisioned-model-throughput/{provisionedModelId}");
  b.p("provisionedModelId", () => input.provisionedModelId, "{provisionedModelId}", false);
  let body;
  body = JSON.stringify(
    (0, import_smithy_client.take)(input, {
      desiredModelId: [],
      desiredProvisionedModelName: []
    })
  );
  b.m("PATCH").h(headers).b(body);
  return b.build();
}, "se_UpdateProvisionedModelThroughputCommand");
var de_BatchDeleteEvaluationJobCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 202 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    errors: import_smithy_client._json,
    evaluationJobs: import_smithy_client._json
  });
  Object.assign(contents, doc);
  return contents;
}, "de_BatchDeleteEvaluationJobCommand");
var de_CreateCustomModelCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    modelArn: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_CreateCustomModelCommand");
var de_CreateEvaluationJobCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 202 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    jobArn: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_CreateEvaluationJobCommand");
var de_CreateFoundationModelAgreementCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 202 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    modelId: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_CreateFoundationModelAgreementCommand");
var de_CreateGuardrailCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 202 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    createdAt: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "createdAt"),
    guardrailArn: import_smithy_client.expectString,
    guardrailId: import_smithy_client.expectString,
    version: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_CreateGuardrailCommand");
var de_CreateGuardrailVersionCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 202 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    guardrailId: import_smithy_client.expectString,
    version: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_CreateGuardrailVersionCommand");
var de_CreateInferenceProfileCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 201 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    inferenceProfileArn: import_smithy_client.expectString,
    status: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_CreateInferenceProfileCommand");
var de_CreateMarketplaceModelEndpointCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    marketplaceModelEndpoint: /* @__PURE__ */ __name((_) => de_MarketplaceModelEndpoint(_, context), "marketplaceModelEndpoint")
  });
  Object.assign(contents, doc);
  return contents;
}, "de_CreateMarketplaceModelEndpointCommand");
var de_CreateModelCopyJobCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 201 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    jobArn: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_CreateModelCopyJobCommand");
var de_CreateModelCustomizationJobCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 201 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    jobArn: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_CreateModelCustomizationJobCommand");
var de_CreateModelImportJobCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 201 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    jobArn: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_CreateModelImportJobCommand");
var de_CreateModelInvocationJobCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    jobArn: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_CreateModelInvocationJobCommand");
var de_CreatePromptRouterCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    promptRouterArn: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_CreatePromptRouterCommand");
var de_CreateProvisionedModelThroughputCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 201 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    provisionedModelArn: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_CreateProvisionedModelThroughputCommand");
var de_DeleteCustomModelCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteCustomModelCommand");
var de_DeleteFoundationModelAgreementCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 202 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteFoundationModelAgreementCommand");
var de_DeleteGuardrailCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 202 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteGuardrailCommand");
var de_DeleteImportedModelCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteImportedModelCommand");
var de_DeleteInferenceProfileCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteInferenceProfileCommand");
var de_DeleteMarketplaceModelEndpointCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteMarketplaceModelEndpointCommand");
var de_DeleteModelInvocationLoggingConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteModelInvocationLoggingConfigurationCommand");
var de_DeletePromptRouterCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeletePromptRouterCommand");
var de_DeleteProvisionedModelThroughputCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteProvisionedModelThroughputCommand");
var de_DeregisterMarketplaceModelEndpointCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeregisterMarketplaceModelEndpointCommand");
var de_GetCustomModelCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    baseModelArn: import_smithy_client.expectString,
    creationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "creationTime"),
    customizationConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)((0, import_core2.awsExpectUnion)(_)), "customizationConfig"),
    customizationType: import_smithy_client.expectString,
    failureMessage: import_smithy_client.expectString,
    hyperParameters: import_smithy_client._json,
    jobArn: import_smithy_client.expectString,
    jobName: import_smithy_client.expectString,
    modelArn: import_smithy_client.expectString,
    modelKmsKeyArn: import_smithy_client.expectString,
    modelName: import_smithy_client.expectString,
    modelStatus: import_smithy_client.expectString,
    outputDataConfig: import_smithy_client._json,
    trainingDataConfig: import_smithy_client._json,
    trainingMetrics: /* @__PURE__ */ __name((_) => de_TrainingMetrics(_, context), "trainingMetrics"),
    validationDataConfig: import_smithy_client._json,
    validationMetrics: /* @__PURE__ */ __name((_) => de_ValidationMetrics(_, context), "validationMetrics")
  });
  Object.assign(contents, doc);
  return contents;
}, "de_GetCustomModelCommand");
var de_GetEvaluationJobCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    applicationType: import_smithy_client.expectString,
    creationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "creationTime"),
    customerEncryptionKeyId: import_smithy_client.expectString,
    evaluationConfig: /* @__PURE__ */ __name((_) => de_EvaluationConfig((0, import_core2.awsExpectUnion)(_), context), "evaluationConfig"),
    failureMessages: import_smithy_client._json,
    inferenceConfig: /* @__PURE__ */ __name((_) => de_EvaluationInferenceConfig((0, import_core2.awsExpectUnion)(_), context), "inferenceConfig"),
    jobArn: import_smithy_client.expectString,
    jobDescription: import_smithy_client.expectString,
    jobName: import_smithy_client.expectString,
    jobType: import_smithy_client.expectString,
    lastModifiedTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "lastModifiedTime"),
    outputDataConfig: import_smithy_client._json,
    roleArn: import_smithy_client.expectString,
    status: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_GetEvaluationJobCommand");
var de_GetFoundationModelCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    modelDetails: import_smithy_client._json
  });
  Object.assign(contents, doc);
  return contents;
}, "de_GetFoundationModelCommand");
var de_GetFoundationModelAvailabilityCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    agreementAvailability: import_smithy_client._json,
    authorizationStatus: import_smithy_client.expectString,
    entitlementAvailability: import_smithy_client.expectString,
    modelId: import_smithy_client.expectString,
    regionAvailability: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_GetFoundationModelAvailabilityCommand");
var de_GetGuardrailCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    blockedInputMessaging: import_smithy_client.expectString,
    blockedOutputsMessaging: import_smithy_client.expectString,
    contentPolicy: import_smithy_client._json,
    contextualGroundingPolicy: /* @__PURE__ */ __name((_) => de_GuardrailContextualGroundingPolicy(_, context), "contextualGroundingPolicy"),
    createdAt: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "createdAt"),
    crossRegionDetails: import_smithy_client._json,
    description: import_smithy_client.expectString,
    failureRecommendations: import_smithy_client._json,
    guardrailArn: import_smithy_client.expectString,
    guardrailId: import_smithy_client.expectString,
    kmsKeyArn: import_smithy_client.expectString,
    name: import_smithy_client.expectString,
    sensitiveInformationPolicy: import_smithy_client._json,
    status: import_smithy_client.expectString,
    statusReasons: import_smithy_client._json,
    topicPolicy: import_smithy_client._json,
    updatedAt: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "updatedAt"),
    version: import_smithy_client.expectString,
    wordPolicy: import_smithy_client._json
  });
  Object.assign(contents, doc);
  return contents;
}, "de_GetGuardrailCommand");
var de_GetImportedModelCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    creationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "creationTime"),
    customModelUnits: import_smithy_client._json,
    instructSupported: import_smithy_client.expectBoolean,
    jobArn: import_smithy_client.expectString,
    jobName: import_smithy_client.expectString,
    modelArchitecture: import_smithy_client.expectString,
    modelArn: import_smithy_client.expectString,
    modelDataSource: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)((0, import_core2.awsExpectUnion)(_)), "modelDataSource"),
    modelKmsKeyArn: import_smithy_client.expectString,
    modelName: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_GetImportedModelCommand");
var de_GetInferenceProfileCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    createdAt: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "createdAt"),
    description: import_smithy_client.expectString,
    inferenceProfileArn: import_smithy_client.expectString,
    inferenceProfileId: import_smithy_client.expectString,
    inferenceProfileName: import_smithy_client.expectString,
    models: import_smithy_client._json,
    status: import_smithy_client.expectString,
    type: import_smithy_client.expectString,
    updatedAt: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "updatedAt")
  });
  Object.assign(contents, doc);
  return contents;
}, "de_GetInferenceProfileCommand");
var de_GetMarketplaceModelEndpointCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    marketplaceModelEndpoint: /* @__PURE__ */ __name((_) => de_MarketplaceModelEndpoint(_, context), "marketplaceModelEndpoint")
  });
  Object.assign(contents, doc);
  return contents;
}, "de_GetMarketplaceModelEndpointCommand");
var de_GetModelCopyJobCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    creationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "creationTime"),
    failureMessage: import_smithy_client.expectString,
    jobArn: import_smithy_client.expectString,
    sourceAccountId: import_smithy_client.expectString,
    sourceModelArn: import_smithy_client.expectString,
    sourceModelName: import_smithy_client.expectString,
    status: import_smithy_client.expectString,
    targetModelArn: import_smithy_client.expectString,
    targetModelKmsKeyArn: import_smithy_client.expectString,
    targetModelName: import_smithy_client.expectString,
    targetModelTags: import_smithy_client._json
  });
  Object.assign(contents, doc);
  return contents;
}, "de_GetModelCopyJobCommand");
var de_GetModelCustomizationJobCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    baseModelArn: import_smithy_client.expectString,
    clientRequestToken: import_smithy_client.expectString,
    creationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "creationTime"),
    customizationConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)((0, import_core2.awsExpectUnion)(_)), "customizationConfig"),
    customizationType: import_smithy_client.expectString,
    endTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "endTime"),
    failureMessage: import_smithy_client.expectString,
    hyperParameters: import_smithy_client._json,
    jobArn: import_smithy_client.expectString,
    jobName: import_smithy_client.expectString,
    lastModifiedTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "lastModifiedTime"),
    outputDataConfig: import_smithy_client._json,
    outputModelArn: import_smithy_client.expectString,
    outputModelKmsKeyArn: import_smithy_client.expectString,
    outputModelName: import_smithy_client.expectString,
    roleArn: import_smithy_client.expectString,
    status: import_smithy_client.expectString,
    statusDetails: /* @__PURE__ */ __name((_) => de_StatusDetails(_, context), "statusDetails"),
    trainingDataConfig: import_smithy_client._json,
    trainingMetrics: /* @__PURE__ */ __name((_) => de_TrainingMetrics(_, context), "trainingMetrics"),
    validationDataConfig: import_smithy_client._json,
    validationMetrics: /* @__PURE__ */ __name((_) => de_ValidationMetrics(_, context), "validationMetrics"),
    vpcConfig: import_smithy_client._json
  });
  Object.assign(contents, doc);
  return contents;
}, "de_GetModelCustomizationJobCommand");
var de_GetModelImportJobCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    creationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "creationTime"),
    endTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "endTime"),
    failureMessage: import_smithy_client.expectString,
    importedModelArn: import_smithy_client.expectString,
    importedModelKmsKeyArn: import_smithy_client.expectString,
    importedModelName: import_smithy_client.expectString,
    jobArn: import_smithy_client.expectString,
    jobName: import_smithy_client.expectString,
    lastModifiedTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "lastModifiedTime"),
    modelDataSource: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)((0, import_core2.awsExpectUnion)(_)), "modelDataSource"),
    roleArn: import_smithy_client.expectString,
    status: import_smithy_client.expectString,
    vpcConfig: import_smithy_client._json
  });
  Object.assign(contents, doc);
  return contents;
}, "de_GetModelImportJobCommand");
var de_GetModelInvocationJobCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    clientRequestToken: import_smithy_client.expectString,
    endTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "endTime"),
    inputDataConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)((0, import_core2.awsExpectUnion)(_)), "inputDataConfig"),
    jobArn: import_smithy_client.expectString,
    jobExpirationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "jobExpirationTime"),
    jobName: import_smithy_client.expectString,
    lastModifiedTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "lastModifiedTime"),
    message: import_smithy_client.expectString,
    modelId: import_smithy_client.expectString,
    outputDataConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)((0, import_core2.awsExpectUnion)(_)), "outputDataConfig"),
    roleArn: import_smithy_client.expectString,
    status: import_smithy_client.expectString,
    submitTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "submitTime"),
    timeoutDurationInHours: import_smithy_client.expectInt32,
    vpcConfig: import_smithy_client._json
  });
  Object.assign(contents, doc);
  return contents;
}, "de_GetModelInvocationJobCommand");
var de_GetModelInvocationLoggingConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    loggingConfig: import_smithy_client._json
  });
  Object.assign(contents, doc);
  return contents;
}, "de_GetModelInvocationLoggingConfigurationCommand");
var de_GetPromptRouterCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    createdAt: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "createdAt"),
    description: import_smithy_client.expectString,
    fallbackModel: import_smithy_client._json,
    models: import_smithy_client._json,
    promptRouterArn: import_smithy_client.expectString,
    promptRouterName: import_smithy_client.expectString,
    routingCriteria: /* @__PURE__ */ __name((_) => de_RoutingCriteria(_, context), "routingCriteria"),
    status: import_smithy_client.expectString,
    type: import_smithy_client.expectString,
    updatedAt: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "updatedAt")
  });
  Object.assign(contents, doc);
  return contents;
}, "de_GetPromptRouterCommand");
var de_GetProvisionedModelThroughputCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    commitmentDuration: import_smithy_client.expectString,
    commitmentExpirationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "commitmentExpirationTime"),
    creationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "creationTime"),
    desiredModelArn: import_smithy_client.expectString,
    desiredModelUnits: import_smithy_client.expectInt32,
    failureMessage: import_smithy_client.expectString,
    foundationModelArn: import_smithy_client.expectString,
    lastModifiedTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "lastModifiedTime"),
    modelArn: import_smithy_client.expectString,
    modelUnits: import_smithy_client.expectInt32,
    provisionedModelArn: import_smithy_client.expectString,
    provisionedModelName: import_smithy_client.expectString,
    status: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_GetProvisionedModelThroughputCommand");
var de_GetUseCaseForModelAccessCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    formData: context.base64Decoder
  });
  Object.assign(contents, doc);
  return contents;
}, "de_GetUseCaseForModelAccessCommand");
var de_ListCustomModelsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    modelSummaries: /* @__PURE__ */ __name((_) => de_CustomModelSummaryList(_, context), "modelSummaries"),
    nextToken: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_ListCustomModelsCommand");
var de_ListEvaluationJobsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    jobSummaries: /* @__PURE__ */ __name((_) => de_EvaluationSummaries(_, context), "jobSummaries"),
    nextToken: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_ListEvaluationJobsCommand");
var de_ListFoundationModelAgreementOffersCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    modelId: import_smithy_client.expectString,
    offers: import_smithy_client._json
  });
  Object.assign(contents, doc);
  return contents;
}, "de_ListFoundationModelAgreementOffersCommand");
var de_ListFoundationModelsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    modelSummaries: import_smithy_client._json
  });
  Object.assign(contents, doc);
  return contents;
}, "de_ListFoundationModelsCommand");
var de_ListGuardrailsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    guardrails: /* @__PURE__ */ __name((_) => de_GuardrailSummaries(_, context), "guardrails"),
    nextToken: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_ListGuardrailsCommand");
var de_ListImportedModelsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    modelSummaries: /* @__PURE__ */ __name((_) => de_ImportedModelSummaryList(_, context), "modelSummaries"),
    nextToken: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_ListImportedModelsCommand");
var de_ListInferenceProfilesCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    inferenceProfileSummaries: /* @__PURE__ */ __name((_) => de_InferenceProfileSummaries(_, context), "inferenceProfileSummaries"),
    nextToken: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_ListInferenceProfilesCommand");
var de_ListMarketplaceModelEndpointsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    marketplaceModelEndpoints: /* @__PURE__ */ __name((_) => de_MarketplaceModelEndpointSummaries(_, context), "marketplaceModelEndpoints"),
    nextToken: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_ListMarketplaceModelEndpointsCommand");
var de_ListModelCopyJobsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    modelCopyJobSummaries: /* @__PURE__ */ __name((_) => de_ModelCopyJobSummaries(_, context), "modelCopyJobSummaries"),
    nextToken: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_ListModelCopyJobsCommand");
var de_ListModelCustomizationJobsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    modelCustomizationJobSummaries: /* @__PURE__ */ __name((_) => de_ModelCustomizationJobSummaries(_, context), "modelCustomizationJobSummaries"),
    nextToken: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_ListModelCustomizationJobsCommand");
var de_ListModelImportJobsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    modelImportJobSummaries: /* @__PURE__ */ __name((_) => de_ModelImportJobSummaries(_, context), "modelImportJobSummaries"),
    nextToken: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_ListModelImportJobsCommand");
var de_ListModelInvocationJobsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    invocationJobSummaries: /* @__PURE__ */ __name((_) => de_ModelInvocationJobSummaries(_, context), "invocationJobSummaries"),
    nextToken: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_ListModelInvocationJobsCommand");
var de_ListPromptRoutersCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    nextToken: import_smithy_client.expectString,
    promptRouterSummaries: /* @__PURE__ */ __name((_) => de_PromptRouterSummaries(_, context), "promptRouterSummaries")
  });
  Object.assign(contents, doc);
  return contents;
}, "de_ListPromptRoutersCommand");
var de_ListProvisionedModelThroughputsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    nextToken: import_smithy_client.expectString,
    provisionedModelSummaries: /* @__PURE__ */ __name((_) => de_ProvisionedModelSummaries(_, context), "provisionedModelSummaries")
  });
  Object.assign(contents, doc);
  return contents;
}, "de_ListProvisionedModelThroughputsCommand");
var de_ListTagsForResourceCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    tags: import_smithy_client._json
  });
  Object.assign(contents, doc);
  return contents;
}, "de_ListTagsForResourceCommand");
var de_PutModelInvocationLoggingConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutModelInvocationLoggingConfigurationCommand");
var de_PutUseCaseForModelAccessCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 201 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutUseCaseForModelAccessCommand");
var de_RegisterMarketplaceModelEndpointCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    marketplaceModelEndpoint: /* @__PURE__ */ __name((_) => de_MarketplaceModelEndpoint(_, context), "marketplaceModelEndpoint")
  });
  Object.assign(contents, doc);
  return contents;
}, "de_RegisterMarketplaceModelEndpointCommand");
var de_StopEvaluationJobCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_StopEvaluationJobCommand");
var de_StopModelCustomizationJobCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_StopModelCustomizationJobCommand");
var de_StopModelInvocationJobCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_StopModelInvocationJobCommand");
var de_TagResourceCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_TagResourceCommand");
var de_UntagResourceCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_UntagResourceCommand");
var de_UpdateGuardrailCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 202 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    guardrailArn: import_smithy_client.expectString,
    guardrailId: import_smithy_client.expectString,
    updatedAt: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "updatedAt"),
    version: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  return contents;
}, "de_UpdateGuardrailCommand");
var de_UpdateMarketplaceModelEndpointCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    marketplaceModelEndpoint: /* @__PURE__ */ __name((_) => de_MarketplaceModelEndpoint(_, context), "marketplaceModelEndpoint")
  });
  Object.assign(contents, doc);
  return contents;
}, "de_UpdateMarketplaceModelEndpointCommand");
var de_UpdateProvisionedModelThroughputCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_UpdateProvisionedModelThroughputCommand");
var de_CommandError = /* @__PURE__ */ __name(async (output, context) => {
  const parsedOutput = {
    ...output,
    body: await (0, import_core2.parseJsonErrorBody)(output.body, context)
  };
  const errorCode = (0, import_core2.loadRestJsonErrorCode)(output, parsedOutput.body);
  switch (errorCode) {
    case "AccessDeniedException":
    case "com.amazonaws.bedrock#AccessDeniedException":
      throw await de_AccessDeniedExceptionRes(parsedOutput, context);
    case "ConflictException":
    case "com.amazonaws.bedrock#ConflictException":
      throw await de_ConflictExceptionRes(parsedOutput, context);
    case "InternalServerException":
    case "com.amazonaws.bedrock#InternalServerException":
      throw await de_InternalServerExceptionRes(parsedOutput, context);
    case "ResourceNotFoundException":
    case "com.amazonaws.bedrock#ResourceNotFoundException":
      throw await de_ResourceNotFoundExceptionRes(parsedOutput, context);
    case "ThrottlingException":
    case "com.amazonaws.bedrock#ThrottlingException":
      throw await de_ThrottlingExceptionRes(parsedOutput, context);
    case "ValidationException":
    case "com.amazonaws.bedrock#ValidationException":
      throw await de_ValidationExceptionRes(parsedOutput, context);
    case "ServiceQuotaExceededException":
    case "com.amazonaws.bedrock#ServiceQuotaExceededException":
      throw await de_ServiceQuotaExceededExceptionRes(parsedOutput, context);
    case "TooManyTagsException":
    case "com.amazonaws.bedrock#TooManyTagsException":
      throw await de_TooManyTagsExceptionRes(parsedOutput, context);
    case "ServiceUnavailableException":
    case "com.amazonaws.bedrock#ServiceUnavailableException":
      throw await de_ServiceUnavailableExceptionRes(parsedOutput, context);
    default:
      const parsedBody = parsedOutput.body;
      return throwDefaultError({
        output,
        parsedBody,
        errorCode
      });
  }
}, "de_CommandError");
var throwDefaultError = (0, import_smithy_client.withBaseException)(BedrockServiceException);
var de_AccessDeniedExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const doc = (0, import_smithy_client.take)(data, {
    message: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  const exception = new AccessDeniedException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_AccessDeniedExceptionRes");
var de_ConflictExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const doc = (0, import_smithy_client.take)(data, {
    message: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  const exception = new ConflictException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_ConflictExceptionRes");
var de_InternalServerExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const doc = (0, import_smithy_client.take)(data, {
    message: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  const exception = new InternalServerException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_InternalServerExceptionRes");
var de_ResourceNotFoundExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const doc = (0, import_smithy_client.take)(data, {
    message: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  const exception = new ResourceNotFoundException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_ResourceNotFoundExceptionRes");
var de_ServiceQuotaExceededExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const doc = (0, import_smithy_client.take)(data, {
    message: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  const exception = new ServiceQuotaExceededException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_ServiceQuotaExceededExceptionRes");
var de_ServiceUnavailableExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const doc = (0, import_smithy_client.take)(data, {
    message: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  const exception = new ServiceUnavailableException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_ServiceUnavailableExceptionRes");
var de_ThrottlingExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const doc = (0, import_smithy_client.take)(data, {
    message: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  const exception = new ThrottlingException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_ThrottlingExceptionRes");
var de_TooManyTagsExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const doc = (0, import_smithy_client.take)(data, {
    message: import_smithy_client.expectString,
    resourceName: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  const exception = new TooManyTagsException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_TooManyTagsExceptionRes");
var de_ValidationExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const doc = (0, import_smithy_client.take)(data, {
    message: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  const exception = new ValidationException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_ValidationExceptionRes");
var se_AdditionalModelRequestFields = /* @__PURE__ */ __name((input, context) => {
  return Object.entries(input).reduce((acc, [key, value]) => {
    if (value === null) {
      return acc;
    }
    acc[key] = se_AdditionalModelRequestFieldsValue(value, context);
    return acc;
  }, {});
}, "se_AdditionalModelRequestFields");
var se_AdditionalModelRequestFieldsValue = /* @__PURE__ */ __name((input, context) => {
  return input;
}, "se_AdditionalModelRequestFieldsValue");
var se_AutomatedEvaluationConfig = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    customMetricConfig: /* @__PURE__ */ __name((_) => se_AutomatedEvaluationCustomMetricConfig(_, context), "customMetricConfig"),
    datasetMetricConfigs: import_smithy_client._json,
    evaluatorModelConfig: import_smithy_client._json
  });
}, "se_AutomatedEvaluationConfig");
var se_AutomatedEvaluationCustomMetricConfig = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    customMetrics: /* @__PURE__ */ __name((_) => se_AutomatedEvaluationCustomMetrics(_, context), "customMetrics"),
    evaluatorModelConfig: import_smithy_client._json
  });
}, "se_AutomatedEvaluationCustomMetricConfig");
var se_AutomatedEvaluationCustomMetrics = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    return se_AutomatedEvaluationCustomMetricSource(entry, context);
  });
}, "se_AutomatedEvaluationCustomMetrics");
var se_AutomatedEvaluationCustomMetricSource = /* @__PURE__ */ __name((input, context) => {
  return AutomatedEvaluationCustomMetricSource.visit(input, {
    customMetricDefinition: /* @__PURE__ */ __name((value) => ({ customMetricDefinition: se_CustomMetricDefinition(value, context) }), "customMetricDefinition"),
    _: /* @__PURE__ */ __name((name, value) => ({ [name]: value }), "_")
  });
}, "se_AutomatedEvaluationCustomMetricSource");
var se_ByteContentDoc = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    contentType: [],
    data: context.base64Encoder,
    identifier: []
  });
}, "se_ByteContentDoc");
var se_CustomMetricDefinition = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    instructions: [],
    name: [],
    ratingScale: /* @__PURE__ */ __name((_) => se_RatingScale(_, context), "ratingScale")
  });
}, "se_CustomMetricDefinition");
var se_EvaluationConfig = /* @__PURE__ */ __name((input, context) => {
  return EvaluationConfig.visit(input, {
    automated: /* @__PURE__ */ __name((value) => ({ automated: se_AutomatedEvaluationConfig(value, context) }), "automated"),
    human: /* @__PURE__ */ __name((value) => ({ human: (0, import_smithy_client._json)(value) }), "human"),
    _: /* @__PURE__ */ __name((name, value) => ({ [name]: value }), "_")
  });
}, "se_EvaluationConfig");
var se_EvaluationInferenceConfig = /* @__PURE__ */ __name((input, context) => {
  return EvaluationInferenceConfig.visit(input, {
    models: /* @__PURE__ */ __name((value) => ({ models: (0, import_smithy_client._json)(value) }), "models"),
    ragConfigs: /* @__PURE__ */ __name((value) => ({ ragConfigs: se_RagConfigs(value, context) }), "ragConfigs"),
    _: /* @__PURE__ */ __name((name, value) => ({ [name]: value }), "_")
  });
}, "se_EvaluationInferenceConfig");
var se_ExternalSource = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    byteContent: /* @__PURE__ */ __name((_) => se_ByteContentDoc(_, context), "byteContent"),
    s3Location: import_smithy_client._json,
    sourceType: []
  });
}, "se_ExternalSource");
var se_ExternalSources = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    return se_ExternalSource(entry, context);
  });
}, "se_ExternalSources");
var se_ExternalSourcesGenerationConfiguration = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    additionalModelRequestFields: /* @__PURE__ */ __name((_) => se_AdditionalModelRequestFields(_, context), "additionalModelRequestFields"),
    guardrailConfiguration: import_smithy_client._json,
    kbInferenceConfig: /* @__PURE__ */ __name((_) => se_KbInferenceConfig(_, context), "kbInferenceConfig"),
    promptTemplate: import_smithy_client._json
  });
}, "se_ExternalSourcesGenerationConfiguration");
var se_ExternalSourcesRetrieveAndGenerateConfiguration = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    generationConfiguration: /* @__PURE__ */ __name((_) => se_ExternalSourcesGenerationConfiguration(_, context), "generationConfiguration"),
    modelArn: [],
    sources: /* @__PURE__ */ __name((_) => se_ExternalSources(_, context), "sources")
  });
}, "se_ExternalSourcesRetrieveAndGenerateConfiguration");
var se_FilterAttribute = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    key: [],
    value: /* @__PURE__ */ __name((_) => se_FilterValue(_, context), "value")
  });
}, "se_FilterAttribute");
var se_FilterValue = /* @__PURE__ */ __name((input, context) => {
  return input;
}, "se_FilterValue");
var se_GenerationConfiguration = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    additionalModelRequestFields: /* @__PURE__ */ __name((_) => se_AdditionalModelRequestFields(_, context), "additionalModelRequestFields"),
    guardrailConfiguration: import_smithy_client._json,
    kbInferenceConfig: /* @__PURE__ */ __name((_) => se_KbInferenceConfig(_, context), "kbInferenceConfig"),
    promptTemplate: import_smithy_client._json
  });
}, "se_GenerationConfiguration");
var se_GuardrailContextualGroundingFilterConfig = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    action: [],
    enabled: [],
    threshold: import_smithy_client.serializeFloat,
    type: []
  });
}, "se_GuardrailContextualGroundingFilterConfig");
var se_GuardrailContextualGroundingFiltersConfig = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    return se_GuardrailContextualGroundingFilterConfig(entry, context);
  });
}, "se_GuardrailContextualGroundingFiltersConfig");
var se_GuardrailContextualGroundingPolicyConfig = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    filtersConfig: /* @__PURE__ */ __name((_) => se_GuardrailContextualGroundingFiltersConfig(_, context), "filtersConfig")
  });
}, "se_GuardrailContextualGroundingPolicyConfig");
var se_KbInferenceConfig = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    textInferenceConfig: /* @__PURE__ */ __name((_) => se_TextInferenceConfig(_, context), "textInferenceConfig")
  });
}, "se_KbInferenceConfig");
var se_KnowledgeBaseConfig = /* @__PURE__ */ __name((input, context) => {
  return KnowledgeBaseConfig.visit(input, {
    retrieveAndGenerateConfig: /* @__PURE__ */ __name((value) => ({
      retrieveAndGenerateConfig: se_RetrieveAndGenerateConfiguration(value, context)
    }), "retrieveAndGenerateConfig"),
    retrieveConfig: /* @__PURE__ */ __name((value) => ({ retrieveConfig: se_RetrieveConfig(value, context) }), "retrieveConfig"),
    _: /* @__PURE__ */ __name((name, value) => ({ [name]: value }), "_")
  });
}, "se_KnowledgeBaseConfig");
var se_KnowledgeBaseRetrievalConfiguration = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    vectorSearchConfiguration: /* @__PURE__ */ __name((_) => se_KnowledgeBaseVectorSearchConfiguration(_, context), "vectorSearchConfiguration")
  });
}, "se_KnowledgeBaseRetrievalConfiguration");
var se_KnowledgeBaseRetrieveAndGenerateConfiguration = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    generationConfiguration: /* @__PURE__ */ __name((_) => se_GenerationConfiguration(_, context), "generationConfiguration"),
    knowledgeBaseId: [],
    modelArn: [],
    orchestrationConfiguration: import_smithy_client._json,
    retrievalConfiguration: /* @__PURE__ */ __name((_) => se_KnowledgeBaseRetrievalConfiguration(_, context), "retrievalConfiguration")
  });
}, "se_KnowledgeBaseRetrieveAndGenerateConfiguration");
var se_KnowledgeBaseVectorSearchConfiguration = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    filter: /* @__PURE__ */ __name((_) => se_RetrievalFilter(_, context), "filter"),
    implicitFilterConfiguration: import_smithy_client._json,
    numberOfResults: [],
    overrideSearchType: [],
    rerankingConfiguration: /* @__PURE__ */ __name((_) => se_VectorSearchRerankingConfiguration(_, context), "rerankingConfiguration")
  });
}, "se_KnowledgeBaseVectorSearchConfiguration");
var se_RAGConfig = /* @__PURE__ */ __name((input, context) => {
  return RAGConfig.visit(input, {
    knowledgeBaseConfig: /* @__PURE__ */ __name((value) => ({ knowledgeBaseConfig: se_KnowledgeBaseConfig(value, context) }), "knowledgeBaseConfig"),
    precomputedRagSourceConfig: /* @__PURE__ */ __name((value) => ({ precomputedRagSourceConfig: (0, import_smithy_client._json)(value) }), "precomputedRagSourceConfig"),
    _: /* @__PURE__ */ __name((name, value) => ({ [name]: value }), "_")
  });
}, "se_RAGConfig");
var se_RagConfigs = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    return se_RAGConfig(entry, context);
  });
}, "se_RagConfigs");
var se_RatingScale = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    return se_RatingScaleItem(entry, context);
  });
}, "se_RatingScale");
var se_RatingScaleItem = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    definition: [],
    value: /* @__PURE__ */ __name((_) => se_RatingScaleItemValue(_, context), "value")
  });
}, "se_RatingScaleItem");
var se_RatingScaleItemValue = /* @__PURE__ */ __name((input, context) => {
  return RatingScaleItemValue.visit(input, {
    floatValue: /* @__PURE__ */ __name((value) => ({ floatValue: (0, import_smithy_client.serializeFloat)(value) }), "floatValue"),
    stringValue: /* @__PURE__ */ __name((value) => ({ stringValue: value }), "stringValue"),
    _: /* @__PURE__ */ __name((name, value) => ({ [name]: value }), "_")
  });
}, "se_RatingScaleItemValue");
var se_RetrievalFilter = /* @__PURE__ */ __name((input, context) => {
  return RetrievalFilter.visit(input, {
    andAll: /* @__PURE__ */ __name((value) => ({ andAll: se_RetrievalFilterList(value, context) }), "andAll"),
    equals: /* @__PURE__ */ __name((value) => ({ equals: se_FilterAttribute(value, context) }), "equals"),
    greaterThan: /* @__PURE__ */ __name((value) => ({ greaterThan: se_FilterAttribute(value, context) }), "greaterThan"),
    greaterThanOrEquals: /* @__PURE__ */ __name((value) => ({ greaterThanOrEquals: se_FilterAttribute(value, context) }), "greaterThanOrEquals"),
    in: /* @__PURE__ */ __name((value) => ({ in: se_FilterAttribute(value, context) }), "in"),
    lessThan: /* @__PURE__ */ __name((value) => ({ lessThan: se_FilterAttribute(value, context) }), "lessThan"),
    lessThanOrEquals: /* @__PURE__ */ __name((value) => ({ lessThanOrEquals: se_FilterAttribute(value, context) }), "lessThanOrEquals"),
    listContains: /* @__PURE__ */ __name((value) => ({ listContains: se_FilterAttribute(value, context) }), "listContains"),
    notEquals: /* @__PURE__ */ __name((value) => ({ notEquals: se_FilterAttribute(value, context) }), "notEquals"),
    notIn: /* @__PURE__ */ __name((value) => ({ notIn: se_FilterAttribute(value, context) }), "notIn"),
    orAll: /* @__PURE__ */ __name((value) => ({ orAll: se_RetrievalFilterList(value, context) }), "orAll"),
    startsWith: /* @__PURE__ */ __name((value) => ({ startsWith: se_FilterAttribute(value, context) }), "startsWith"),
    stringContains: /* @__PURE__ */ __name((value) => ({ stringContains: se_FilterAttribute(value, context) }), "stringContains"),
    _: /* @__PURE__ */ __name((name, value) => ({ [name]: value }), "_")
  });
}, "se_RetrievalFilter");
var se_RetrievalFilterList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    return se_RetrievalFilter(entry, context);
  });
}, "se_RetrievalFilterList");
var se_RetrieveAndGenerateConfiguration = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    externalSourcesConfiguration: /* @__PURE__ */ __name((_) => se_ExternalSourcesRetrieveAndGenerateConfiguration(_, context), "externalSourcesConfiguration"),
    knowledgeBaseConfiguration: /* @__PURE__ */ __name((_) => se_KnowledgeBaseRetrieveAndGenerateConfiguration(_, context), "knowledgeBaseConfiguration"),
    type: []
  });
}, "se_RetrieveAndGenerateConfiguration");
var se_RetrieveConfig = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    knowledgeBaseId: [],
    knowledgeBaseRetrievalConfiguration: /* @__PURE__ */ __name((_) => se_KnowledgeBaseRetrievalConfiguration(_, context), "knowledgeBaseRetrievalConfiguration")
  });
}, "se_RetrieveConfig");
var se_RoutingCriteria = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    responseQualityDifference: import_smithy_client.serializeFloat
  });
}, "se_RoutingCriteria");
var se_TextInferenceConfig = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    maxTokens: [],
    stopSequences: import_smithy_client._json,
    temperature: import_smithy_client.serializeFloat,
    topP: import_smithy_client.serializeFloat
  });
}, "se_TextInferenceConfig");
var se_VectorSearchBedrockRerankingConfiguration = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    metadataConfiguration: import_smithy_client._json,
    modelConfiguration: /* @__PURE__ */ __name((_) => se_VectorSearchBedrockRerankingModelConfiguration(_, context), "modelConfiguration"),
    numberOfRerankedResults: []
  });
}, "se_VectorSearchBedrockRerankingConfiguration");
var se_VectorSearchBedrockRerankingModelConfiguration = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    additionalModelRequestFields: /* @__PURE__ */ __name((_) => se_AdditionalModelRequestFields(_, context), "additionalModelRequestFields"),
    modelArn: []
  });
}, "se_VectorSearchBedrockRerankingModelConfiguration");
var se_VectorSearchRerankingConfiguration = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    bedrockRerankingConfiguration: /* @__PURE__ */ __name((_) => se_VectorSearchBedrockRerankingConfiguration(_, context), "bedrockRerankingConfiguration"),
    type: []
  });
}, "se_VectorSearchRerankingConfiguration");
var de_AdditionalModelRequestFields = /* @__PURE__ */ __name((output, context) => {
  return Object.entries(output).reduce((acc, [key, value]) => {
    if (value === null) {
      return acc;
    }
    acc[key] = de_AdditionalModelRequestFieldsValue(value, context);
    return acc;
  }, {});
}, "de_AdditionalModelRequestFields");
var de_AdditionalModelRequestFieldsValue = /* @__PURE__ */ __name((output, context) => {
  return output;
}, "de_AdditionalModelRequestFieldsValue");
var de_AutomatedEvaluationConfig = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    customMetricConfig: /* @__PURE__ */ __name((_) => de_AutomatedEvaluationCustomMetricConfig(_, context), "customMetricConfig"),
    datasetMetricConfigs: import_smithy_client._json,
    evaluatorModelConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)((0, import_core2.awsExpectUnion)(_)), "evaluatorModelConfig")
  });
}, "de_AutomatedEvaluationConfig");
var de_AutomatedEvaluationCustomMetricConfig = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    customMetrics: /* @__PURE__ */ __name((_) => de_AutomatedEvaluationCustomMetrics(_, context), "customMetrics"),
    evaluatorModelConfig: import_smithy_client._json
  });
}, "de_AutomatedEvaluationCustomMetricConfig");
var de_AutomatedEvaluationCustomMetrics = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_AutomatedEvaluationCustomMetricSource((0, import_core2.awsExpectUnion)(entry), context);
  });
  return retVal;
}, "de_AutomatedEvaluationCustomMetrics");
var de_AutomatedEvaluationCustomMetricSource = /* @__PURE__ */ __name((output, context) => {
  if (output.customMetricDefinition != null) {
    return {
      customMetricDefinition: de_CustomMetricDefinition(output.customMetricDefinition, context)
    };
  }
  return { $unknown: Object.entries(output)[0] };
}, "de_AutomatedEvaluationCustomMetricSource");
var de_ByteContentDoc = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    contentType: import_smithy_client.expectString,
    data: context.base64Decoder,
    identifier: import_smithy_client.expectString
  });
}, "de_ByteContentDoc");
var de_CustomMetricDefinition = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    instructions: import_smithy_client.expectString,
    name: import_smithy_client.expectString,
    ratingScale: /* @__PURE__ */ __name((_) => de_RatingScale(_, context), "ratingScale")
  });
}, "de_CustomMetricDefinition");
var de_CustomModelSummary = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    baseModelArn: import_smithy_client.expectString,
    baseModelName: import_smithy_client.expectString,
    creationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "creationTime"),
    customizationType: import_smithy_client.expectString,
    modelArn: import_smithy_client.expectString,
    modelName: import_smithy_client.expectString,
    modelStatus: import_smithy_client.expectString,
    ownerAccountId: import_smithy_client.expectString
  });
}, "de_CustomModelSummary");
var de_CustomModelSummaryList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_CustomModelSummary(entry, context);
  });
  return retVal;
}, "de_CustomModelSummaryList");
var de_DataProcessingDetails = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    creationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "creationTime"),
    lastModifiedTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "lastModifiedTime"),
    status: import_smithy_client.expectString
  });
}, "de_DataProcessingDetails");
var de_EvaluationConfig = /* @__PURE__ */ __name((output, context) => {
  if (output.automated != null) {
    return {
      automated: de_AutomatedEvaluationConfig(output.automated, context)
    };
  }
  if (output.human != null) {
    return {
      human: (0, import_smithy_client._json)(output.human)
    };
  }
  return { $unknown: Object.entries(output)[0] };
}, "de_EvaluationConfig");
var de_EvaluationInferenceConfig = /* @__PURE__ */ __name((output, context) => {
  if (output.models != null) {
    return {
      models: (0, import_smithy_client._json)(output.models)
    };
  }
  if (output.ragConfigs != null) {
    return {
      ragConfigs: de_RagConfigs(output.ragConfigs, context)
    };
  }
  return { $unknown: Object.entries(output)[0] };
}, "de_EvaluationInferenceConfig");
var de_EvaluationSummaries = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_EvaluationSummary(entry, context);
  });
  return retVal;
}, "de_EvaluationSummaries");
var de_EvaluationSummary = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    applicationType: import_smithy_client.expectString,
    creationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "creationTime"),
    customMetricsEvaluatorModelIdentifiers: import_smithy_client._json,
    evaluationTaskTypes: import_smithy_client._json,
    evaluatorModelIdentifiers: import_smithy_client._json,
    inferenceConfigSummary: import_smithy_client._json,
    jobArn: import_smithy_client.expectString,
    jobName: import_smithy_client.expectString,
    jobType: import_smithy_client.expectString,
    modelIdentifiers: import_smithy_client._json,
    ragIdentifiers: import_smithy_client._json,
    status: import_smithy_client.expectString
  });
}, "de_EvaluationSummary");
var de_ExternalSource = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    byteContent: /* @__PURE__ */ __name((_) => de_ByteContentDoc(_, context), "byteContent"),
    s3Location: import_smithy_client._json,
    sourceType: import_smithy_client.expectString
  });
}, "de_ExternalSource");
var de_ExternalSources = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_ExternalSource(entry, context);
  });
  return retVal;
}, "de_ExternalSources");
var de_ExternalSourcesGenerationConfiguration = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    additionalModelRequestFields: /* @__PURE__ */ __name((_) => de_AdditionalModelRequestFields(_, context), "additionalModelRequestFields"),
    guardrailConfiguration: import_smithy_client._json,
    kbInferenceConfig: /* @__PURE__ */ __name((_) => de_KbInferenceConfig(_, context), "kbInferenceConfig"),
    promptTemplate: import_smithy_client._json
  });
}, "de_ExternalSourcesGenerationConfiguration");
var de_ExternalSourcesRetrieveAndGenerateConfiguration = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    generationConfiguration: /* @__PURE__ */ __name((_) => de_ExternalSourcesGenerationConfiguration(_, context), "generationConfiguration"),
    modelArn: import_smithy_client.expectString,
    sources: /* @__PURE__ */ __name((_) => de_ExternalSources(_, context), "sources")
  });
}, "de_ExternalSourcesRetrieveAndGenerateConfiguration");
var de_FilterAttribute = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    key: import_smithy_client.expectString,
    value: /* @__PURE__ */ __name((_) => de_FilterValue(_, context), "value")
  });
}, "de_FilterAttribute");
var de_FilterValue = /* @__PURE__ */ __name((output, context) => {
  return output;
}, "de_FilterValue");
var de_GenerationConfiguration = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    additionalModelRequestFields: /* @__PURE__ */ __name((_) => de_AdditionalModelRequestFields(_, context), "additionalModelRequestFields"),
    guardrailConfiguration: import_smithy_client._json,
    kbInferenceConfig: /* @__PURE__ */ __name((_) => de_KbInferenceConfig(_, context), "kbInferenceConfig"),
    promptTemplate: import_smithy_client._json
  });
}, "de_GenerationConfiguration");
var de_GuardrailContextualGroundingFilter = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    action: import_smithy_client.expectString,
    enabled: import_smithy_client.expectBoolean,
    threshold: import_smithy_client.limitedParseDouble,
    type: import_smithy_client.expectString
  });
}, "de_GuardrailContextualGroundingFilter");
var de_GuardrailContextualGroundingFilters = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_GuardrailContextualGroundingFilter(entry, context);
  });
  return retVal;
}, "de_GuardrailContextualGroundingFilters");
var de_GuardrailContextualGroundingPolicy = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    filters: /* @__PURE__ */ __name((_) => de_GuardrailContextualGroundingFilters(_, context), "filters")
  });
}, "de_GuardrailContextualGroundingPolicy");
var de_GuardrailSummaries = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_GuardrailSummary(entry, context);
  });
  return retVal;
}, "de_GuardrailSummaries");
var de_GuardrailSummary = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    arn: import_smithy_client.expectString,
    createdAt: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "createdAt"),
    crossRegionDetails: import_smithy_client._json,
    description: import_smithy_client.expectString,
    id: import_smithy_client.expectString,
    name: import_smithy_client.expectString,
    status: import_smithy_client.expectString,
    updatedAt: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "updatedAt"),
    version: import_smithy_client.expectString
  });
}, "de_GuardrailSummary");
var de_ImportedModelSummary = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    creationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "creationTime"),
    instructSupported: import_smithy_client.expectBoolean,
    modelArchitecture: import_smithy_client.expectString,
    modelArn: import_smithy_client.expectString,
    modelName: import_smithy_client.expectString
  });
}, "de_ImportedModelSummary");
var de_ImportedModelSummaryList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_ImportedModelSummary(entry, context);
  });
  return retVal;
}, "de_ImportedModelSummaryList");
var de_InferenceProfileSummaries = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_InferenceProfileSummary(entry, context);
  });
  return retVal;
}, "de_InferenceProfileSummaries");
var de_InferenceProfileSummary = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    createdAt: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "createdAt"),
    description: import_smithy_client.expectString,
    inferenceProfileArn: import_smithy_client.expectString,
    inferenceProfileId: import_smithy_client.expectString,
    inferenceProfileName: import_smithy_client.expectString,
    models: import_smithy_client._json,
    status: import_smithy_client.expectString,
    type: import_smithy_client.expectString,
    updatedAt: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "updatedAt")
  });
}, "de_InferenceProfileSummary");
var de_KbInferenceConfig = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    textInferenceConfig: /* @__PURE__ */ __name((_) => de_TextInferenceConfig(_, context), "textInferenceConfig")
  });
}, "de_KbInferenceConfig");
var de_KnowledgeBaseConfig = /* @__PURE__ */ __name((output, context) => {
  if (output.retrieveAndGenerateConfig != null) {
    return {
      retrieveAndGenerateConfig: de_RetrieveAndGenerateConfiguration(output.retrieveAndGenerateConfig, context)
    };
  }
  if (output.retrieveConfig != null) {
    return {
      retrieveConfig: de_RetrieveConfig(output.retrieveConfig, context)
    };
  }
  return { $unknown: Object.entries(output)[0] };
}, "de_KnowledgeBaseConfig");
var de_KnowledgeBaseRetrievalConfiguration = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    vectorSearchConfiguration: /* @__PURE__ */ __name((_) => de_KnowledgeBaseVectorSearchConfiguration(_, context), "vectorSearchConfiguration")
  });
}, "de_KnowledgeBaseRetrievalConfiguration");
var de_KnowledgeBaseRetrieveAndGenerateConfiguration = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    generationConfiguration: /* @__PURE__ */ __name((_) => de_GenerationConfiguration(_, context), "generationConfiguration"),
    knowledgeBaseId: import_smithy_client.expectString,
    modelArn: import_smithy_client.expectString,
    orchestrationConfiguration: import_smithy_client._json,
    retrievalConfiguration: /* @__PURE__ */ __name((_) => de_KnowledgeBaseRetrievalConfiguration(_, context), "retrievalConfiguration")
  });
}, "de_KnowledgeBaseRetrieveAndGenerateConfiguration");
var de_KnowledgeBaseVectorSearchConfiguration = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    filter: /* @__PURE__ */ __name((_) => de_RetrievalFilter((0, import_core2.awsExpectUnion)(_), context), "filter"),
    implicitFilterConfiguration: import_smithy_client._json,
    numberOfResults: import_smithy_client.expectInt32,
    overrideSearchType: import_smithy_client.expectString,
    rerankingConfiguration: /* @__PURE__ */ __name((_) => de_VectorSearchRerankingConfiguration(_, context), "rerankingConfiguration")
  });
}, "de_KnowledgeBaseVectorSearchConfiguration");
var de_MarketplaceModelEndpoint = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    createdAt: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "createdAt"),
    endpointArn: import_smithy_client.expectString,
    endpointConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)((0, import_core2.awsExpectUnion)(_)), "endpointConfig"),
    endpointStatus: import_smithy_client.expectString,
    endpointStatusMessage: import_smithy_client.expectString,
    modelSourceIdentifier: import_smithy_client.expectString,
    status: import_smithy_client.expectString,
    statusMessage: import_smithy_client.expectString,
    updatedAt: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "updatedAt")
  });
}, "de_MarketplaceModelEndpoint");
var de_MarketplaceModelEndpointSummaries = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_MarketplaceModelEndpointSummary(entry, context);
  });
  return retVal;
}, "de_MarketplaceModelEndpointSummaries");
var de_MarketplaceModelEndpointSummary = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    createdAt: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "createdAt"),
    endpointArn: import_smithy_client.expectString,
    modelSourceIdentifier: import_smithy_client.expectString,
    status: import_smithy_client.expectString,
    statusMessage: import_smithy_client.expectString,
    updatedAt: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "updatedAt")
  });
}, "de_MarketplaceModelEndpointSummary");
var de_ModelCopyJobSummaries = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_ModelCopyJobSummary(entry, context);
  });
  return retVal;
}, "de_ModelCopyJobSummaries");
var de_ModelCopyJobSummary = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    creationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "creationTime"),
    failureMessage: import_smithy_client.expectString,
    jobArn: import_smithy_client.expectString,
    sourceAccountId: import_smithy_client.expectString,
    sourceModelArn: import_smithy_client.expectString,
    sourceModelName: import_smithy_client.expectString,
    status: import_smithy_client.expectString,
    targetModelArn: import_smithy_client.expectString,
    targetModelKmsKeyArn: import_smithy_client.expectString,
    targetModelName: import_smithy_client.expectString,
    targetModelTags: import_smithy_client._json
  });
}, "de_ModelCopyJobSummary");
var de_ModelCustomizationJobSummaries = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_ModelCustomizationJobSummary(entry, context);
  });
  return retVal;
}, "de_ModelCustomizationJobSummaries");
var de_ModelCustomizationJobSummary = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    baseModelArn: import_smithy_client.expectString,
    creationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "creationTime"),
    customModelArn: import_smithy_client.expectString,
    customModelName: import_smithy_client.expectString,
    customizationType: import_smithy_client.expectString,
    endTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "endTime"),
    jobArn: import_smithy_client.expectString,
    jobName: import_smithy_client.expectString,
    lastModifiedTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "lastModifiedTime"),
    status: import_smithy_client.expectString,
    statusDetails: /* @__PURE__ */ __name((_) => de_StatusDetails(_, context), "statusDetails")
  });
}, "de_ModelCustomizationJobSummary");
var de_ModelImportJobSummaries = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_ModelImportJobSummary(entry, context);
  });
  return retVal;
}, "de_ModelImportJobSummaries");
var de_ModelImportJobSummary = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    creationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "creationTime"),
    endTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "endTime"),
    importedModelArn: import_smithy_client.expectString,
    importedModelName: import_smithy_client.expectString,
    jobArn: import_smithy_client.expectString,
    jobName: import_smithy_client.expectString,
    lastModifiedTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "lastModifiedTime"),
    status: import_smithy_client.expectString
  });
}, "de_ModelImportJobSummary");
var de_ModelInvocationJobSummaries = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_ModelInvocationJobSummary(entry, context);
  });
  return retVal;
}, "de_ModelInvocationJobSummaries");
var de_ModelInvocationJobSummary = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    clientRequestToken: import_smithy_client.expectString,
    endTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "endTime"),
    inputDataConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)((0, import_core2.awsExpectUnion)(_)), "inputDataConfig"),
    jobArn: import_smithy_client.expectString,
    jobExpirationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "jobExpirationTime"),
    jobName: import_smithy_client.expectString,
    lastModifiedTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "lastModifiedTime"),
    message: import_smithy_client.expectString,
    modelId: import_smithy_client.expectString,
    outputDataConfig: /* @__PURE__ */ __name((_) => (0, import_smithy_client._json)((0, import_core2.awsExpectUnion)(_)), "outputDataConfig"),
    roleArn: import_smithy_client.expectString,
    status: import_smithy_client.expectString,
    submitTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "submitTime"),
    timeoutDurationInHours: import_smithy_client.expectInt32,
    vpcConfig: import_smithy_client._json
  });
}, "de_ModelInvocationJobSummary");
var de_PromptRouterSummaries = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_PromptRouterSummary(entry, context);
  });
  return retVal;
}, "de_PromptRouterSummaries");
var de_PromptRouterSummary = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    createdAt: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "createdAt"),
    description: import_smithy_client.expectString,
    fallbackModel: import_smithy_client._json,
    models: import_smithy_client._json,
    promptRouterArn: import_smithy_client.expectString,
    promptRouterName: import_smithy_client.expectString,
    routingCriteria: /* @__PURE__ */ __name((_) => de_RoutingCriteria(_, context), "routingCriteria"),
    status: import_smithy_client.expectString,
    type: import_smithy_client.expectString,
    updatedAt: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "updatedAt")
  });
}, "de_PromptRouterSummary");
var de_ProvisionedModelSummaries = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_ProvisionedModelSummary(entry, context);
  });
  return retVal;
}, "de_ProvisionedModelSummaries");
var de_ProvisionedModelSummary = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    commitmentDuration: import_smithy_client.expectString,
    commitmentExpirationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "commitmentExpirationTime"),
    creationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "creationTime"),
    desiredModelArn: import_smithy_client.expectString,
    desiredModelUnits: import_smithy_client.expectInt32,
    foundationModelArn: import_smithy_client.expectString,
    lastModifiedTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "lastModifiedTime"),
    modelArn: import_smithy_client.expectString,
    modelUnits: import_smithy_client.expectInt32,
    provisionedModelArn: import_smithy_client.expectString,
    provisionedModelName: import_smithy_client.expectString,
    status: import_smithy_client.expectString
  });
}, "de_ProvisionedModelSummary");
var de_RAGConfig = /* @__PURE__ */ __name((output, context) => {
  if (output.knowledgeBaseConfig != null) {
    return {
      knowledgeBaseConfig: de_KnowledgeBaseConfig((0, import_core2.awsExpectUnion)(output.knowledgeBaseConfig), context)
    };
  }
  if (output.precomputedRagSourceConfig != null) {
    return {
      precomputedRagSourceConfig: (0, import_smithy_client._json)((0, import_core2.awsExpectUnion)(output.precomputedRagSourceConfig))
    };
  }
  return { $unknown: Object.entries(output)[0] };
}, "de_RAGConfig");
var de_RagConfigs = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_RAGConfig((0, import_core2.awsExpectUnion)(entry), context);
  });
  return retVal;
}, "de_RagConfigs");
var de_RatingScale = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_RatingScaleItem(entry, context);
  });
  return retVal;
}, "de_RatingScale");
var de_RatingScaleItem = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    definition: import_smithy_client.expectString,
    value: /* @__PURE__ */ __name((_) => de_RatingScaleItemValue((0, import_core2.awsExpectUnion)(_), context), "value")
  });
}, "de_RatingScaleItem");
var de_RatingScaleItemValue = /* @__PURE__ */ __name((output, context) => {
  if ((0, import_smithy_client.limitedParseFloat32)(output.floatValue) !== void 0) {
    return { floatValue: (0, import_smithy_client.limitedParseFloat32)(output.floatValue) };
  }
  if ((0, import_smithy_client.expectString)(output.stringValue) !== void 0) {
    return { stringValue: (0, import_smithy_client.expectString)(output.stringValue) };
  }
  return { $unknown: Object.entries(output)[0] };
}, "de_RatingScaleItemValue");
var de_RetrievalFilter = /* @__PURE__ */ __name((output, context) => {
  if (output.andAll != null) {
    return {
      andAll: de_RetrievalFilterList(output.andAll, context)
    };
  }
  if (output.equals != null) {
    return {
      equals: de_FilterAttribute(output.equals, context)
    };
  }
  if (output.greaterThan != null) {
    return {
      greaterThan: de_FilterAttribute(output.greaterThan, context)
    };
  }
  if (output.greaterThanOrEquals != null) {
    return {
      greaterThanOrEquals: de_FilterAttribute(output.greaterThanOrEquals, context)
    };
  }
  if (output.in != null) {
    return {
      in: de_FilterAttribute(output.in, context)
    };
  }
  if (output.lessThan != null) {
    return {
      lessThan: de_FilterAttribute(output.lessThan, context)
    };
  }
  if (output.lessThanOrEquals != null) {
    return {
      lessThanOrEquals: de_FilterAttribute(output.lessThanOrEquals, context)
    };
  }
  if (output.listContains != null) {
    return {
      listContains: de_FilterAttribute(output.listContains, context)
    };
  }
  if (output.notEquals != null) {
    return {
      notEquals: de_FilterAttribute(output.notEquals, context)
    };
  }
  if (output.notIn != null) {
    return {
      notIn: de_FilterAttribute(output.notIn, context)
    };
  }
  if (output.orAll != null) {
    return {
      orAll: de_RetrievalFilterList(output.orAll, context)
    };
  }
  if (output.startsWith != null) {
    return {
      startsWith: de_FilterAttribute(output.startsWith, context)
    };
  }
  if (output.stringContains != null) {
    return {
      stringContains: de_FilterAttribute(output.stringContains, context)
    };
  }
  return { $unknown: Object.entries(output)[0] };
}, "de_RetrievalFilter");
var de_RetrievalFilterList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_RetrievalFilter((0, import_core2.awsExpectUnion)(entry), context);
  });
  return retVal;
}, "de_RetrievalFilterList");
var de_RetrieveAndGenerateConfiguration = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    externalSourcesConfiguration: /* @__PURE__ */ __name((_) => de_ExternalSourcesRetrieveAndGenerateConfiguration(_, context), "externalSourcesConfiguration"),
    knowledgeBaseConfiguration: /* @__PURE__ */ __name((_) => de_KnowledgeBaseRetrieveAndGenerateConfiguration(_, context), "knowledgeBaseConfiguration"),
    type: import_smithy_client.expectString
  });
}, "de_RetrieveAndGenerateConfiguration");
var de_RetrieveConfig = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    knowledgeBaseId: import_smithy_client.expectString,
    knowledgeBaseRetrievalConfiguration: /* @__PURE__ */ __name((_) => de_KnowledgeBaseRetrievalConfiguration(_, context), "knowledgeBaseRetrievalConfiguration")
  });
}, "de_RetrieveConfig");
var de_RoutingCriteria = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    responseQualityDifference: import_smithy_client.limitedParseDouble
  });
}, "de_RoutingCriteria");
var de_StatusDetails = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    dataProcessingDetails: /* @__PURE__ */ __name((_) => de_DataProcessingDetails(_, context), "dataProcessingDetails"),
    trainingDetails: /* @__PURE__ */ __name((_) => de_TrainingDetails(_, context), "trainingDetails"),
    validationDetails: /* @__PURE__ */ __name((_) => de_ValidationDetails(_, context), "validationDetails")
  });
}, "de_StatusDetails");
var de_TextInferenceConfig = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    maxTokens: import_smithy_client.expectInt32,
    stopSequences: import_smithy_client._json,
    temperature: import_smithy_client.limitedParseFloat32,
    topP: import_smithy_client.limitedParseFloat32
  });
}, "de_TextInferenceConfig");
var de_TrainingDetails = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    creationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "creationTime"),
    lastModifiedTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "lastModifiedTime"),
    status: import_smithy_client.expectString
  });
}, "de_TrainingDetails");
var de_TrainingMetrics = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    trainingLoss: import_smithy_client.limitedParseFloat32
  });
}, "de_TrainingMetrics");
var de_ValidationDetails = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    creationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "creationTime"),
    lastModifiedTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), "lastModifiedTime"),
    status: import_smithy_client.expectString
  });
}, "de_ValidationDetails");
var de_ValidationMetrics = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_ValidatorMetric(entry, context);
  });
  return retVal;
}, "de_ValidationMetrics");
var de_ValidatorMetric = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    validationLoss: import_smithy_client.limitedParseFloat32
  });
}, "de_ValidatorMetric");
var de_VectorSearchBedrockRerankingConfiguration = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    metadataConfiguration: import_smithy_client._json,
    modelConfiguration: /* @__PURE__ */ __name((_) => de_VectorSearchBedrockRerankingModelConfiguration(_, context), "modelConfiguration"),
    numberOfRerankedResults: import_smithy_client.expectInt32
  });
}, "de_VectorSearchBedrockRerankingConfiguration");
var de_VectorSearchBedrockRerankingModelConfiguration = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    additionalModelRequestFields: /* @__PURE__ */ __name((_) => de_AdditionalModelRequestFields(_, context), "additionalModelRequestFields"),
    modelArn: import_smithy_client.expectString
  });
}, "de_VectorSearchBedrockRerankingModelConfiguration");
var de_VectorSearchRerankingConfiguration = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    bedrockRerankingConfiguration: /* @__PURE__ */ __name((_) => de_VectorSearchBedrockRerankingConfiguration(_, context), "bedrockRerankingConfiguration"),
    type: import_smithy_client.expectString
  });
}, "de_VectorSearchRerankingConfiguration");
var deserializeMetadata = /* @__PURE__ */ __name((output) => ({
  httpStatusCode: output.statusCode,
  requestId: output.headers["x-amzn-requestid"] ?? output.headers["x-amzn-request-id"] ?? output.headers["x-amz-request-id"],
  extendedRequestId: output.headers["x-amz-id-2"],
  cfId: output.headers["x-amz-cf-id"]
}), "deserializeMetadata");
var _aTE = "applicationTypeEquals";
var _bCT = "byCustomizationType";
var _bIT = "byInferenceType";
var _bMAE = "baseModelArnEquals";
var _bOM = "byOutputModality";
var _bP = "byProvider";
var _cTA = "creationTimeAfter";
var _cTB = "creationTimeBefore";
var _fMAE = "foundationModelArnEquals";
var _gI = "guardrailIdentifier";
var _gV = "guardrailVersion";
var _iO = "isOwned";
var _mAE = "modelArnEquals";
var _mR = "maxResults";
var _mS = "modelStatus";
var _mSE = "modelSourceEquals";
var _mSI = "modelSourceIdentifier";
var _nC = "nameContains";
var _nT = "nextToken";
var _oMNC = "outputModelNameContains";
var _oT = "offerType";
var _sAE = "sourceAccountEquals";
var _sB = "sortBy";
var _sE = "statusEquals";
var _sMAE = "sourceModelArnEquals";
var _sO = "sortOrder";
var _sTA = "submitTimeAfter";
var _sTB = "submitTimeBefore";
var _t = "type";
var _tE = "typeEquals";
var _tMNC = "targetModelNameContains";

// src/commands/BatchDeleteEvaluationJobCommand.ts
var BatchDeleteEvaluationJobCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "BatchDeleteEvaluationJob", {}).n("BedrockClient", "BatchDeleteEvaluationJobCommand").f(BatchDeleteEvaluationJobRequestFilterSensitiveLog, BatchDeleteEvaluationJobResponseFilterSensitiveLog).ser(se_BatchDeleteEvaluationJobCommand).de(de_BatchDeleteEvaluationJobCommand).build() {
  static {
    __name(this, "BatchDeleteEvaluationJobCommand");
  }
};

// src/commands/CreateCustomModelCommand.ts



var CreateCustomModelCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "CreateCustomModel", {}).n("BedrockClient", "CreateCustomModelCommand").f(void 0, void 0).ser(se_CreateCustomModelCommand).de(de_CreateCustomModelCommand).build() {
  static {
    __name(this, "CreateCustomModelCommand");
  }
};

// src/commands/CreateEvaluationJobCommand.ts



var CreateEvaluationJobCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "CreateEvaluationJob", {}).n("BedrockClient", "CreateEvaluationJobCommand").f(CreateEvaluationJobRequestFilterSensitiveLog, void 0).ser(se_CreateEvaluationJobCommand).de(de_CreateEvaluationJobCommand).build() {
  static {
    __name(this, "CreateEvaluationJobCommand");
  }
};

// src/commands/CreateFoundationModelAgreementCommand.ts



var CreateFoundationModelAgreementCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "CreateFoundationModelAgreement", {}).n("BedrockClient", "CreateFoundationModelAgreementCommand").f(void 0, void 0).ser(se_CreateFoundationModelAgreementCommand).de(de_CreateFoundationModelAgreementCommand).build() {
  static {
    __name(this, "CreateFoundationModelAgreementCommand");
  }
};

// src/commands/CreateGuardrailCommand.ts



var CreateGuardrailCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "CreateGuardrail", {}).n("BedrockClient", "CreateGuardrailCommand").f(CreateGuardrailRequestFilterSensitiveLog, void 0).ser(se_CreateGuardrailCommand).de(de_CreateGuardrailCommand).build() {
  static {
    __name(this, "CreateGuardrailCommand");
  }
};

// src/commands/CreateGuardrailVersionCommand.ts



var CreateGuardrailVersionCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "CreateGuardrailVersion", {}).n("BedrockClient", "CreateGuardrailVersionCommand").f(CreateGuardrailVersionRequestFilterSensitiveLog, void 0).ser(se_CreateGuardrailVersionCommand).de(de_CreateGuardrailVersionCommand).build() {
  static {
    __name(this, "CreateGuardrailVersionCommand");
  }
};

// src/commands/CreateInferenceProfileCommand.ts



var CreateInferenceProfileCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "CreateInferenceProfile", {}).n("BedrockClient", "CreateInferenceProfileCommand").f(CreateInferenceProfileRequestFilterSensitiveLog, void 0).ser(se_CreateInferenceProfileCommand).de(de_CreateInferenceProfileCommand).build() {
  static {
    __name(this, "CreateInferenceProfileCommand");
  }
};

// src/commands/CreateMarketplaceModelEndpointCommand.ts



var CreateMarketplaceModelEndpointCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "CreateMarketplaceModelEndpoint", {}).n("BedrockClient", "CreateMarketplaceModelEndpointCommand").f(void 0, void 0).ser(se_CreateMarketplaceModelEndpointCommand).de(de_CreateMarketplaceModelEndpointCommand).build() {
  static {
    __name(this, "CreateMarketplaceModelEndpointCommand");
  }
};

// src/commands/CreateModelCopyJobCommand.ts



var CreateModelCopyJobCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "CreateModelCopyJob", {}).n("BedrockClient", "CreateModelCopyJobCommand").f(void 0, void 0).ser(se_CreateModelCopyJobCommand).de(de_CreateModelCopyJobCommand).build() {
  static {
    __name(this, "CreateModelCopyJobCommand");
  }
};

// src/commands/CreateModelCustomizationJobCommand.ts



var CreateModelCustomizationJobCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "CreateModelCustomizationJob", {}).n("BedrockClient", "CreateModelCustomizationJobCommand").f(CreateModelCustomizationJobRequestFilterSensitiveLog, void 0).ser(se_CreateModelCustomizationJobCommand).de(de_CreateModelCustomizationJobCommand).build() {
  static {
    __name(this, "CreateModelCustomizationJobCommand");
  }
};

// src/commands/CreateModelImportJobCommand.ts



var CreateModelImportJobCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "CreateModelImportJob", {}).n("BedrockClient", "CreateModelImportJobCommand").f(void 0, void 0).ser(se_CreateModelImportJobCommand).de(de_CreateModelImportJobCommand).build() {
  static {
    __name(this, "CreateModelImportJobCommand");
  }
};

// src/commands/CreateModelInvocationJobCommand.ts



var CreateModelInvocationJobCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "CreateModelInvocationJob", {}).n("BedrockClient", "CreateModelInvocationJobCommand").f(void 0, void 0).ser(se_CreateModelInvocationJobCommand).de(de_CreateModelInvocationJobCommand).build() {
  static {
    __name(this, "CreateModelInvocationJobCommand");
  }
};

// src/commands/CreatePromptRouterCommand.ts



var CreatePromptRouterCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "CreatePromptRouter", {}).n("BedrockClient", "CreatePromptRouterCommand").f(CreatePromptRouterRequestFilterSensitiveLog, void 0).ser(se_CreatePromptRouterCommand).de(de_CreatePromptRouterCommand).build() {
  static {
    __name(this, "CreatePromptRouterCommand");
  }
};

// src/commands/CreateProvisionedModelThroughputCommand.ts



var CreateProvisionedModelThroughputCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "CreateProvisionedModelThroughput", {}).n("BedrockClient", "CreateProvisionedModelThroughputCommand").f(void 0, void 0).ser(se_CreateProvisionedModelThroughputCommand).de(de_CreateProvisionedModelThroughputCommand).build() {
  static {
    __name(this, "CreateProvisionedModelThroughputCommand");
  }
};

// src/commands/DeleteCustomModelCommand.ts



var DeleteCustomModelCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "DeleteCustomModel", {}).n("BedrockClient", "DeleteCustomModelCommand").f(void 0, void 0).ser(se_DeleteCustomModelCommand).de(de_DeleteCustomModelCommand).build() {
  static {
    __name(this, "DeleteCustomModelCommand");
  }
};

// src/commands/DeleteFoundationModelAgreementCommand.ts



var DeleteFoundationModelAgreementCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "DeleteFoundationModelAgreement", {}).n("BedrockClient", "DeleteFoundationModelAgreementCommand").f(void 0, void 0).ser(se_DeleteFoundationModelAgreementCommand).de(de_DeleteFoundationModelAgreementCommand).build() {
  static {
    __name(this, "DeleteFoundationModelAgreementCommand");
  }
};

// src/commands/DeleteGuardrailCommand.ts



var DeleteGuardrailCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "DeleteGuardrail", {}).n("BedrockClient", "DeleteGuardrailCommand").f(void 0, void 0).ser(se_DeleteGuardrailCommand).de(de_DeleteGuardrailCommand).build() {
  static {
    __name(this, "DeleteGuardrailCommand");
  }
};

// src/commands/DeleteImportedModelCommand.ts



var DeleteImportedModelCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "DeleteImportedModel", {}).n("BedrockClient", "DeleteImportedModelCommand").f(void 0, void 0).ser(se_DeleteImportedModelCommand).de(de_DeleteImportedModelCommand).build() {
  static {
    __name(this, "DeleteImportedModelCommand");
  }
};

// src/commands/DeleteInferenceProfileCommand.ts



var DeleteInferenceProfileCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "DeleteInferenceProfile", {}).n("BedrockClient", "DeleteInferenceProfileCommand").f(void 0, void 0).ser(se_DeleteInferenceProfileCommand).de(de_DeleteInferenceProfileCommand).build() {
  static {
    __name(this, "DeleteInferenceProfileCommand");
  }
};

// src/commands/DeleteMarketplaceModelEndpointCommand.ts



var DeleteMarketplaceModelEndpointCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "DeleteMarketplaceModelEndpoint", {}).n("BedrockClient", "DeleteMarketplaceModelEndpointCommand").f(void 0, void 0).ser(se_DeleteMarketplaceModelEndpointCommand).de(de_DeleteMarketplaceModelEndpointCommand).build() {
  static {
    __name(this, "DeleteMarketplaceModelEndpointCommand");
  }
};

// src/commands/DeleteModelInvocationLoggingConfigurationCommand.ts



var DeleteModelInvocationLoggingConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "DeleteModelInvocationLoggingConfiguration", {}).n("BedrockClient", "DeleteModelInvocationLoggingConfigurationCommand").f(void 0, void 0).ser(se_DeleteModelInvocationLoggingConfigurationCommand).de(de_DeleteModelInvocationLoggingConfigurationCommand).build() {
  static {
    __name(this, "DeleteModelInvocationLoggingConfigurationCommand");
  }
};

// src/commands/DeletePromptRouterCommand.ts



var DeletePromptRouterCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "DeletePromptRouter", {}).n("BedrockClient", "DeletePromptRouterCommand").f(void 0, void 0).ser(se_DeletePromptRouterCommand).de(de_DeletePromptRouterCommand).build() {
  static {
    __name(this, "DeletePromptRouterCommand");
  }
};

// src/commands/DeleteProvisionedModelThroughputCommand.ts



var DeleteProvisionedModelThroughputCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "DeleteProvisionedModelThroughput", {}).n("BedrockClient", "DeleteProvisionedModelThroughputCommand").f(void 0, void 0).ser(se_DeleteProvisionedModelThroughputCommand).de(de_DeleteProvisionedModelThroughputCommand).build() {
  static {
    __name(this, "DeleteProvisionedModelThroughputCommand");
  }
};

// src/commands/DeregisterMarketplaceModelEndpointCommand.ts



var DeregisterMarketplaceModelEndpointCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "DeregisterMarketplaceModelEndpoint", {}).n("BedrockClient", "DeregisterMarketplaceModelEndpointCommand").f(void 0, void 0).ser(se_DeregisterMarketplaceModelEndpointCommand).de(de_DeregisterMarketplaceModelEndpointCommand).build() {
  static {
    __name(this, "DeregisterMarketplaceModelEndpointCommand");
  }
};

// src/commands/GetCustomModelCommand.ts



var GetCustomModelCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "GetCustomModel", {}).n("BedrockClient", "GetCustomModelCommand").f(void 0, GetCustomModelResponseFilterSensitiveLog).ser(se_GetCustomModelCommand).de(de_GetCustomModelCommand).build() {
  static {
    __name(this, "GetCustomModelCommand");
  }
};

// src/commands/GetEvaluationJobCommand.ts



var GetEvaluationJobCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "GetEvaluationJob", {}).n("BedrockClient", "GetEvaluationJobCommand").f(GetEvaluationJobRequestFilterSensitiveLog, GetEvaluationJobResponseFilterSensitiveLog).ser(se_GetEvaluationJobCommand).de(de_GetEvaluationJobCommand).build() {
  static {
    __name(this, "GetEvaluationJobCommand");
  }
};

// src/commands/GetFoundationModelAvailabilityCommand.ts



var GetFoundationModelAvailabilityCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "GetFoundationModelAvailability", {}).n("BedrockClient", "GetFoundationModelAvailabilityCommand").f(void 0, void 0).ser(se_GetFoundationModelAvailabilityCommand).de(de_GetFoundationModelAvailabilityCommand).build() {
  static {
    __name(this, "GetFoundationModelAvailabilityCommand");
  }
};

// src/commands/GetFoundationModelCommand.ts



var GetFoundationModelCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "GetFoundationModel", {}).n("BedrockClient", "GetFoundationModelCommand").f(void 0, void 0).ser(se_GetFoundationModelCommand).de(de_GetFoundationModelCommand).build() {
  static {
    __name(this, "GetFoundationModelCommand");
  }
};

// src/commands/GetGuardrailCommand.ts



var GetGuardrailCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "GetGuardrail", {}).n("BedrockClient", "GetGuardrailCommand").f(void 0, GetGuardrailResponseFilterSensitiveLog).ser(se_GetGuardrailCommand).de(de_GetGuardrailCommand).build() {
  static {
    __name(this, "GetGuardrailCommand");
  }
};

// src/commands/GetImportedModelCommand.ts



var GetImportedModelCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "GetImportedModel", {}).n("BedrockClient", "GetImportedModelCommand").f(void 0, void 0).ser(se_GetImportedModelCommand).de(de_GetImportedModelCommand).build() {
  static {
    __name(this, "GetImportedModelCommand");
  }
};

// src/commands/GetInferenceProfileCommand.ts



var GetInferenceProfileCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "GetInferenceProfile", {}).n("BedrockClient", "GetInferenceProfileCommand").f(void 0, GetInferenceProfileResponseFilterSensitiveLog).ser(se_GetInferenceProfileCommand).de(de_GetInferenceProfileCommand).build() {
  static {
    __name(this, "GetInferenceProfileCommand");
  }
};

// src/commands/GetMarketplaceModelEndpointCommand.ts



var GetMarketplaceModelEndpointCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "GetMarketplaceModelEndpoint", {}).n("BedrockClient", "GetMarketplaceModelEndpointCommand").f(void 0, void 0).ser(se_GetMarketplaceModelEndpointCommand).de(de_GetMarketplaceModelEndpointCommand).build() {
  static {
    __name(this, "GetMarketplaceModelEndpointCommand");
  }
};

// src/commands/GetModelCopyJobCommand.ts



var GetModelCopyJobCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "GetModelCopyJob", {}).n("BedrockClient", "GetModelCopyJobCommand").f(void 0, void 0).ser(se_GetModelCopyJobCommand).de(de_GetModelCopyJobCommand).build() {
  static {
    __name(this, "GetModelCopyJobCommand");
  }
};

// src/commands/GetModelCustomizationJobCommand.ts



var GetModelCustomizationJobCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "GetModelCustomizationJob", {}).n("BedrockClient", "GetModelCustomizationJobCommand").f(void 0, GetModelCustomizationJobResponseFilterSensitiveLog).ser(se_GetModelCustomizationJobCommand).de(de_GetModelCustomizationJobCommand).build() {
  static {
    __name(this, "GetModelCustomizationJobCommand");
  }
};

// src/commands/GetModelImportJobCommand.ts



var GetModelImportJobCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "GetModelImportJob", {}).n("BedrockClient", "GetModelImportJobCommand").f(void 0, void 0).ser(se_GetModelImportJobCommand).de(de_GetModelImportJobCommand).build() {
  static {
    __name(this, "GetModelImportJobCommand");
  }
};

// src/commands/GetModelInvocationJobCommand.ts



var GetModelInvocationJobCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "GetModelInvocationJob", {}).n("BedrockClient", "GetModelInvocationJobCommand").f(void 0, GetModelInvocationJobResponseFilterSensitiveLog).ser(se_GetModelInvocationJobCommand).de(de_GetModelInvocationJobCommand).build() {
  static {
    __name(this, "GetModelInvocationJobCommand");
  }
};

// src/commands/GetModelInvocationLoggingConfigurationCommand.ts



var GetModelInvocationLoggingConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "GetModelInvocationLoggingConfiguration", {}).n("BedrockClient", "GetModelInvocationLoggingConfigurationCommand").f(void 0, void 0).ser(se_GetModelInvocationLoggingConfigurationCommand).de(de_GetModelInvocationLoggingConfigurationCommand).build() {
  static {
    __name(this, "GetModelInvocationLoggingConfigurationCommand");
  }
};

// src/commands/GetPromptRouterCommand.ts



var GetPromptRouterCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "GetPromptRouter", {}).n("BedrockClient", "GetPromptRouterCommand").f(void 0, GetPromptRouterResponseFilterSensitiveLog).ser(se_GetPromptRouterCommand).de(de_GetPromptRouterCommand).build() {
  static {
    __name(this, "GetPromptRouterCommand");
  }
};

// src/commands/GetProvisionedModelThroughputCommand.ts



var GetProvisionedModelThroughputCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "GetProvisionedModelThroughput", {}).n("BedrockClient", "GetProvisionedModelThroughputCommand").f(void 0, void 0).ser(se_GetProvisionedModelThroughputCommand).de(de_GetProvisionedModelThroughputCommand).build() {
  static {
    __name(this, "GetProvisionedModelThroughputCommand");
  }
};

// src/commands/GetUseCaseForModelAccessCommand.ts



var GetUseCaseForModelAccessCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "GetUseCaseForModelAccess", {}).n("BedrockClient", "GetUseCaseForModelAccessCommand").f(void 0, void 0).ser(se_GetUseCaseForModelAccessCommand).de(de_GetUseCaseForModelAccessCommand).build() {
  static {
    __name(this, "GetUseCaseForModelAccessCommand");
  }
};

// src/commands/ListCustomModelsCommand.ts



var ListCustomModelsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "ListCustomModels", {}).n("BedrockClient", "ListCustomModelsCommand").f(void 0, void 0).ser(se_ListCustomModelsCommand).de(de_ListCustomModelsCommand).build() {
  static {
    __name(this, "ListCustomModelsCommand");
  }
};

// src/commands/ListEvaluationJobsCommand.ts



var ListEvaluationJobsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "ListEvaluationJobs", {}).n("BedrockClient", "ListEvaluationJobsCommand").f(void 0, void 0).ser(se_ListEvaluationJobsCommand).de(de_ListEvaluationJobsCommand).build() {
  static {
    __name(this, "ListEvaluationJobsCommand");
  }
};

// src/commands/ListFoundationModelAgreementOffersCommand.ts



var ListFoundationModelAgreementOffersCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "ListFoundationModelAgreementOffers", {}).n("BedrockClient", "ListFoundationModelAgreementOffersCommand").f(void 0, void 0).ser(se_ListFoundationModelAgreementOffersCommand).de(de_ListFoundationModelAgreementOffersCommand).build() {
  static {
    __name(this, "ListFoundationModelAgreementOffersCommand");
  }
};

// src/commands/ListFoundationModelsCommand.ts



var ListFoundationModelsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "ListFoundationModels", {}).n("BedrockClient", "ListFoundationModelsCommand").f(void 0, void 0).ser(se_ListFoundationModelsCommand).de(de_ListFoundationModelsCommand).build() {
  static {
    __name(this, "ListFoundationModelsCommand");
  }
};

// src/commands/ListGuardrailsCommand.ts



var ListGuardrailsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "ListGuardrails", {}).n("BedrockClient", "ListGuardrailsCommand").f(void 0, ListGuardrailsResponseFilterSensitiveLog).ser(se_ListGuardrailsCommand).de(de_ListGuardrailsCommand).build() {
  static {
    __name(this, "ListGuardrailsCommand");
  }
};

// src/commands/ListImportedModelsCommand.ts



var ListImportedModelsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "ListImportedModels", {}).n("BedrockClient", "ListImportedModelsCommand").f(void 0, void 0).ser(se_ListImportedModelsCommand).de(de_ListImportedModelsCommand).build() {
  static {
    __name(this, "ListImportedModelsCommand");
  }
};

// src/commands/ListInferenceProfilesCommand.ts



var ListInferenceProfilesCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "ListInferenceProfiles", {}).n("BedrockClient", "ListInferenceProfilesCommand").f(void 0, ListInferenceProfilesResponseFilterSensitiveLog).ser(se_ListInferenceProfilesCommand).de(de_ListInferenceProfilesCommand).build() {
  static {
    __name(this, "ListInferenceProfilesCommand");
  }
};

// src/commands/ListMarketplaceModelEndpointsCommand.ts



var ListMarketplaceModelEndpointsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "ListMarketplaceModelEndpoints", {}).n("BedrockClient", "ListMarketplaceModelEndpointsCommand").f(void 0, void 0).ser(se_ListMarketplaceModelEndpointsCommand).de(de_ListMarketplaceModelEndpointsCommand).build() {
  static {
    __name(this, "ListMarketplaceModelEndpointsCommand");
  }
};

// src/commands/ListModelCopyJobsCommand.ts



var ListModelCopyJobsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "ListModelCopyJobs", {}).n("BedrockClient", "ListModelCopyJobsCommand").f(void 0, void 0).ser(se_ListModelCopyJobsCommand).de(de_ListModelCopyJobsCommand).build() {
  static {
    __name(this, "ListModelCopyJobsCommand");
  }
};

// src/commands/ListModelCustomizationJobsCommand.ts



var ListModelCustomizationJobsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "ListModelCustomizationJobs", {}).n("BedrockClient", "ListModelCustomizationJobsCommand").f(void 0, void 0).ser(se_ListModelCustomizationJobsCommand).de(de_ListModelCustomizationJobsCommand).build() {
  static {
    __name(this, "ListModelCustomizationJobsCommand");
  }
};

// src/commands/ListModelImportJobsCommand.ts



var ListModelImportJobsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "ListModelImportJobs", {}).n("BedrockClient", "ListModelImportJobsCommand").f(void 0, void 0).ser(se_ListModelImportJobsCommand).de(de_ListModelImportJobsCommand).build() {
  static {
    __name(this, "ListModelImportJobsCommand");
  }
};

// src/commands/ListModelInvocationJobsCommand.ts



var ListModelInvocationJobsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "ListModelInvocationJobs", {}).n("BedrockClient", "ListModelInvocationJobsCommand").f(void 0, ListModelInvocationJobsResponseFilterSensitiveLog).ser(se_ListModelInvocationJobsCommand).de(de_ListModelInvocationJobsCommand).build() {
  static {
    __name(this, "ListModelInvocationJobsCommand");
  }
};

// src/commands/ListPromptRoutersCommand.ts



var ListPromptRoutersCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "ListPromptRouters", {}).n("BedrockClient", "ListPromptRoutersCommand").f(void 0, ListPromptRoutersResponseFilterSensitiveLog).ser(se_ListPromptRoutersCommand).de(de_ListPromptRoutersCommand).build() {
  static {
    __name(this, "ListPromptRoutersCommand");
  }
};

// src/commands/ListProvisionedModelThroughputsCommand.ts



var ListProvisionedModelThroughputsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "ListProvisionedModelThroughputs", {}).n("BedrockClient", "ListProvisionedModelThroughputsCommand").f(void 0, void 0).ser(se_ListProvisionedModelThroughputsCommand).de(de_ListProvisionedModelThroughputsCommand).build() {
  static {
    __name(this, "ListProvisionedModelThroughputsCommand");
  }
};

// src/commands/ListTagsForResourceCommand.ts



var ListTagsForResourceCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "ListTagsForResource", {}).n("BedrockClient", "ListTagsForResourceCommand").f(void 0, void 0).ser(se_ListTagsForResourceCommand).de(de_ListTagsForResourceCommand).build() {
  static {
    __name(this, "ListTagsForResourceCommand");
  }
};

// src/commands/PutModelInvocationLoggingConfigurationCommand.ts



var PutModelInvocationLoggingConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "PutModelInvocationLoggingConfiguration", {}).n("BedrockClient", "PutModelInvocationLoggingConfigurationCommand").f(void 0, void 0).ser(se_PutModelInvocationLoggingConfigurationCommand).de(de_PutModelInvocationLoggingConfigurationCommand).build() {
  static {
    __name(this, "PutModelInvocationLoggingConfigurationCommand");
  }
};

// src/commands/PutUseCaseForModelAccessCommand.ts



var PutUseCaseForModelAccessCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "PutUseCaseForModelAccess", {}).n("BedrockClient", "PutUseCaseForModelAccessCommand").f(void 0, void 0).ser(se_PutUseCaseForModelAccessCommand).de(de_PutUseCaseForModelAccessCommand).build() {
  static {
    __name(this, "PutUseCaseForModelAccessCommand");
  }
};

// src/commands/RegisterMarketplaceModelEndpointCommand.ts



var RegisterMarketplaceModelEndpointCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "RegisterMarketplaceModelEndpoint", {}).n("BedrockClient", "RegisterMarketplaceModelEndpointCommand").f(void 0, void 0).ser(se_RegisterMarketplaceModelEndpointCommand).de(de_RegisterMarketplaceModelEndpointCommand).build() {
  static {
    __name(this, "RegisterMarketplaceModelEndpointCommand");
  }
};

// src/commands/StopEvaluationJobCommand.ts



var StopEvaluationJobCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "StopEvaluationJob", {}).n("BedrockClient", "StopEvaluationJobCommand").f(StopEvaluationJobRequestFilterSensitiveLog, void 0).ser(se_StopEvaluationJobCommand).de(de_StopEvaluationJobCommand).build() {
  static {
    __name(this, "StopEvaluationJobCommand");
  }
};

// src/commands/StopModelCustomizationJobCommand.ts



var StopModelCustomizationJobCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "StopModelCustomizationJob", {}).n("BedrockClient", "StopModelCustomizationJobCommand").f(void 0, void 0).ser(se_StopModelCustomizationJobCommand).de(de_StopModelCustomizationJobCommand).build() {
  static {
    __name(this, "StopModelCustomizationJobCommand");
  }
};

// src/commands/StopModelInvocationJobCommand.ts



var StopModelInvocationJobCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "StopModelInvocationJob", {}).n("BedrockClient", "StopModelInvocationJobCommand").f(void 0, void 0).ser(se_StopModelInvocationJobCommand).de(de_StopModelInvocationJobCommand).build() {
  static {
    __name(this, "StopModelInvocationJobCommand");
  }
};

// src/commands/TagResourceCommand.ts



var TagResourceCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "TagResource", {}).n("BedrockClient", "TagResourceCommand").f(void 0, void 0).ser(se_TagResourceCommand).de(de_TagResourceCommand).build() {
  static {
    __name(this, "TagResourceCommand");
  }
};

// src/commands/UntagResourceCommand.ts



var UntagResourceCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "UntagResource", {}).n("BedrockClient", "UntagResourceCommand").f(void 0, void 0).ser(se_UntagResourceCommand).de(de_UntagResourceCommand).build() {
  static {
    __name(this, "UntagResourceCommand");
  }
};

// src/commands/UpdateGuardrailCommand.ts



var UpdateGuardrailCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "UpdateGuardrail", {}).n("BedrockClient", "UpdateGuardrailCommand").f(UpdateGuardrailRequestFilterSensitiveLog, void 0).ser(se_UpdateGuardrailCommand).de(de_UpdateGuardrailCommand).build() {
  static {
    __name(this, "UpdateGuardrailCommand");
  }
};

// src/commands/UpdateMarketplaceModelEndpointCommand.ts



var UpdateMarketplaceModelEndpointCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "UpdateMarketplaceModelEndpoint", {}).n("BedrockClient", "UpdateMarketplaceModelEndpointCommand").f(void 0, void 0).ser(se_UpdateMarketplaceModelEndpointCommand).de(de_UpdateMarketplaceModelEndpointCommand).build() {
  static {
    __name(this, "UpdateMarketplaceModelEndpointCommand");
  }
};

// src/commands/UpdateProvisionedModelThroughputCommand.ts



var UpdateProvisionedModelThroughputCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonBedrockControlPlaneService", "UpdateProvisionedModelThroughput", {}).n("BedrockClient", "UpdateProvisionedModelThroughputCommand").f(void 0, void 0).ser(se_UpdateProvisionedModelThroughputCommand).de(de_UpdateProvisionedModelThroughputCommand).build() {
  static {
    __name(this, "UpdateProvisionedModelThroughputCommand");
  }
};

// src/Bedrock.ts
var commands = {
  BatchDeleteEvaluationJobCommand,
  CreateCustomModelCommand,
  CreateEvaluationJobCommand,
  CreateFoundationModelAgreementCommand,
  CreateGuardrailCommand,
  CreateGuardrailVersionCommand,
  CreateInferenceProfileCommand,
  CreateMarketplaceModelEndpointCommand,
  CreateModelCopyJobCommand,
  CreateModelCustomizationJobCommand,
  CreateModelImportJobCommand,
  CreateModelInvocationJobCommand,
  CreatePromptRouterCommand,
  CreateProvisionedModelThroughputCommand,
  DeleteCustomModelCommand,
  DeleteFoundationModelAgreementCommand,
  DeleteGuardrailCommand,
  DeleteImportedModelCommand,
  DeleteInferenceProfileCommand,
  DeleteMarketplaceModelEndpointCommand,
  DeleteModelInvocationLoggingConfigurationCommand,
  DeletePromptRouterCommand,
  DeleteProvisionedModelThroughputCommand,
  DeregisterMarketplaceModelEndpointCommand,
  GetCustomModelCommand,
  GetEvaluationJobCommand,
  GetFoundationModelCommand,
  GetFoundationModelAvailabilityCommand,
  GetGuardrailCommand,
  GetImportedModelCommand,
  GetInferenceProfileCommand,
  GetMarketplaceModelEndpointCommand,
  GetModelCopyJobCommand,
  GetModelCustomizationJobCommand,
  GetModelImportJobCommand,
  GetModelInvocationJobCommand,
  GetModelInvocationLoggingConfigurationCommand,
  GetPromptRouterCommand,
  GetProvisionedModelThroughputCommand,
  GetUseCaseForModelAccessCommand,
  ListCustomModelsCommand,
  ListEvaluationJobsCommand,
  ListFoundationModelAgreementOffersCommand,
  ListFoundationModelsCommand,
  ListGuardrailsCommand,
  ListImportedModelsCommand,
  ListInferenceProfilesCommand,
  ListMarketplaceModelEndpointsCommand,
  ListModelCopyJobsCommand,
  ListModelCustomizationJobsCommand,
  ListModelImportJobsCommand,
  ListModelInvocationJobsCommand,
  ListPromptRoutersCommand,
  ListProvisionedModelThroughputsCommand,
  ListTagsForResourceCommand,
  PutModelInvocationLoggingConfigurationCommand,
  PutUseCaseForModelAccessCommand,
  RegisterMarketplaceModelEndpointCommand,
  StopEvaluationJobCommand,
  StopModelCustomizationJobCommand,
  StopModelInvocationJobCommand,
  TagResourceCommand,
  UntagResourceCommand,
  UpdateGuardrailCommand,
  UpdateMarketplaceModelEndpointCommand,
  UpdateProvisionedModelThroughputCommand
};
var Bedrock = class extends BedrockClient {
  static {
    __name(this, "Bedrock");
  }
};
(0, import_smithy_client.createAggregatedClient)(commands, Bedrock);

// src/pagination/ListCustomModelsPaginator.ts

var paginateListCustomModels = (0, import_core.createPaginator)(BedrockClient, ListCustomModelsCommand, "nextToken", "nextToken", "maxResults");

// src/pagination/ListEvaluationJobsPaginator.ts

var paginateListEvaluationJobs = (0, import_core.createPaginator)(BedrockClient, ListEvaluationJobsCommand, "nextToken", "nextToken", "maxResults");

// src/pagination/ListGuardrailsPaginator.ts

var paginateListGuardrails = (0, import_core.createPaginator)(BedrockClient, ListGuardrailsCommand, "nextToken", "nextToken", "maxResults");

// src/pagination/ListImportedModelsPaginator.ts

var paginateListImportedModels = (0, import_core.createPaginator)(BedrockClient, ListImportedModelsCommand, "nextToken", "nextToken", "maxResults");

// src/pagination/ListInferenceProfilesPaginator.ts

var paginateListInferenceProfiles = (0, import_core.createPaginator)(BedrockClient, ListInferenceProfilesCommand, "nextToken", "nextToken", "maxResults");

// src/pagination/ListMarketplaceModelEndpointsPaginator.ts

var paginateListMarketplaceModelEndpoints = (0, import_core.createPaginator)(BedrockClient, ListMarketplaceModelEndpointsCommand, "nextToken", "nextToken", "maxResults");

// src/pagination/ListModelCopyJobsPaginator.ts

var paginateListModelCopyJobs = (0, import_core.createPaginator)(BedrockClient, ListModelCopyJobsCommand, "nextToken", "nextToken", "maxResults");

// src/pagination/ListModelCustomizationJobsPaginator.ts

var paginateListModelCustomizationJobs = (0, import_core.createPaginator)(BedrockClient, ListModelCustomizationJobsCommand, "nextToken", "nextToken", "maxResults");

// src/pagination/ListModelImportJobsPaginator.ts

var paginateListModelImportJobs = (0, import_core.createPaginator)(BedrockClient, ListModelImportJobsCommand, "nextToken", "nextToken", "maxResults");

// src/pagination/ListModelInvocationJobsPaginator.ts

var paginateListModelInvocationJobs = (0, import_core.createPaginator)(BedrockClient, ListModelInvocationJobsCommand, "nextToken", "nextToken", "maxResults");

// src/pagination/ListPromptRoutersPaginator.ts

var paginateListPromptRouters = (0, import_core.createPaginator)(BedrockClient, ListPromptRoutersCommand, "nextToken", "nextToken", "maxResults");

// src/pagination/ListProvisionedModelThroughputsPaginator.ts

var paginateListProvisionedModelThroughputs = (0, import_core.createPaginator)(BedrockClient, ListProvisionedModelThroughputsCommand, "nextToken", "nextToken", "maxResults");
// Annotate the CommonJS export names for ESM import in node:

0 && (module.exports = {
  BedrockServiceException,
  __Client,
  BedrockClient,
  Bedrock,
  $Command,
  BatchDeleteEvaluationJobCommand,
  CreateCustomModelCommand,
  CreateEvaluationJobCommand,
  CreateFoundationModelAgreementCommand,
  CreateGuardrailCommand,
  CreateGuardrailVersionCommand,
  CreateInferenceProfileCommand,
  CreateMarketplaceModelEndpointCommand,
  CreateModelCopyJobCommand,
  CreateModelCustomizationJobCommand,
  CreateModelImportJobCommand,
  CreateModelInvocationJobCommand,
  CreatePromptRouterCommand,
  CreateProvisionedModelThroughputCommand,
  DeleteCustomModelCommand,
  DeleteFoundationModelAgreementCommand,
  DeleteGuardrailCommand,
  DeleteImportedModelCommand,
  DeleteInferenceProfileCommand,
  DeleteMarketplaceModelEndpointCommand,
  DeleteModelInvocationLoggingConfigurationCommand,
  DeletePromptRouterCommand,
  DeleteProvisionedModelThroughputCommand,
  DeregisterMarketplaceModelEndpointCommand,
  GetCustomModelCommand,
  GetEvaluationJobCommand,
  GetFoundationModelAvailabilityCommand,
  GetFoundationModelCommand,
  GetGuardrailCommand,
  GetImportedModelCommand,
  GetInferenceProfileCommand,
  GetMarketplaceModelEndpointCommand,
  GetModelCopyJobCommand,
  GetModelCustomizationJobCommand,
  GetModelImportJobCommand,
  GetModelInvocationJobCommand,
  GetModelInvocationLoggingConfigurationCommand,
  GetPromptRouterCommand,
  GetProvisionedModelThroughputCommand,
  GetUseCaseForModelAccessCommand,
  ListCustomModelsCommand,
  ListEvaluationJobsCommand,
  ListFoundationModelAgreementOffersCommand,
  ListFoundationModelsCommand,
  ListGuardrailsCommand,
  ListImportedModelsCommand,
  ListInferenceProfilesCommand,
  ListMarketplaceModelEndpointsCommand,
  ListModelCopyJobsCommand,
  ListModelCustomizationJobsCommand,
  ListModelImportJobsCommand,
  ListModelInvocationJobsCommand,
  ListPromptRoutersCommand,
  ListProvisionedModelThroughputsCommand,
  ListTagsForResourceCommand,
  PutModelInvocationLoggingConfigurationCommand,
  PutUseCaseForModelAccessCommand,
  RegisterMarketplaceModelEndpointCommand,
  StopEvaluationJobCommand,
  StopModelCustomizationJobCommand,
  StopModelInvocationJobCommand,
  TagResourceCommand,
  UntagResourceCommand,
  UpdateGuardrailCommand,
  UpdateMarketplaceModelEndpointCommand,
  UpdateProvisionedModelThroughputCommand,
  paginateListCustomModels,
  paginateListEvaluationJobs,
  paginateListGuardrails,
  paginateListImportedModels,
  paginateListInferenceProfiles,
  paginateListMarketplaceModelEndpoints,
  paginateListModelCopyJobs,
  paginateListModelCustomizationJobs,
  paginateListModelImportJobs,
  paginateListModelInvocationJobs,
  paginateListPromptRouters,
  paginateListProvisionedModelThroughputs,
  AccessDeniedException,
  AgreementStatus,
  InternalServerException,
  ResourceNotFoundException,
  ThrottlingException,
  ValidationException,
  ConflictException,
  EndpointConfig,
  Status,
  ServiceQuotaExceededException,
  ServiceUnavailableException,
  ModelDataSource,
  TooManyTagsException,
  CustomizationConfig,
  CustomizationType,
  ModelStatus,
  InvocationLogSource,
  RequestMetadataFilters,
  SortModelsBy,
  SortOrder,
  EvaluationJobStatus,
  ApplicationType,
  RatingScaleItemValue,
  AutomatedEvaluationCustomMetricSource,
  EvaluationDatasetLocation,
  EvaluationTaskType,
  EvaluatorModelConfig,
  EvaluationConfig,
  PerformanceConfigLatency,
  EvaluationModelConfig,
  ExternalSourceType,
  QueryTransformationType,
  AttributeType,
  SearchType,
  RerankingMetadataSelectionMode,
  RerankingMetadataSelectiveModeConfiguration,
  VectorSearchRerankingConfigurationType,
  RetrieveAndGenerateType,
  EvaluationPrecomputedRagSourceConfig,
  EvaluationJobType,
  SortJobsBy,
  GuardrailContentFilterAction,
  GuardrailModality,
  GuardrailFilterStrength,
  GuardrailContentFilterType,
  GuardrailContentFiltersTierName,
  GuardrailContextualGroundingAction,
  GuardrailContextualGroundingFilterType,
  GuardrailSensitiveInformationAction,
  GuardrailPiiEntityType,
  GuardrailTopicsTierName,
  GuardrailTopicAction,
  GuardrailTopicType,
  GuardrailWordAction,
  GuardrailManagedWordsType,
  GuardrailStatus,
  InferenceProfileModelSource,
  InferenceProfileStatus,
  InferenceProfileType,
  ModelCopyJobStatus,
  ModelImportJobStatus,
  S3InputFormat,
  ModelInvocationJobInputDataConfig,
  ModelInvocationJobOutputDataConfig,
  ModelInvocationJobStatus,
  ModelCustomization,
  InferenceType,
  ModelModality,
  FoundationModelLifecycleStatus,
  PromptRouterStatus,
  PromptRouterType,
  CommitmentDuration,
  ProvisionedModelStatus,
  SortByProvisionedModels,
  AuthorizationStatus,
  RequestMetadataBaseFiltersFilterSensitiveLog,
  RequestMetadataFiltersFilterSensitiveLog,
  InvocationLogsConfigFilterSensitiveLog,
  TrainingDataConfigFilterSensitiveLog,
  GetCustomModelResponseFilterSensitiveLog,
  BatchDeleteEvaluationJobRequestFilterSensitiveLog,
  BatchDeleteEvaluationJobErrorFilterSensitiveLog,
  BatchDeleteEvaluationJobItemFilterSensitiveLog,
  BatchDeleteEvaluationJobResponseFilterSensitiveLog,
  CustomMetricDefinitionFilterSensitiveLog,
  AutomatedEvaluationCustomMetricSourceFilterSensitiveLog,
  AutomatedEvaluationCustomMetricConfigFilterSensitiveLog,
  EvaluationDatasetFilterSensitiveLog,
  EvaluationDatasetMetricConfigFilterSensitiveLog,
  AutomatedEvaluationConfigFilterSensitiveLog,
  HumanEvaluationCustomMetricFilterSensitiveLog,
  HumanWorkflowConfigFilterSensitiveLog,
  HumanEvaluationConfigFilterSensitiveLog,
  EvaluationConfigFilterSensitiveLog,
  EvaluationBedrockModelFilterSensitiveLog,
  EvaluationModelConfigFilterSensitiveLog,
  PromptTemplateFilterSensitiveLog,
  ExternalSourcesGenerationConfigurationFilterSensitiveLog,
  ByteContentDocFilterSensitiveLog,
  ExternalSourceFilterSensitiveLog,
  ExternalSourcesRetrieveAndGenerateConfigurationFilterSensitiveLog,
  GenerationConfigurationFilterSensitiveLog,
  MetadataAttributeSchemaFilterSensitiveLog,
  ImplicitFilterConfigurationFilterSensitiveLog,
  RerankingMetadataSelectiveModeConfigurationFilterSensitiveLog,
  MetadataConfigurationForRerankingFilterSensitiveLog,
  VectorSearchBedrockRerankingConfigurationFilterSensitiveLog,
  VectorSearchRerankingConfigurationFilterSensitiveLog,
  GetEvaluationJobRequestFilterSensitiveLog,
  StopEvaluationJobRequestFilterSensitiveLog,
  GuardrailContentFilterConfigFilterSensitiveLog,
  GuardrailContentFiltersTierConfigFilterSensitiveLog,
  GuardrailContentPolicyConfigFilterSensitiveLog,
  GuardrailContextualGroundingFilterConfigFilterSensitiveLog,
  GuardrailContextualGroundingPolicyConfigFilterSensitiveLog,
  GuardrailTopicsTierConfigFilterSensitiveLog,
  GuardrailTopicConfigFilterSensitiveLog,
  GuardrailTopicPolicyConfigFilterSensitiveLog,
  GuardrailManagedWordsConfigFilterSensitiveLog,
  GuardrailWordConfigFilterSensitiveLog,
  GuardrailWordPolicyConfigFilterSensitiveLog,
  CreateGuardrailRequestFilterSensitiveLog,
  CreateGuardrailVersionRequestFilterSensitiveLog,
  GuardrailContentFilterFilterSensitiveLog,
  GuardrailContentFiltersTierFilterSensitiveLog,
  GuardrailContentPolicyFilterSensitiveLog,
  GuardrailContextualGroundingFilterFilterSensitiveLog,
  GuardrailContextualGroundingPolicyFilterSensitiveLog,
  GuardrailTopicsTierFilterSensitiveLog,
  GuardrailTopicFilterSensitiveLog,
  GuardrailTopicPolicyFilterSensitiveLog,
  GuardrailManagedWordsFilterSensitiveLog,
  GuardrailWordFilterSensitiveLog,
  GuardrailWordPolicyFilterSensitiveLog,
  GetGuardrailResponseFilterSensitiveLog,
  GuardrailSummaryFilterSensitiveLog,
  ListGuardrailsResponseFilterSensitiveLog,
  UpdateGuardrailRequestFilterSensitiveLog,
  CreateInferenceProfileRequestFilterSensitiveLog,
  GetInferenceProfileResponseFilterSensitiveLog,
  InferenceProfileSummaryFilterSensitiveLog,
  ListInferenceProfilesResponseFilterSensitiveLog,
  GetModelInvocationJobResponseFilterSensitiveLog,
  ModelInvocationJobSummaryFilterSensitiveLog,
  ListModelInvocationJobsResponseFilterSensitiveLog,
  CreatePromptRouterRequestFilterSensitiveLog,
  GetPromptRouterResponseFilterSensitiveLog,
  PromptRouterSummaryFilterSensitiveLog,
  ListPromptRoutersResponseFilterSensitiveLog,
  EntitlementAvailability,
  RegionAvailability,
  OfferType,
  ModelCustomizationJobStatus,
  JobStatusDetails,
  FineTuningJobStatus,
  RetrievalFilter,
  KnowledgeBaseConfig,
  RAGConfig,
  EvaluationInferenceConfig,
  CreateModelCustomizationJobRequestFilterSensitiveLog,
  GetModelCustomizationJobResponseFilterSensitiveLog,
  RetrievalFilterFilterSensitiveLog,
  KnowledgeBaseVectorSearchConfigurationFilterSensitiveLog,
  KnowledgeBaseRetrievalConfigurationFilterSensitiveLog,
  KnowledgeBaseRetrieveAndGenerateConfigurationFilterSensitiveLog,
  RetrieveConfigFilterSensitiveLog,
  RetrieveAndGenerateConfigurationFilterSensitiveLog,
  KnowledgeBaseConfigFilterSensitiveLog,
  RAGConfigFilterSensitiveLog,
  EvaluationInferenceConfigFilterSensitiveLog,
  CreateEvaluationJobRequestFilterSensitiveLog,
  GetEvaluationJobResponseFilterSensitiveLog
});

