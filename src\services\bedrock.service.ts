import { BedrockRuntimeClient, InvokeModelCommand } from '@aws-sdk/client-bedrock-runtime';
import { config } from '../config/environment';
import { ConversationQuery, ConversationResponse, VectorSearchResult } from '../models/business.models';

/**
 * AWS Bedrock Service for AI-powered conversational responses
 */
export class BedrockService {
  private client: BedrockRuntimeClient;
  private modelId: string;
  private maxTokens: number;
  private temperature: number;

  constructor() {
    this.client = new BedrockRuntimeClient({
      region: config.aws.region,
      credentials: {
        accessKeyId: config.aws.accessKeyId,
        secretAccessKey: config.aws.secretAccessKey,
      },
    });
    
    this.modelId = config.aws.bedrock.modelId;
    this.maxTokens = config.aws.bedrock.maxTokens;
    this.temperature = config.aws.bedrock.temperature;
  }

  /**
   * Generate embeddings for text using Bedrock
   * @param text - Text to generate embeddings for
   * @returns Array of embedding values
   */
  async generateEmbeddings(text: string): Promise<number[]> {
    try {
      // Use Amazon Titan Embeddings model for generating embeddings
      const embeddingModelId = 'amazon.titan-embed-text-v1';
      
      const payload = {
        inputText: text,
      };

      const command = new InvokeModelCommand({
        modelId: embeddingModelId,
        body: JSON.stringify(payload),
        contentType: 'application/json',
        accept: 'application/json',
      });

      const response = await this.client.send(command);
      const responseBody = JSON.parse(new TextDecoder().decode(response.body));
      
      return responseBody.embedding;
    } catch (error) {
      console.error('Error generating embeddings:', error);
      throw new Error('Failed to generate embeddings');
    }
  }

  /**
   * Generate a conversational response using Claude
   * @param query - User query and context
   * @param relevantBusinesses - Relevant businesses from vector search
   * @returns AI-generated conversational response
   */
  async generateConversationalResponse(
    query: ConversationQuery,
    relevantBusinesses: VectorSearchResult[]
  ): Promise<string> {
    try {
      const systemPrompt = this.buildSystemPrompt();
      const userPrompt = this.buildUserPrompt(query, relevantBusinesses);

      const payload = {
        anthropic_version: 'bedrock-2023-05-31',
        max_tokens: this.maxTokens,
        temperature: this.temperature,
        system: systemPrompt,
        messages: [
          {
            role: 'user',
            content: userPrompt,
          },
        ],
      };

      const command = new InvokeModelCommand({
        modelId: this.modelId,
        body: JSON.stringify(payload),
        contentType: 'application/json',
        accept: 'application/json',
      });

      const response = await this.client.send(command);
      const responseBody = JSON.parse(new TextDecoder().decode(response.body));
      
      return responseBody.content[0].text;
    } catch (error) {
      console.error('Error generating conversational response:', error);
      throw new Error('Failed to generate conversational response');
    }
  }

  /**
   * Generate suggestions for follow-up questions
   * @param query - Original user query
   * @param response - AI response
   * @param relevantBusinesses - Relevant businesses
   * @returns Array of suggested follow-up questions
   */
  async generateSuggestions(
    query: ConversationQuery,
    response: string,
    relevantBusinesses: VectorSearchResult[]
  ): Promise<string[]> {
    try {
      const suggestionPrompt = `
        Based on the user's query: "${query.message}"
        And the response provided: "${response}"
        And the relevant businesses found: ${relevantBusinesses.map(b => b.metadata.name).join(', ')}
        
        Generate 3 helpful follow-up questions the user might want to ask. 
        Make them specific and actionable.
        Return only the questions, one per line, without numbering or bullets.
      `;

      const payload = {
        anthropic_version: 'bedrock-2023-05-31',
        max_tokens: 200,
        temperature: 0.7,
        messages: [
          {
            role: 'user',
            content: suggestionPrompt,
          },
        ],
      };

      const command = new InvokeModelCommand({
        modelId: this.modelId,
        body: JSON.stringify(payload),
        contentType: 'application/json',
        accept: 'application/json',
      });

      const response_result = await this.client.send(command);
      const responseBody = JSON.parse(new TextDecoder().decode(response_result.body));
      
      const suggestions = responseBody.content[0].text
        .split('\n')
        .filter((line: string) => line.trim().length > 0)
        .slice(0, 3);
      
      return suggestions;
    } catch (error) {
      console.error('Error generating suggestions:', error);
      return []; // Return empty array if suggestions fail
    }
  }

  /**
   * Build system prompt for the conversational AI
   */
  private buildSystemPrompt(): string {
    return `
      You are a professional and knowledgeable local concierge for the Cravin Concierge application. 
      Your role is to help users discover and learn about local businesses including sports clubs, restaurants, and shops.
      
      Guidelines for your responses:
      1. Be conversational, friendly, and professional
      2. Provide accurate information based on the business data provided
      3. Include specific details like location, contact information, and key features when available
      4. If multiple businesses match the query, present them in a helpful comparison format
      5. Always be helpful and suggest alternatives if the exact request cannot be fulfilled
      6. Use a warm, welcoming tone as if you're a local expert helping a friend
      7. Include practical information like opening hours, contact details, and booking information when relevant
      8. If no relevant businesses are found, politely explain this and suggest broader alternatives
      
      Remember: You are representing the Cravin Concierge brand, so maintain a high standard of service and professionalism.
    `;
  }

  /**
   * Build user prompt with query and business context
   */
  private buildUserPrompt(query: ConversationQuery, relevantBusinesses: VectorSearchResult[]): string {
    let prompt = `User Query: "${query.message}"\n\n`;
    
    if (query.user_location) {
      prompt += `User Location: ${query.user_location.address || 'Not specified'}\n`;
      if (query.user_location.emirate) {
        prompt += `Emirate: ${query.user_location.emirate}\n`;
      }
    }
    
    if (query.preferences) {
      prompt += `User Preferences:\n`;
      if (query.preferences.business_type) {
        prompt += `- Business Type: ${query.preferences.business_type}\n`;
      }
      if (query.preferences.price_range) {
        prompt += `- Price Range: ${query.preferences.price_range}\n`;
      }
      if (query.preferences.cuisine_type) {
        prompt += `- Cuisine Type: ${query.preferences.cuisine_type}\n`;
      }
      if (query.preferences.sport_type) {
        prompt += `- Sport Type: ${query.preferences.sport_type}\n`;
      }
    }
    
    if (query.context) {
      prompt += `\nConversation Context: ${query.context}\n`;
    }
    
    prompt += `\nRelevant Businesses Found:\n`;
    
    if (relevantBusinesses.length === 0) {
      prompt += `No specific businesses found matching the query. Please provide a helpful response suggesting alternatives or asking for more specific information.\n`;
    } else {
      relevantBusinesses.forEach((business, index) => {
        prompt += `\n${index + 1}. ${business.metadata.name} (${business.metadata.type})\n`;
        prompt += `   Content: ${business.content}\n`;
        if (business.metadata.location) {
          prompt += `   Location: ${business.metadata.location}\n`;
        }
        if (business.metadata.category) {
          prompt += `   Category: ${business.metadata.category}\n`;
        }
        if (business.metadata.tags && business.metadata.tags.length > 0) {
          prompt += `   Tags: ${business.metadata.tags.join(', ')}\n`;
        }
        prompt += `   Relevance Score: ${(business.similarity_score * 100).toFixed(1)}%\n`;
      });
    }
    
    prompt += `\nPlease provide a helpful, conversational response that addresses the user's query using the business information provided. Be specific and include actionable information.`;
    
    return prompt;
  }

  /**
   * Test the Bedrock connection
   */
  async testConnection(): Promise<boolean> {
    try {
      const testPayload = {
        anthropic_version: 'bedrock-2023-05-31',
        max_tokens: 10,
        messages: [
          {
            role: 'user',
            content: 'Hello',
          },
        ],
      };

      const command = new InvokeModelCommand({
        modelId: this.modelId,
        body: JSON.stringify(testPayload),
        contentType: 'application/json',
        accept: 'application/json',
      });

      await this.client.send(command);
      return true;
    } catch (error) {
      console.error('Bedrock connection test failed:', error);
      return false;
    }
  }
}
