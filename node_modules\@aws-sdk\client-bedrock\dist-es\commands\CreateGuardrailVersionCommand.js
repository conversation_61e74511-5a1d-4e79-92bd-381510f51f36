import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { CreateGuardrailVersionRequestFilterSensitiveLog, } from "../models/models_0";
import { de_CreateGuardrailVersionCommand, se_CreateGuardrailVersionCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class CreateGuardrailVersionCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockControlPlaneService", "CreateGuardrailVersion", {})
    .n("BedrockClient", "CreateGuardrailVersionCommand")
    .f(CreateGuardrailVersionRequestFilterSensitiveLog, void 0)
    .ser(se_CreateGuardrailVersionCommand)
    .de(de_CreateGuardrailVersionCommand)
    .build() {
}
