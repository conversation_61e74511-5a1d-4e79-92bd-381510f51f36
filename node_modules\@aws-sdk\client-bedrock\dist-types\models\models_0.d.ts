import { ExceptionOptionType as __ExceptionOptionType } from "@smithy/smithy-client";
import { DocumentType as __DocumentType } from "@smithy/types";
import { BedrockServiceException as __BaseException } from "./BedrockServiceException";
/**
 * <p>The request is denied because of missing access permissions.</p>
 * @public
 */
export declare class AccessDeniedException extends __BaseException {
    readonly name: "AccessDeniedException";
    readonly $fault: "client";
    /**
     * @internal
     */
    constructor(opts: __ExceptionOptionType<AccessDeniedException, __BaseException>);
}
/**
 * @public
 * @enum
 */
export declare const AgreementStatus: {
    readonly AVAILABLE: "AVAILABLE";
    readonly ERROR: "ERROR";
    readonly NOT_AVAILABLE: "NOT_AVAILABLE";
    readonly PENDING: "PENDING";
};
/**
 * @public
 */
export type AgreementStatus = (typeof AgreementStatus)[keyof typeof AgreementStatus];
/**
 * <p>Information about the agreement availability</p>
 * @public
 */
export interface AgreementAvailability {
    /**
     * <p>Status of the agreement.</p>
     * @public
     */
    status: AgreementStatus | undefined;
    /**
     * <p>Error message.</p>
     * @public
     */
    errorMessage?: string | undefined;
}
/**
 * @public
 */
export interface GetUseCaseForModelAccessRequest {
}
/**
 * @public
 */
export interface GetUseCaseForModelAccessResponse {
    /**
     * <p>Get customer profile Response.</p>
     * @public
     */
    formData: Uint8Array | undefined;
}
/**
 * <p>An internal server error occurred. Retry your request.</p>
 * @public
 */
export declare class InternalServerException extends __BaseException {
    readonly name: "InternalServerException";
    readonly $fault: "server";
    /**
     * @internal
     */
    constructor(opts: __ExceptionOptionType<InternalServerException, __BaseException>);
}
/**
 * <p>The specified resource Amazon Resource Name (ARN) was not found. Check the Amazon Resource Name (ARN) and try your request again.</p>
 * @public
 */
export declare class ResourceNotFoundException extends __BaseException {
    readonly name: "ResourceNotFoundException";
    readonly $fault: "client";
    /**
     * @internal
     */
    constructor(opts: __ExceptionOptionType<ResourceNotFoundException, __BaseException>);
}
/**
 * <p>The number of requests exceeds the limit. Resubmit your request later.</p>
 * @public
 */
export declare class ThrottlingException extends __BaseException {
    readonly name: "ThrottlingException";
    readonly $fault: "client";
    /**
     * @internal
     */
    constructor(opts: __ExceptionOptionType<ThrottlingException, __BaseException>);
}
/**
 * <p>Input validation failed. Check your request parameters and retry the request.</p>
 * @public
 */
export declare class ValidationException extends __BaseException {
    readonly name: "ValidationException";
    readonly $fault: "client";
    /**
     * @internal
     */
    constructor(opts: __ExceptionOptionType<ValidationException, __BaseException>);
}
/**
 * @public
 */
export interface PutUseCaseForModelAccessRequest {
    /**
     * <p>Put customer profile Request.</p>
     * @public
     */
    formData: Uint8Array | undefined;
}
/**
 * @public
 */
export interface PutUseCaseForModelAccessResponse {
}
/**
 * <p>Error occurred because of a conflict while performing an operation.</p>
 * @public
 */
export declare class ConflictException extends __BaseException {
    readonly name: "ConflictException";
    readonly $fault: "client";
    /**
     * @internal
     */
    constructor(opts: __ExceptionOptionType<ConflictException, __BaseException>);
}
/**
 * <p>The configuration of a virtual private cloud (VPC). For more information, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/usingVPC.html">Protect your data using Amazon Virtual Private Cloud and Amazon Web Services PrivateLink</a>.</p>
 * @public
 */
export interface VpcConfig {
    /**
     * <p>An array of IDs for each subnet in the VPC to use.</p>
     * @public
     */
    subnetIds: string[] | undefined;
    /**
     * <p>An array of IDs for each security group in the VPC to use.</p>
     * @public
     */
    securityGroupIds: string[] | undefined;
}
/**
 * <p>Specifies the configuration for a Amazon SageMaker endpoint.</p>
 * @public
 */
export interface SageMakerEndpoint {
    /**
     * <p>The number of Amazon EC2 compute instances to deploy for initial endpoint creation.</p>
     * @public
     */
    initialInstanceCount: number | undefined;
    /**
     * <p>The Amazon EC2 compute instance type to deploy for hosting the model.</p>
     * @public
     */
    instanceType: string | undefined;
    /**
     * <p>The ARN of the IAM role that Amazon SageMaker can assume to access model artifacts and docker image for deployment on Amazon EC2 compute instances or for batch transform jobs.</p>
     * @public
     */
    executionRole: string | undefined;
    /**
     * <p>The Amazon Web Services KMS key that Amazon SageMaker uses to encrypt data on the storage volume attached to the Amazon EC2 compute instance that hosts the endpoint.</p>
     * @public
     */
    kmsEncryptionKey?: string | undefined;
    /**
     * <p>The VPC configuration for the endpoint.</p>
     * @public
     */
    vpc?: VpcConfig | undefined;
}
/**
 * <p>Specifies the configuration for the endpoint.</p>
 * @public
 */
export type EndpointConfig = EndpointConfig.SageMakerMember | EndpointConfig.$UnknownMember;
/**
 * @public
 */
export declare namespace EndpointConfig {
    /**
     * <p>The configuration specific to Amazon SageMaker for the endpoint.</p>
     * @public
     */
    interface SageMakerMember {
        sageMaker: SageMakerEndpoint;
        $unknown?: never;
    }
    /**
     * @public
     */
    interface $UnknownMember {
        sageMaker?: never;
        $unknown: [string, any];
    }
    interface Visitor<T> {
        sageMaker: (value: SageMakerEndpoint) => T;
        _: (name: string, value: any) => T;
    }
    const visit: <T>(value: EndpointConfig, visitor: Visitor<T>) => T;
}
/**
 * <p>Definition of the key/value pair for a tag.</p>
 * @public
 */
export interface Tag {
    /**
     * <p>Key for the tag.</p>
     * @public
     */
    key: string | undefined;
    /**
     * <p>Value for the tag.</p>
     * @public
     */
    value: string | undefined;
}
/**
 * @public
 */
export interface CreateMarketplaceModelEndpointRequest {
    /**
     * <p>The ARN of the model from Amazon Bedrock Marketplace that you want to deploy to the endpoint.</p>
     * @public
     */
    modelSourceIdentifier: string | undefined;
    /**
     * <p>The configuration for the endpoint, including the number and type of instances to use.</p>
     * @public
     */
    endpointConfig: EndpointConfig | undefined;
    /**
     * <p>Indicates whether you accept the end-user license agreement (EULA) for the model. Set to <code>true</code> to accept the EULA.</p>
     * @public
     */
    acceptEula?: boolean | undefined;
    /**
     * <p>The name of the endpoint. This name must be unique within your Amazon Web Services account and region.</p>
     * @public
     */
    endpointName: string | undefined;
    /**
     * <p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. This token is listed as not required because Amazon Web Services SDKs automatically generate it for you and set this parameter. If you're not using the Amazon Web Services SDK or the CLI, you must provide this token or the action will fail.</p>
     * @public
     */
    clientRequestToken?: string | undefined;
    /**
     * <p>An array of key-value pairs to apply to the underlying Amazon SageMaker endpoint. You can use these tags to organize and identify your Amazon Web Services resources.</p>
     * @public
     */
    tags?: Tag[] | undefined;
}
/**
 * @public
 * @enum
 */
export declare const Status: {
    readonly INCOMPATIBLE_ENDPOINT: "INCOMPATIBLE_ENDPOINT";
    readonly REGISTERED: "REGISTERED";
};
/**
 * @public
 */
export type Status = (typeof Status)[keyof typeof Status];
/**
 * <p>Contains details about an endpoint for a model from Amazon Bedrock Marketplace.</p>
 * @public
 */
export interface MarketplaceModelEndpoint {
    /**
     * <p>The Amazon Resource Name (ARN) of the endpoint.</p>
     * @public
     */
    endpointArn: string | undefined;
    /**
     * <p>The ARN of the model from Amazon Bedrock Marketplace that is deployed on this endpoint.</p>
     * @public
     */
    modelSourceIdentifier: string | undefined;
    /**
     * <p>The overall status of the endpoint in Amazon Bedrock Marketplace (e.g., ACTIVE, INACTIVE).</p>
     * @public
     */
    status?: Status | undefined;
    /**
     * <p>Additional information about the overall status, if available.</p>
     * @public
     */
    statusMessage?: string | undefined;
    /**
     * <p>The timestamp when the endpoint was registered.</p>
     * @public
     */
    createdAt: Date | undefined;
    /**
     * <p>The timestamp when the endpoint was last updated.</p>
     * @public
     */
    updatedAt: Date | undefined;
    /**
     * <p>The configuration of the endpoint, including the number and type of instances used.</p>
     * @public
     */
    endpointConfig: EndpointConfig | undefined;
    /**
     * <p>The current status of the endpoint (e.g., Creating, InService, Updating, Failed).</p>
     * @public
     */
    endpointStatus: string | undefined;
    /**
     * <p>Additional information about the endpoint status, if available.</p>
     * @public
     */
    endpointStatusMessage?: string | undefined;
}
/**
 * @public
 */
export interface CreateMarketplaceModelEndpointResponse {
    /**
     * <p>Details about the created endpoint.</p>
     * @public
     */
    marketplaceModelEndpoint: MarketplaceModelEndpoint | undefined;
}
/**
 * <p>The number of requests exceeds the service quota. Resubmit your request later.</p>
 * @public
 */
export declare class ServiceQuotaExceededException extends __BaseException {
    readonly name: "ServiceQuotaExceededException";
    readonly $fault: "client";
    /**
     * @internal
     */
    constructor(opts: __ExceptionOptionType<ServiceQuotaExceededException, __BaseException>);
}
/**
 * @public
 */
export interface DeleteMarketplaceModelEndpointRequest {
    /**
     * <p>The Amazon Resource Name (ARN) of the endpoint you want to delete.</p>
     * @public
     */
    endpointArn: string | undefined;
}
/**
 * @public
 */
export interface DeleteMarketplaceModelEndpointResponse {
}
/**
 * @public
 */
export interface DeregisterMarketplaceModelEndpointRequest {
    /**
     * <p>The Amazon Resource Name (ARN) of the endpoint you want to deregister.</p>
     * @public
     */
    endpointArn: string | undefined;
}
/**
 * @public
 */
export interface DeregisterMarketplaceModelEndpointResponse {
}
/**
 * <p>Returned if the service cannot complete the request.</p>
 * @public
 */
export declare class ServiceUnavailableException extends __BaseException {
    readonly name: "ServiceUnavailableException";
    readonly $fault: "server";
    /**
     * @internal
     */
    constructor(opts: __ExceptionOptionType<ServiceUnavailableException, __BaseException>);
}
/**
 * @public
 */
export interface GetMarketplaceModelEndpointRequest {
    /**
     * <p>The Amazon Resource Name (ARN) of the endpoint you want to get information about.</p>
     * @public
     */
    endpointArn: string | undefined;
}
/**
 * @public
 */
export interface GetMarketplaceModelEndpointResponse {
    /**
     * <p>Details about the requested endpoint.</p>
     * @public
     */
    marketplaceModelEndpoint?: MarketplaceModelEndpoint | undefined;
}
/**
 * @public
 */
export interface ListMarketplaceModelEndpointsRequest {
    /**
     * <p>The maximum number of results to return in a single call. If more results are available, the operation returns a <code>NextToken</code> value.</p>
     * @public
     */
    maxResults?: number | undefined;
    /**
     * <p>The token for the next set of results. You receive this token from a previous <code>ListMarketplaceModelEndpoints</code> call.</p>
     * @public
     */
    nextToken?: string | undefined;
    /**
     * <p>If specified, only endpoints for the given model source identifier are returned.</p>
     * @public
     */
    modelSourceEquals?: string | undefined;
}
/**
 * <p>Provides a summary of an endpoint for a model from Amazon Bedrock Marketplace.</p>
 * @public
 */
export interface MarketplaceModelEndpointSummary {
    /**
     * <p>The Amazon Resource Name (ARN) of the endpoint.</p>
     * @public
     */
    endpointArn: string | undefined;
    /**
     * <p>The ARN of the model from Amazon Bedrock Marketplace that is deployed on this endpoint.</p>
     * @public
     */
    modelSourceIdentifier: string | undefined;
    /**
     * <p>The overall status of the endpoint in Amazon Bedrock Marketplace.</p>
     * @public
     */
    status?: Status | undefined;
    /**
     * <p>Additional information about the overall status, if available.</p>
     * @public
     */
    statusMessage?: string | undefined;
    /**
     * <p>The timestamp when the endpoint was created.</p>
     * @public
     */
    createdAt: Date | undefined;
    /**
     * <p>The timestamp when the endpoint was last updated.</p>
     * @public
     */
    updatedAt: Date | undefined;
}
/**
 * @public
 */
export interface ListMarketplaceModelEndpointsResponse {
    /**
     * <p>An array of endpoint summaries.</p>
     * @public
     */
    marketplaceModelEndpoints?: MarketplaceModelEndpointSummary[] | undefined;
    /**
     * <p>The token for the next set of results. Use this token to get the next set of results.</p>
     * @public
     */
    nextToken?: string | undefined;
}
/**
 * @public
 */
export interface RegisterMarketplaceModelEndpointRequest {
    /**
     * <p>The ARN of the Amazon SageMaker endpoint you want to register with Amazon Bedrock Marketplace.</p>
     * @public
     */
    endpointIdentifier: string | undefined;
    /**
     * <p>The ARN of the model from Amazon Bedrock Marketplace that is deployed on the endpoint.</p>
     * @public
     */
    modelSourceIdentifier: string | undefined;
}
/**
 * @public
 */
export interface RegisterMarketplaceModelEndpointResponse {
    /**
     * <p>Details about the registered endpoint.</p>
     * @public
     */
    marketplaceModelEndpoint: MarketplaceModelEndpoint | undefined;
}
/**
 * @public
 */
export interface UpdateMarketplaceModelEndpointRequest {
    /**
     * <p>The Amazon Resource Name (ARN) of the endpoint you want to update.</p>
     * @public
     */
    endpointArn: string | undefined;
    /**
     * <p>The new configuration for the endpoint, including the number and type of instances to use.</p>
     * @public
     */
    endpointConfig: EndpointConfig | undefined;
    /**
     * <p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. This token is listed as not required because Amazon Web Services SDKs automatically generate it for you and set this parameter. If you're not using the Amazon Web Services SDK or the CLI, you must provide this token or the action will fail.</p>
     * @public
     */
    clientRequestToken?: string | undefined;
}
/**
 * @public
 */
export interface UpdateMarketplaceModelEndpointResponse {
    /**
     * <p>Details about the updated endpoint.</p>
     * @public
     */
    marketplaceModelEndpoint: MarketplaceModelEndpoint | undefined;
}
/**
 * <p>The Amazon S3 data source of the model to import. </p>
 * @public
 */
export interface S3DataSource {
    /**
     * <p>The URI of the Amazon S3 data source.</p>
     * @public
     */
    s3Uri: string | undefined;
}
/**
 * <p>The data source of the model to import.</p>
 * @public
 */
export type ModelDataSource = ModelDataSource.S3DataSourceMember | ModelDataSource.$UnknownMember;
/**
 * @public
 */
export declare namespace ModelDataSource {
    /**
     * <p>The Amazon S3 data source of the model to import.</p>
     * @public
     */
    interface S3DataSourceMember {
        s3DataSource: S3DataSource;
        $unknown?: never;
    }
    /**
     * @public
     */
    interface $UnknownMember {
        s3DataSource?: never;
        $unknown: [string, any];
    }
    interface Visitor<T> {
        s3DataSource: (value: S3DataSource) => T;
        _: (name: string, value: any) => T;
    }
    const visit: <T>(value: ModelDataSource, visitor: Visitor<T>) => T;
}
/**
 * @public
 */
export interface CreateCustomModelRequest {
    /**
     * <p>A unique name for the custom model.</p>
     * @public
     */
    modelName: string | undefined;
    /**
     * <p>The data source for the model. The Amazon S3 URI in the model source must be for the Amazon-managed Amazon S3 bucket containing your model artifacts.</p>
     * @public
     */
    modelSourceConfig: ModelDataSource | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the customer managed KMS key to encrypt the custom model. If you don't provide a KMS key, Amazon Bedrock uses an Amazon Web Services-managed KMS key to encrypt the model. </p> <p>If you provide a customer managed KMS key, your Amazon Bedrock service role must have permissions to use it. For more information see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/encryption-import-model.html">Encryption of imported models</a>. </p>
     * @public
     */
    modelKmsKeyArn?: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of an IAM service role that Amazon Bedrock assumes to perform tasks on your behalf. This role must have permissions to access the Amazon S3 bucket containing your model artifacts and the KMS key (if specified). For more information, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/model-import-iam-role.html">Setting up an IAM service role for importing models</a> in the Amazon Bedrock User Guide.</p>
     * @public
     */
    roleArn?: string | undefined;
    /**
     * <p>A list of key-value pairs to associate with the custom model resource. You can use these tags to organize and identify your resources.</p> <p>For more information, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/tagging.html">Tagging resources</a> in the <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/what-is-service.html">Amazon Bedrock User Guide</a>.</p>
     * @public
     */
    modelTags?: Tag[] | undefined;
    /**
     * <p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href="https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html">Ensuring idempotency</a>.</p>
     * @public
     */
    clientRequestToken?: string | undefined;
}
/**
 * @public
 */
export interface CreateCustomModelResponse {
    /**
     * <p>The Amazon Resource Name (ARN) of the new custom model.</p>
     * @public
     */
    modelArn: string | undefined;
}
/**
 * <p>The request contains more tags than can be associated with a resource (50 tags per resource). The maximum number of tags includes both existing tags and those included in your current request. </p>
 * @public
 */
export declare class TooManyTagsException extends __BaseException {
    readonly name: "TooManyTagsException";
    readonly $fault: "client";
    /**
     * <p>The name of the resource with too many tags.</p>
     * @public
     */
    resourceName?: string | undefined;
    /**
     * @internal
     */
    constructor(opts: __ExceptionOptionType<TooManyTagsException, __BaseException>);
}
/**
 * @public
 */
export interface DeleteCustomModelRequest {
    /**
     * <p>Name of the model to delete.</p>
     * @public
     */
    modelIdentifier: string | undefined;
}
/**
 * @public
 */
export interface DeleteCustomModelResponse {
}
/**
 * @public
 */
export interface GetCustomModelRequest {
    /**
     * <p>Name or Amazon Resource Name (ARN) of the custom model.</p>
     * @public
     */
    modelIdentifier: string | undefined;
}
/**
 * <p>Details about a teacher model used for model customization.</p>
 * @public
 */
export interface TeacherModelConfig {
    /**
     * <p>The identifier of the teacher model.</p>
     * @public
     */
    teacherModelIdentifier: string | undefined;
    /**
     * <p>The maximum number of tokens requested when the customization job invokes the teacher model.</p>
     * @public
     */
    maxResponseLengthForInference?: number | undefined;
}
/**
 * <p>Settings for distilling a foundation model into a smaller and more efficient model.</p>
 * @public
 */
export interface DistillationConfig {
    /**
     * <p>The teacher model configuration.</p>
     * @public
     */
    teacherModelConfig: TeacherModelConfig | undefined;
}
/**
 * <p>A model customization configuration</p>
 * @public
 */
export type CustomizationConfig = CustomizationConfig.DistillationConfigMember | CustomizationConfig.$UnknownMember;
/**
 * @public
 */
export declare namespace CustomizationConfig {
    /**
     * <p>The Distillation configuration for the custom model.</p>
     * @public
     */
    interface DistillationConfigMember {
        distillationConfig: DistillationConfig;
        $unknown?: never;
    }
    /**
     * @public
     */
    interface $UnknownMember {
        distillationConfig?: never;
        $unknown: [string, any];
    }
    interface Visitor<T> {
        distillationConfig: (value: DistillationConfig) => T;
        _: (name: string, value: any) => T;
    }
    const visit: <T>(value: CustomizationConfig, visitor: Visitor<T>) => T;
}
/**
 * @public
 * @enum
 */
export declare const CustomizationType: {
    readonly CONTINUED_PRE_TRAINING: "CONTINUED_PRE_TRAINING";
    readonly DISTILLATION: "DISTILLATION";
    readonly FINE_TUNING: "FINE_TUNING";
    readonly IMPORTED: "IMPORTED";
};
/**
 * @public
 */
export type CustomizationType = (typeof CustomizationType)[keyof typeof CustomizationType];
/**
 * @public
 * @enum
 */
export declare const ModelStatus: {
    readonly ACTIVE: "Active";
    readonly CREATING: "Creating";
    readonly FAILED: "Failed";
};
/**
 * @public
 */
export type ModelStatus = (typeof ModelStatus)[keyof typeof ModelStatus];
/**
 * <p>S3 Location of the output data.</p>
 * @public
 */
export interface OutputDataConfig {
    /**
     * <p>The S3 URI where the output data is stored.</p>
     * @public
     */
    s3Uri: string | undefined;
}
/**
 * <p>A storage location for invocation logs.</p>
 * @public
 */
export type InvocationLogSource = InvocationLogSource.S3UriMember | InvocationLogSource.$UnknownMember;
/**
 * @public
 */
export declare namespace InvocationLogSource {
    /**
     * <p>The URI of an invocation log in a bucket.</p>
     * @public
     */
    interface S3UriMember {
        s3Uri: string;
        $unknown?: never;
    }
    /**
     * @public
     */
    interface $UnknownMember {
        s3Uri?: never;
        $unknown: [string, any];
    }
    interface Visitor<T> {
        s3Uri: (value: string) => T;
        _: (name: string, value: any) => T;
    }
    const visit: <T>(value: InvocationLogSource, visitor: Visitor<T>) => T;
}
/**
 * <p>A mapping of a metadata key to a value that it should or should not equal.</p>
 * @public
 */
export interface RequestMetadataBaseFilters {
    /**
     * <p>Include results where the key equals the value.</p>
     * @public
     */
    equals?: Record<string, string> | undefined;
    /**
     * <p>Include results where the key does not equal the value.</p>
     * @public
     */
    notEquals?: Record<string, string> | undefined;
}
/**
 * <p>Rules for filtering invocation logs. A filter can be a mapping of a metadata key to a value that it should or should not equal (a base filter), or a list of base filters that are all applied with <code>AND</code> or <code>OR</code> logical operators</p>
 * @public
 */
export type RequestMetadataFilters = RequestMetadataFilters.AndAllMember | RequestMetadataFilters.EqualsMember | RequestMetadataFilters.NotEqualsMember | RequestMetadataFilters.OrAllMember | RequestMetadataFilters.$UnknownMember;
/**
 * @public
 */
export declare namespace RequestMetadataFilters {
    /**
     * <p>Include results where the key equals the value.</p>
     * @public
     */
    interface EqualsMember {
        equals: Record<string, string>;
        notEquals?: never;
        andAll?: never;
        orAll?: never;
        $unknown?: never;
    }
    /**
     * <p>Include results where the key does not equal the value.</p>
     * @public
     */
    interface NotEqualsMember {
        equals?: never;
        notEquals: Record<string, string>;
        andAll?: never;
        orAll?: never;
        $unknown?: never;
    }
    /**
     * <p>Include results where all of the based filters match.</p>
     * @public
     */
    interface AndAllMember {
        equals?: never;
        notEquals?: never;
        andAll: RequestMetadataBaseFilters[];
        orAll?: never;
        $unknown?: never;
    }
    /**
     * <p>Include results where any of the base filters match.</p>
     * @public
     */
    interface OrAllMember {
        equals?: never;
        notEquals?: never;
        andAll?: never;
        orAll: RequestMetadataBaseFilters[];
        $unknown?: never;
    }
    /**
     * @public
     */
    interface $UnknownMember {
        equals?: never;
        notEquals?: never;
        andAll?: never;
        orAll?: never;
        $unknown: [string, any];
    }
    interface Visitor<T> {
        equals: (value: Record<string, string>) => T;
        notEquals: (value: Record<string, string>) => T;
        andAll: (value: RequestMetadataBaseFilters[]) => T;
        orAll: (value: RequestMetadataBaseFilters[]) => T;
        _: (name: string, value: any) => T;
    }
    const visit: <T>(value: RequestMetadataFilters, visitor: Visitor<T>) => T;
}
/**
 * <p>Settings for using invocation logs to customize a model.</p>
 * @public
 */
export interface InvocationLogsConfig {
    /**
     * <p>Whether to use the model's response for training, or just the prompt. The default value is <code>False</code>.</p>
     * @public
     */
    usePromptResponse?: boolean | undefined;
    /**
     * <p>The source of the invocation logs.</p>
     * @public
     */
    invocationLogSource: InvocationLogSource | undefined;
    /**
     * <p>Rules for filtering invocation logs based on request metadata.</p>
     * @public
     */
    requestMetadataFilters?: RequestMetadataFilters | undefined;
}
/**
 * <p>S3 Location of the training data.</p>
 * @public
 */
export interface TrainingDataConfig {
    /**
     * <p>The S3 URI where the training data is stored.</p>
     * @public
     */
    s3Uri?: string | undefined;
    /**
     * <p>Settings for using invocation logs to customize a model.</p>
     * @public
     */
    invocationLogsConfig?: InvocationLogsConfig | undefined;
}
/**
 * <p>Metrics associated with the custom job.</p>
 * @public
 */
export interface TrainingMetrics {
    /**
     * <p>Loss metric associated with the custom job.</p>
     * @public
     */
    trainingLoss?: number | undefined;
}
/**
 * <p>Information about a validator.</p>
 * @public
 */
export interface Validator {
    /**
     * <p>The S3 URI where the validation data is stored.</p>
     * @public
     */
    s3Uri: string | undefined;
}
/**
 * <p>Array of up to 10 validators.</p>
 * @public
 */
export interface ValidationDataConfig {
    /**
     * <p>Information about the validators.</p>
     * @public
     */
    validators: Validator[] | undefined;
}
/**
 * <p>The metric for the validator.</p>
 * @public
 */
export interface ValidatorMetric {
    /**
     * <p>The validation loss associated with this validator.</p>
     * @public
     */
    validationLoss?: number | undefined;
}
/**
 * @public
 */
export interface GetCustomModelResponse {
    /**
     * <p>Amazon Resource Name (ARN) associated with this model.</p>
     * @public
     */
    modelArn: string | undefined;
    /**
     * <p>Model name associated with this model.</p>
     * @public
     */
    modelName: string | undefined;
    /**
     * <p>Job name associated with this model.</p>
     * @public
     */
    jobName?: string | undefined;
    /**
     * <p>Job Amazon Resource Name (ARN) associated with this model. For models that you create with the <a href="https://docs.aws.amazon.com/bedrock/latest/APIReference/API_CreateCustomModel.html">CreateCustomModel</a> API operation, this is <code>NULL</code>.</p>
     * @public
     */
    jobArn?: string | undefined;
    /**
     * <p>Amazon Resource Name (ARN) of the base model.</p>
     * @public
     */
    baseModelArn?: string | undefined;
    /**
     * <p>The type of model customization.</p>
     * @public
     */
    customizationType?: CustomizationType | undefined;
    /**
     * <p>The custom model is encrypted at rest using this key.</p>
     * @public
     */
    modelKmsKeyArn?: string | undefined;
    /**
     * <p>Hyperparameter values associated with this model. For details on the format for different models, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/custom-models-hp.html">Custom model hyperparameters</a>.</p>
     * @public
     */
    hyperParameters?: Record<string, string> | undefined;
    /**
     * <p>Contains information about the training dataset.</p>
     * @public
     */
    trainingDataConfig?: TrainingDataConfig | undefined;
    /**
     * <p>Contains information about the validation dataset.</p>
     * @public
     */
    validationDataConfig?: ValidationDataConfig | undefined;
    /**
     * <p>Output data configuration associated with this custom model.</p>
     * @public
     */
    outputDataConfig?: OutputDataConfig | undefined;
    /**
     * <p>Contains training metrics from the job creation.</p>
     * @public
     */
    trainingMetrics?: TrainingMetrics | undefined;
    /**
     * <p>The validation metrics from the job creation.</p>
     * @public
     */
    validationMetrics?: ValidatorMetric[] | undefined;
    /**
     * <p>Creation time of the model.</p>
     * @public
     */
    creationTime: Date | undefined;
    /**
     * <p>The customization configuration for the custom model.</p>
     * @public
     */
    customizationConfig?: CustomizationConfig | undefined;
    /**
     * <p>The current status of the custom model. Possible values include:</p> <ul> <li> <p> <code>Creating</code> - The model is being created and validated.</p> </li> <li> <p> <code>Active</code> - The model has been successfully created and is ready for use.</p> </li> <li> <p> <code>Failed</code> - The model creation process failed. Check the <code>failureMessage</code> field for details.</p> </li> </ul>
     * @public
     */
    modelStatus?: ModelStatus | undefined;
    /**
     * <p>A failure message for any issues that occurred when creating the custom model. This is included for only a failed CreateCustomModel operation.</p>
     * @public
     */
    failureMessage?: string | undefined;
}
/**
 * @public
 * @enum
 */
export declare const SortModelsBy: {
    readonly CREATION_TIME: "CreationTime";
};
/**
 * @public
 */
export type SortModelsBy = (typeof SortModelsBy)[keyof typeof SortModelsBy];
/**
 * @public
 * @enum
 */
export declare const SortOrder: {
    readonly ASCENDING: "Ascending";
    readonly DESCENDING: "Descending";
};
/**
 * @public
 */
export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder];
/**
 * @public
 */
export interface ListCustomModelsRequest {
    /**
     * <p>Return custom models created before the specified time. </p>
     * @public
     */
    creationTimeBefore?: Date | undefined;
    /**
     * <p>Return custom models created after the specified time. </p>
     * @public
     */
    creationTimeAfter?: Date | undefined;
    /**
     * <p>Return custom models only if the job name contains these characters.</p>
     * @public
     */
    nameContains?: string | undefined;
    /**
     * <p>Return custom models only if the base model Amazon Resource Name (ARN) matches this parameter.</p>
     * @public
     */
    baseModelArnEquals?: string | undefined;
    /**
     * <p>Return custom models only if the foundation model Amazon Resource Name (ARN) matches this parameter.</p>
     * @public
     */
    foundationModelArnEquals?: string | undefined;
    /**
     * <p>The maximum number of results to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>
     * @public
     */
    maxResults?: number | undefined;
    /**
     * <p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results.</p>
     * @public
     */
    nextToken?: string | undefined;
    /**
     * <p>The field to sort by in the returned list of models.</p>
     * @public
     */
    sortBy?: SortModelsBy | undefined;
    /**
     * <p>The sort order of the results.</p>
     * @public
     */
    sortOrder?: SortOrder | undefined;
    /**
     * <p>Return custom models depending on if the current account owns them (<code>true</code>) or if they were shared with the current account (<code>false</code>).</p>
     * @public
     */
    isOwned?: boolean | undefined;
    /**
     * <p>The status of them model to filter results by. Possible values include:</p> <ul> <li> <p> <code>Creating</code> - Include only models that are currently being created and validated.</p> </li> <li> <p> <code>Active</code> - Include only models that have been successfully created and are ready for use.</p> </li> <li> <p> <code>Failed</code> - Include only models where the creation process failed.</p> </li> </ul> <p>If you don't specify a status, the API returns models in all states.</p>
     * @public
     */
    modelStatus?: ModelStatus | undefined;
}
/**
 * <p>Summary information for a custom model.</p>
 * @public
 */
export interface CustomModelSummary {
    /**
     * <p>The Amazon Resource Name (ARN) of the custom model.</p>
     * @public
     */
    modelArn: string | undefined;
    /**
     * <p>The name of the custom model.</p>
     * @public
     */
    modelName: string | undefined;
    /**
     * <p>Creation time of the model.</p>
     * @public
     */
    creationTime: Date | undefined;
    /**
     * <p>The base model Amazon Resource Name (ARN).</p>
     * @public
     */
    baseModelArn: string | undefined;
    /**
     * <p>The base model name.</p>
     * @public
     */
    baseModelName: string | undefined;
    /**
     * <p>Specifies whether to carry out continued pre-training of a model or whether to fine-tune it. For more information, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/custom-models.html">Custom models</a>.</p>
     * @public
     */
    customizationType?: CustomizationType | undefined;
    /**
     * <p>The unique identifier of the account that owns the model.</p>
     * @public
     */
    ownerAccountId?: string | undefined;
    /**
     * <p>The current status of the custom model. Possible values include:</p> <ul> <li> <p> <code>Creating</code> - The model is being created and validated.</p> </li> <li> <p> <code>Active</code> - The model has been successfully created and is ready for use.</p> </li> <li> <p> <code>Failed</code> - The model creation process failed.</p> </li> </ul>
     * @public
     */
    modelStatus?: ModelStatus | undefined;
}
/**
 * @public
 */
export interface ListCustomModelsResponse {
    /**
     * <p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, use this token when making another request in the <code>nextToken</code> field to return the next batch of results.</p>
     * @public
     */
    nextToken?: string | undefined;
    /**
     * <p>Model summaries.</p>
     * @public
     */
    modelSummaries?: CustomModelSummary[] | undefined;
}
/**
 * @public
 */
export interface BatchDeleteEvaluationJobRequest {
    /**
     * <p>A list of one or more evaluation job Amazon Resource Names (ARNs) you want to delete.</p>
     * @public
     */
    jobIdentifiers: string[] | undefined;
}
/**
 * <p>A JSON array that provides the status of the evaluation jobs being deleted.</p>
 * @public
 */
export interface BatchDeleteEvaluationJobError {
    /**
     * <p>The ARN of the evaluation job being deleted.</p>
     * @public
     */
    jobIdentifier: string | undefined;
    /**
     * <p>A HTTP status code of the evaluation job being deleted.</p>
     * @public
     */
    code: string | undefined;
    /**
     * <p>A status message about the evaluation job deletion.</p>
     * @public
     */
    message?: string | undefined;
}
/**
 * @public
 * @enum
 */
export declare const EvaluationJobStatus: {
    readonly COMPLETED: "Completed";
    readonly DELETING: "Deleting";
    readonly FAILED: "Failed";
    readonly IN_PROGRESS: "InProgress";
    readonly STOPPED: "Stopped";
    readonly STOPPING: "Stopping";
};
/**
 * @public
 */
export type EvaluationJobStatus = (typeof EvaluationJobStatus)[keyof typeof EvaluationJobStatus];
/**
 * <p>An evaluation job for deletion, and it’s current status.</p>
 * @public
 */
export interface BatchDeleteEvaluationJobItem {
    /**
     * <p>The Amazon Resource Name (ARN) of the evaluation job for deletion.</p>
     * @public
     */
    jobIdentifier: string | undefined;
    /**
     * <p>The status of the evaluation job for deletion.</p>
     * @public
     */
    jobStatus: EvaluationJobStatus | undefined;
}
/**
 * @public
 */
export interface BatchDeleteEvaluationJobResponse {
    /**
     * <p>A JSON object containing the HTTP status codes and the ARNs of evaluation jobs that failed to be deleted.</p>
     * @public
     */
    errors: BatchDeleteEvaluationJobError[] | undefined;
    /**
     * <p>The list of evaluation jobs for deletion.</p>
     * @public
     */
    evaluationJobs: BatchDeleteEvaluationJobItem[] | undefined;
}
/**
 * @public
 * @enum
 */
export declare const ApplicationType: {
    readonly MODEL_EVALUATION: "ModelEvaluation";
    readonly RAG_EVALUATION: "RagEvaluation";
};
/**
 * @public
 */
export type ApplicationType = (typeof ApplicationType)[keyof typeof ApplicationType];
/**
 * <p>Defines the value for one rating in a custom metric rating scale.</p>
 * @public
 */
export type RatingScaleItemValue = RatingScaleItemValue.FloatValueMember | RatingScaleItemValue.StringValueMember | RatingScaleItemValue.$UnknownMember;
/**
 * @public
 */
export declare namespace RatingScaleItemValue {
    /**
     * <p>A string representing the value for a rating in a custom metric rating scale.</p>
     * @public
     */
    interface StringValueMember {
        stringValue: string;
        floatValue?: never;
        $unknown?: never;
    }
    /**
     * <p>A floating point number representing the value for a rating in a custom metric rating scale.</p>
     * @public
     */
    interface FloatValueMember {
        stringValue?: never;
        floatValue: number;
        $unknown?: never;
    }
    /**
     * @public
     */
    interface $UnknownMember {
        stringValue?: never;
        floatValue?: never;
        $unknown: [string, any];
    }
    interface Visitor<T> {
        stringValue: (value: string) => T;
        floatValue: (value: number) => T;
        _: (name: string, value: any) => T;
    }
    const visit: <T>(value: RatingScaleItemValue, visitor: Visitor<T>) => T;
}
/**
 * <p>Defines the value and corresponding definition for one rating in a custom metric rating scale.</p>
 * @public
 */
export interface RatingScaleItem {
    /**
     * <p>Defines the definition for one rating in a custom metric rating scale.</p>
     * @public
     */
    definition: string | undefined;
    /**
     * <p>Defines the value for one rating in a custom metric rating scale.</p>
     * @public
     */
    value: RatingScaleItemValue | undefined;
}
/**
 * <p>The definition of a custom metric for use in an Amazon Bedrock evaluation job. A custom metric definition includes a metric name, prompt (instructions) and optionally, a rating scale. Your prompt must include a task description and input variables. The required input variables are different for model-as-a-judge and RAG evaluations.</p> <p>For more information about how to define a custom metric in Amazon Bedrock, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/model-evaluation-custom-metrics-prompt-formats.html">Create a prompt for a custom metrics (LLM-as-a-judge model evaluations)</a> and <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/kb-evaluation-custom-metrics-prompt-formats.html">Create a prompt for a custom metrics (RAG evaluations)</a>.</p>
 * @public
 */
export interface CustomMetricDefinition {
    /**
     * <p>The name for a custom metric. Names must be unique in your Amazon Web Services region.</p>
     * @public
     */
    name: string | undefined;
    /**
     * <p>The prompt for a custom metric that instructs the evaluator model how to rate the model or RAG source under evaluation.</p>
     * @public
     */
    instructions: string | undefined;
    /**
     * <p>Defines the rating scale to be used for a custom metric. We recommend that you always define a ratings scale when creating a custom metric. If you don't define a scale, Amazon Bedrock won't be able to visually display the results of the evaluation in the console or calculate average values of numerical scores. For more information on specifying a rating scale, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/model-evaluation-custom-metrics-prompt-formats.html#model-evaluation-custom-metrics-prompt-formats-schema">Specifying an output schema (rating scale)</a>.</p>
     * @public
     */
    ratingScale?: RatingScaleItem[] | undefined;
}
/**
 * <p>An array item definining a single custom metric for use in an Amazon Bedrock evaluation job.</p>
 * @public
 */
export type AutomatedEvaluationCustomMetricSource = AutomatedEvaluationCustomMetricSource.CustomMetricDefinitionMember | AutomatedEvaluationCustomMetricSource.$UnknownMember;
/**
 * @public
 */
export declare namespace AutomatedEvaluationCustomMetricSource {
    /**
     * <p>The definition of a custom metric for use in an Amazon Bedrock evaluation job.</p>
     * @public
     */
    interface CustomMetricDefinitionMember {
        customMetricDefinition: CustomMetricDefinition;
        $unknown?: never;
    }
    /**
     * @public
     */
    interface $UnknownMember {
        customMetricDefinition?: never;
        $unknown: [string, any];
    }
    interface Visitor<T> {
        customMetricDefinition: (value: CustomMetricDefinition) => T;
        _: (name: string, value: any) => T;
    }
    const visit: <T>(value: AutomatedEvaluationCustomMetricSource, visitor: Visitor<T>) => T;
}
/**
 * <p>Defines the model you want to evaluate custom metrics in an Amazon Bedrock evaluation job.</p>
 * @public
 */
export interface CustomMetricBedrockEvaluatorModel {
    /**
     * <p>The Amazon Resource Name (ARN) of the evaluator model for custom metrics. For a list of supported evaluator models, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/evaluation-judge.html">Evaluate model performance using another LLM as a judge</a> and <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/evaluation-kb.html">Evaluate the performance of RAG sources using Amazon Bedrock evaluations</a>.</p>
     * @public
     */
    modelIdentifier: string | undefined;
}
/**
 * <p>Configuration of the evaluator model you want to use to evaluate custom metrics in an Amazon Bedrock evaluation job.</p>
 * @public
 */
export interface CustomMetricEvaluatorModelConfig {
    /**
     * <p>Defines the model you want to evaluate custom metrics in an Amazon Bedrock evaluation job.</p>
     * @public
     */
    bedrockEvaluatorModels: CustomMetricBedrockEvaluatorModel[] | undefined;
}
/**
 * <p>Defines the configuration of custom metrics to be used in an evaluation job. To learn more about using custom metrics in Amazon Bedrock evaluation jobs, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/model-evaluation-custom-metrics-prompt-formats.html">Create a prompt for a custom metrics (LLM-as-a-judge model evaluations)</a> and <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/kb-evaluation-custom-metrics-prompt-formats.html">Create a prompt for a custom metrics (RAG evaluations)</a>.</p>
 * @public
 */
export interface AutomatedEvaluationCustomMetricConfig {
    /**
     * <p>Defines a list of custom metrics to be used in an Amazon Bedrock evaluation job.</p>
     * @public
     */
    customMetrics: AutomatedEvaluationCustomMetricSource[] | undefined;
    /**
     * <p>Configuration of the evaluator model you want to use to evaluate custom metrics in an Amazon Bedrock evaluation job.</p>
     * @public
     */
    evaluatorModelConfig: CustomMetricEvaluatorModelConfig | undefined;
}
/**
 * <p>The location in Amazon S3 where your prompt dataset is stored.</p>
 * @public
 */
export type EvaluationDatasetLocation = EvaluationDatasetLocation.S3UriMember | EvaluationDatasetLocation.$UnknownMember;
/**
 * @public
 */
export declare namespace EvaluationDatasetLocation {
    /**
     * <p>The S3 URI of the S3 bucket specified in the job.</p>
     * @public
     */
    interface S3UriMember {
        s3Uri: string;
        $unknown?: never;
    }
    /**
     * @public
     */
    interface $UnknownMember {
        s3Uri?: never;
        $unknown: [string, any];
    }
    interface Visitor<T> {
        s3Uri: (value: string) => T;
        _: (name: string, value: any) => T;
    }
    const visit: <T>(value: EvaluationDatasetLocation, visitor: Visitor<T>) => T;
}
/**
 * <p>Used to specify the name of a built-in prompt dataset and optionally, the Amazon S3 bucket where a custom prompt dataset is saved.</p>
 * @public
 */
export interface EvaluationDataset {
    /**
     * <p>Used to specify supported built-in prompt datasets. Valid values are <code>Builtin.Bold</code>, <code>Builtin.BoolQ</code>, <code>Builtin.NaturalQuestions</code>, <code>Builtin.Gigaword</code>, <code>Builtin.RealToxicityPrompts</code>, <code>Builtin.TriviaQA</code>, <code>Builtin.T-Rex</code>, <code>Builtin.WomensEcommerceClothingReviews</code> and <code>Builtin.Wikitext2</code>.</p>
     * @public
     */
    name: string | undefined;
    /**
     * <p>For custom prompt datasets, you must specify the location in Amazon S3 where the prompt dataset is saved.</p>
     * @public
     */
    datasetLocation?: EvaluationDatasetLocation | undefined;
}
/**
 * @public
 * @enum
 */
export declare const EvaluationTaskType: {
    readonly CLASSIFICATION: "Classification";
    readonly CUSTOM: "Custom";
    readonly GENERATION: "Generation";
    readonly QUESTION_AND_ANSWER: "QuestionAndAnswer";
    readonly SUMMARIZATION: "Summarization";
};
/**
 * @public
 */
export type EvaluationTaskType = (typeof EvaluationTaskType)[keyof typeof EvaluationTaskType];
/**
 * <p>Defines the prompt datasets, built-in metric names and custom metric names, and the task type.</p>
 * @public
 */
export interface EvaluationDatasetMetricConfig {
    /**
     * <p>The the type of task you want to evaluate for your evaluation job. This applies only to model evaluation jobs and is ignored for knowledge base evaluation jobs.</p>
     * @public
     */
    taskType: EvaluationTaskType | undefined;
    /**
     * <p>Specifies the prompt dataset.</p>
     * @public
     */
    dataset: EvaluationDataset | undefined;
    /**
     * <p>The names of the metrics you want to use for your evaluation job.</p> <p>For knowledge base evaluation jobs that evaluate retrieval only, valid values are "<code>Builtin.ContextRelevance</code>", "<code>Builtin.ContextCoverage</code>".</p> <p>For knowledge base evaluation jobs that evaluate retrieval with response generation, valid values are "<code>Builtin.Correctness</code>", "<code>Builtin.Completeness</code>", "<code>Builtin.Helpfulness</code>", "<code>Builtin.LogicalCoherence</code>", "<code>Builtin.Faithfulness</code>", "<code>Builtin.Harmfulness</code>", "<code>Builtin.Stereotyping</code>", "<code>Builtin.Refusal</code>".</p> <p>For automated model evaluation jobs, valid values are "<code>Builtin.Accuracy</code>", "<code>Builtin.Robustness</code>", and "<code>Builtin.Toxicity</code>". In model evaluation jobs that use a LLM as judge you can specify "<code>Builtin.Correctness</code>", "<code>Builtin.Completeness"</code>, "<code>Builtin.Faithfulness"</code>, "<code>Builtin.Helpfulness</code>", "<code>Builtin.Coherence</code>", "<code>Builtin.Relevance</code>", "<code>Builtin.FollowingInstructions</code>", "<code>Builtin.ProfessionalStyleAndTone</code>", You can also specify the following responsible AI related metrics only for model evaluation job that use a LLM as judge "<code>Builtin.Harmfulness</code>", "<code>Builtin.Stereotyping</code>", and "<code>Builtin.Refusal</code>".</p> <p>For human-based model evaluation jobs, the list of strings must match the <code>name</code> parameter specified in <code>HumanEvaluationCustomMetric</code>.</p>
     * @public
     */
    metricNames: string[] | undefined;
}
/**
 * <p>The evaluator model used in knowledge base evaluation job or in model evaluation job that use a model as judge. This model computes all evaluation related metrics.</p>
 * @public
 */
export interface BedrockEvaluatorModel {
    /**
     * <p>The Amazon Resource Name (ARN) of the evaluator model used used in knowledge base evaluation job or in model evaluation job that use a model as judge.</p>
     * @public
     */
    modelIdentifier: string | undefined;
}
/**
 * <p>Specifies the model configuration for the evaluator model. <code>EvaluatorModelConfig</code> is required for evaluation jobs that use a knowledge base or in model evaluation job that use a model as judge. This model computes all evaluation related metrics.</p>
 * @public
 */
export type EvaluatorModelConfig = EvaluatorModelConfig.BedrockEvaluatorModelsMember | EvaluatorModelConfig.$UnknownMember;
/**
 * @public
 */
export declare namespace EvaluatorModelConfig {
    /**
     * <p>The evaluator model used in knowledge base evaluation job or in model evaluation job that use a model as judge. This model computes all evaluation related metrics.</p>
     * @public
     */
    interface BedrockEvaluatorModelsMember {
        bedrockEvaluatorModels: BedrockEvaluatorModel[];
        $unknown?: never;
    }
    /**
     * @public
     */
    interface $UnknownMember {
        bedrockEvaluatorModels?: never;
        $unknown: [string, any];
    }
    interface Visitor<T> {
        bedrockEvaluatorModels: (value: BedrockEvaluatorModel[]) => T;
        _: (name: string, value: any) => T;
    }
    const visit: <T>(value: EvaluatorModelConfig, visitor: Visitor<T>) => T;
}
/**
 * <p>The configuration details of an automated evaluation job. The <code>EvaluationDatasetMetricConfig</code> object is used to specify the prompt datasets, task type, and metric names.</p>
 * @public
 */
export interface AutomatedEvaluationConfig {
    /**
     * <p>Configuration details of the prompt datasets and metrics you want to use for your evaluation job.</p>
     * @public
     */
    datasetMetricConfigs: EvaluationDatasetMetricConfig[] | undefined;
    /**
     * <p>Contains the evaluator model configuration details. <code>EvaluatorModelConfig</code> is required for evaluation jobs that use a knowledge base or in model evaluation job that use a model as judge. This model computes all evaluation related metrics.</p>
     * @public
     */
    evaluatorModelConfig?: EvaluatorModelConfig | undefined;
    /**
     * <p>Defines the configuration of custom metrics to be used in an evaluation job.</p>
     * @public
     */
    customMetricConfig?: AutomatedEvaluationCustomMetricConfig | undefined;
}
/**
 * <p>In a model evaluation job that uses human workers you must define the name of the metric, and how you want that metric rated <code>ratingMethod</code>, and an optional description of the metric.</p>
 * @public
 */
export interface HumanEvaluationCustomMetric {
    /**
     * <p>The name of the metric. Your human evaluators will see this name in the evaluation UI.</p>
     * @public
     */
    name: string | undefined;
    /**
     * <p>An optional description of the metric. Use this parameter to provide more details about the metric.</p>
     * @public
     */
    description?: string | undefined;
    /**
     * <p>Choose how you want your human workers to evaluation your model. Valid values for rating methods are <code>ThumbsUpDown</code>, <code>IndividualLikertScale</code>,<code>ComparisonLikertScale</code>, <code>ComparisonChoice</code>, and <code>ComparisonRank</code> </p>
     * @public
     */
    ratingMethod: string | undefined;
}
/**
 * <p>Contains <code>SageMakerFlowDefinition</code> object. The object is used to specify the prompt dataset, task type, rating method and metric names.</p>
 * @public
 */
export interface HumanWorkflowConfig {
    /**
     * <p>The Amazon Resource Number (ARN) for the flow definition</p>
     * @public
     */
    flowDefinitionArn: string | undefined;
    /**
     * <p>Instructions for the flow definition</p>
     * @public
     */
    instructions?: string | undefined;
}
/**
 * <p>Specifies the custom metrics, how tasks will be rated, the flow definition ARN, and your custom prompt datasets. Model evaluation jobs use human workers <i>only</i> support the use of custom prompt datasets. To learn more about custom prompt datasets and the required format, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/model-evaluation-prompt-datasets-custom.html">Custom prompt datasets</a>.</p> <p>When you create custom metrics in <code>HumanEvaluationCustomMetric</code> you must specify the metric's <code>name</code>. The list of <code>names</code> specified in the <code>HumanEvaluationCustomMetric</code> array, must match the <code>metricNames</code> array of strings specified in <code>EvaluationDatasetMetricConfig</code>. For example, if in the <code>HumanEvaluationCustomMetric</code> array your specified the names <code>"accuracy", "toxicity", "readability"</code> as custom metrics <i>then</i> the <code>metricNames</code> array would need to look like the following <code>["accuracy", "toxicity", "readability"]</code> in <code>EvaluationDatasetMetricConfig</code>.</p>
 * @public
 */
export interface HumanEvaluationConfig {
    /**
     * <p>The parameters of the human workflow.</p>
     * @public
     */
    humanWorkflowConfig?: HumanWorkflowConfig | undefined;
    /**
     * <p>A <code>HumanEvaluationCustomMetric</code> object. It contains the names the metrics, how the metrics are to be evaluated, an optional description.</p>
     * @public
     */
    customMetrics?: HumanEvaluationCustomMetric[] | undefined;
    /**
     * <p>Use to specify the metrics, task, and prompt dataset to be used in your model evaluation job.</p>
     * @public
     */
    datasetMetricConfigs: EvaluationDatasetMetricConfig[] | undefined;
}
/**
 * <p>The configuration details of either an automated or human-based evaluation job.</p>
 * @public
 */
export type EvaluationConfig = EvaluationConfig.AutomatedMember | EvaluationConfig.HumanMember | EvaluationConfig.$UnknownMember;
/**
 * @public
 */
export declare namespace EvaluationConfig {
    /**
     * <p>Contains the configuration details of an automated evaluation job that computes metrics.</p>
     * @public
     */
    interface AutomatedMember {
        automated: AutomatedEvaluationConfig;
        human?: never;
        $unknown?: never;
    }
    /**
     * <p>Contains the configuration details of an evaluation job that uses human workers.</p>
     * @public
     */
    interface HumanMember {
        automated?: never;
        human: HumanEvaluationConfig;
        $unknown?: never;
    }
    /**
     * @public
     */
    interface $UnknownMember {
        automated?: never;
        human?: never;
        $unknown: [string, any];
    }
    interface Visitor<T> {
        automated: (value: AutomatedEvaluationConfig) => T;
        human: (value: HumanEvaluationConfig) => T;
        _: (name: string, value: any) => T;
    }
    const visit: <T>(value: EvaluationConfig, visitor: Visitor<T>) => T;
}
/**
 * @public
 * @enum
 */
export declare const PerformanceConfigLatency: {
    readonly OPTIMIZED: "optimized";
    readonly STANDARD: "standard";
};
/**
 * @public
 */
export type PerformanceConfigLatency = (typeof PerformanceConfigLatency)[keyof typeof PerformanceConfigLatency];
/**
 * <p>Contains performance settings for a model.</p>
 * @public
 */
export interface PerformanceConfiguration {
    /**
     * <p>Specifies whether to use the latency-optimized or standard version of a model or inference profile.</p>
     * @public
     */
    latency?: PerformanceConfigLatency | undefined;
}
/**
 * <p>Contains the ARN of the Amazon Bedrock model or <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/cross-region-inference.html">inference profile</a> specified in your evaluation job. Each Amazon Bedrock model supports different <code>inferenceParams</code>. To learn more about supported inference parameters for Amazon Bedrock models, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters.html">Inference parameters for foundation models</a>.</p> <p>The <code>inferenceParams</code> are specified using JSON. To successfully insert JSON as string make sure that all quotations are properly escaped. For example, <code>"temperature":"0.25"</code> key value pair would need to be formatted as <code>\"temperature\":\"0.25\"</code> to successfully accepted in the request.</p>
 * @public
 */
export interface EvaluationBedrockModel {
    /**
     * <p>The ARN of the Amazon Bedrock model or inference profile specified.</p>
     * @public
     */
    modelIdentifier: string | undefined;
    /**
     * <p>Each Amazon Bedrock support different inference parameters that change how the model behaves during inference.</p>
     * @public
     */
    inferenceParams?: string | undefined;
    /**
     * <p>Specifies performance settings for the model or inference profile.</p>
     * @public
     */
    performanceConfig?: PerformanceConfiguration | undefined;
}
/**
 * <p>A summary of a model used for a model evaluation job where you provide your own inference response data.</p>
 * @public
 */
export interface EvaluationPrecomputedInferenceSource {
    /**
     * <p>A label that identifies a model used in a model evaluation job where you provide your own inference response data.</p>
     * @public
     */
    inferenceSourceIdentifier: string | undefined;
}
/**
 * <p>Defines the models used in the model evaluation job.</p>
 * @public
 */
export type EvaluationModelConfig = EvaluationModelConfig.BedrockModelMember | EvaluationModelConfig.PrecomputedInferenceSourceMember | EvaluationModelConfig.$UnknownMember;
/**
 * @public
 */
export declare namespace EvaluationModelConfig {
    /**
     * <p>Defines the Amazon Bedrock model or inference profile and inference parameters you want used.</p>
     * @public
     */
    interface BedrockModelMember {
        bedrockModel: EvaluationBedrockModel;
        precomputedInferenceSource?: never;
        $unknown?: never;
    }
    /**
     * <p>Defines the model used to generate inference response data for a model evaluation job where you provide your own inference response data.</p>
     * @public
     */
    interface PrecomputedInferenceSourceMember {
        bedrockModel?: never;
        precomputedInferenceSource: EvaluationPrecomputedInferenceSource;
        $unknown?: never;
    }
    /**
     * @public
     */
    interface $UnknownMember {
        bedrockModel?: never;
        precomputedInferenceSource?: never;
        $unknown: [string, any];
    }
    interface Visitor<T> {
        bedrockModel: (value: EvaluationBedrockModel) => T;
        precomputedInferenceSource: (value: EvaluationPrecomputedInferenceSource) => T;
        _: (name: string, value: any) => T;
    }
    const visit: <T>(value: EvaluationModelConfig, visitor: Visitor<T>) => T;
}
/**
 * <p>The configuration details for the guardrail.</p>
 * @public
 */
export interface GuardrailConfiguration {
    /**
     * <p>The unique identifier for the guardrail.</p>
     * @public
     */
    guardrailId: string | undefined;
    /**
     * <p>The version of the guardrail.</p>
     * @public
     */
    guardrailVersion: string | undefined;
}
/**
 * <p>The configuration details for text generation using a language model via the <code>RetrieveAndGenerate</code> function.</p>
 * @public
 */
export interface TextInferenceConfig {
    /**
     * <p>Controls the random-ness of text generated by the language model, influencing how much the model sticks to the most predictable next words versus exploring more surprising options. A lower temperature value (e.g. 0.2 or 0.3) makes model outputs more deterministic or predictable, while a higher temperature (e.g. 0.8 or 0.9) makes the outputs more creative or unpredictable.</p>
     * @public
     */
    temperature?: number | undefined;
    /**
     * <p>A probability distribution threshold which controls what the model considers for the set of possible next tokens. The model will only consider the top p% of the probability distribution when generating the next token.</p>
     * @public
     */
    topP?: number | undefined;
    /**
     * <p>The maximum number of tokens to generate in the output text. Do not use the minimum of 0 or the maximum of 65536. The limit values described here are arbitrary values, for actual values consult the limits defined by your specific model.</p>
     * @public
     */
    maxTokens?: number | undefined;
    /**
     * <p>A list of sequences of characters that, if generated, will cause the model to stop generating further tokens. Do not use a minimum length of 1 or a maximum length of 1000. The limit values described here are arbitrary values, for actual values consult the limits defined by your specific model.</p>
     * @public
     */
    stopSequences?: string[] | undefined;
}
/**
 * <p>Contains configuration details of the inference for knowledge base retrieval and response generation.</p>
 * @public
 */
export interface KbInferenceConfig {
    /**
     * <p>Contains configuration details for text generation using a language model via the <code>RetrieveAndGenerate</code> function.</p>
     * @public
     */
    textInferenceConfig?: TextInferenceConfig | undefined;
}
/**
 * <p>The template for the prompt that's sent to the model for response generation.</p>
 * @public
 */
export interface PromptTemplate {
    /**
     * <p>The template for the prompt that's sent to the model for response generation. You can include prompt placeholders, which become replaced before the prompt is sent to the model to provide instructions and context to the model. In addition, you can include XML tags to delineate meaningful sections of the prompt template.</p> <p>For more information, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/kb-test-config.html">Knowledge base prompt template</a> and <a href="https://docs.anthropic.com/en/docs/build-with-claude/prompt-engineering/use-xml-tags">Use XML tags with Anthropic Claude models</a>.</p>
     * @public
     */
    textPromptTemplate?: string | undefined;
}
/**
 * <p>The response generation configuration of the external source wrapper object.</p>
 * @public
 */
export interface ExternalSourcesGenerationConfiguration {
    /**
     * <p>Contains the template for the prompt for the external source wrapper object.</p>
     * @public
     */
    promptTemplate?: PromptTemplate | undefined;
    /**
     * <p>Configuration details for the guardrail.</p>
     * @public
     */
    guardrailConfiguration?: GuardrailConfiguration | undefined;
    /**
     * <p>Configuration details for inference when using <code>RetrieveAndGenerate</code> to generate responses while using an external source.</p>
     * @public
     */
    kbInferenceConfig?: KbInferenceConfig | undefined;
    /**
     * <p>Additional model parameters and their corresponding values not included in the text inference configuration for an external source. Takes in custom model parameters specific to the language model being used.</p>
     * @public
     */
    additionalModelRequestFields?: Record<string, __DocumentType> | undefined;
}
/**
 * <p>Contains the document contained in the wrapper object, along with its attributes/fields.</p>
 * @public
 */
export interface ByteContentDoc {
    /**
     * <p>The file name of the document contained in the wrapper object.</p>
     * @public
     */
    identifier: string | undefined;
    /**
     * <p>The MIME type of the document contained in the wrapper object.</p>
     * @public
     */
    contentType: string | undefined;
    /**
     * <p>The byte value of the file to upload, encoded as a Base-64 string.</p>
     * @public
     */
    data: Uint8Array | undefined;
}
/**
 * <p>The unique wrapper object of the document from the S3 location.</p>
 * @public
 */
export interface S3ObjectDoc {
    /**
     * <p>The S3 URI location for the wrapper object of the document.</p>
     * @public
     */
    uri: string | undefined;
}
/**
 * @public
 * @enum
 */
export declare const ExternalSourceType: {
    readonly BYTE_CONTENT: "BYTE_CONTENT";
    readonly S3: "S3";
};
/**
 * @public
 */
export type ExternalSourceType = (typeof ExternalSourceType)[keyof typeof ExternalSourceType];
/**
 * <p>The unique external source of the content contained in the wrapper object.</p>
 * @public
 */
export interface ExternalSource {
    /**
     * <p>The source type of the external source wrapper object.</p>
     * @public
     */
    sourceType: ExternalSourceType | undefined;
    /**
     * <p>The S3 location of the external source wrapper object.</p>
     * @public
     */
    s3Location?: S3ObjectDoc | undefined;
    /**
     * <p>The identifier, content type, and data of the external source wrapper object.</p>
     * @public
     */
    byteContent?: ByteContentDoc | undefined;
}
/**
 * <p>The configuration of the external source wrapper object in the <code>retrieveAndGenerate</code> function.</p>
 * @public
 */
export interface ExternalSourcesRetrieveAndGenerateConfiguration {
    /**
     * <p>The Amazon Resource Name (ARN) of the foundation model or <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/cross-region-inference.html">inference profile</a> used to generate responses. </p>
     * @public
     */
    modelArn: string | undefined;
    /**
     * <p>The document for the external source wrapper object in the <code>retrieveAndGenerate</code> function.</p>
     * @public
     */
    sources: ExternalSource[] | undefined;
    /**
     * <p>Contains configurations details for response generation based on retrieved text chunks.</p>
     * @public
     */
    generationConfiguration?: ExternalSourcesGenerationConfiguration | undefined;
}
/**
 * <p>The configuration details for response generation based on retrieved text chunks.</p>
 * @public
 */
export interface GenerationConfiguration {
    /**
     * <p>Contains the template for the prompt that's sent to the model for response generation.</p>
     * @public
     */
    promptTemplate?: PromptTemplate | undefined;
    /**
     * <p>Contains configuration details for the guardrail.</p>
     * @public
     */
    guardrailConfiguration?: GuardrailConfiguration | undefined;
    /**
     * <p>Contains configuration details for inference for knowledge base retrieval and response generation.</p>
     * @public
     */
    kbInferenceConfig?: KbInferenceConfig | undefined;
    /**
     * <p>Additional model parameters and corresponding values not included in the <code>textInferenceConfig</code> structure for a knowledge base. This allows you to provide custom model parameters specific to the language model being used.</p>
     * @public
     */
    additionalModelRequestFields?: Record<string, __DocumentType> | undefined;
}
/**
 * @public
 * @enum
 */
export declare const QueryTransformationType: {
    readonly QUERY_DECOMPOSITION: "QUERY_DECOMPOSITION";
};
/**
 * @public
 */
export type QueryTransformationType = (typeof QueryTransformationType)[keyof typeof QueryTransformationType];
/**
 * <p>The configuration details for transforming the prompt.</p>
 * @public
 */
export interface QueryTransformationConfiguration {
    /**
     * <p>The type of transformation to apply to the prompt.</p>
     * @public
     */
    type: QueryTransformationType | undefined;
}
/**
 * <p>The configuration details for the model to process the prompt prior to retrieval and response generation.</p>
 * @public
 */
export interface OrchestrationConfiguration {
    /**
     * <p>Contains configuration details for transforming the prompt.</p>
     * @public
     */
    queryTransformationConfiguration: QueryTransformationConfiguration | undefined;
}
/**
 * <p>Specifies the name of the metadata attribute/field to apply filters. You must match the name of the attribute/field in your data source/document metadata.</p>
 * @public
 */
export interface FilterAttribute {
    /**
     * <p>The name of metadata attribute/field, which must match the name in your data source/document metadata.</p>
     * @public
     */
    key: string | undefined;
    /**
     * <p>The value of the metadata attribute/field.</p>
     * @public
     */
    value: __DocumentType | undefined;
}
/**
 * @public
 * @enum
 */
export declare const AttributeType: {
    readonly BOOLEAN: "BOOLEAN";
    readonly NUMBER: "NUMBER";
    readonly STRING: "STRING";
    readonly STRING_LIST: "STRING_LIST";
};
/**
 * @public
 */
export type AttributeType = (typeof AttributeType)[keyof typeof AttributeType];
/**
 * <p>Defines the schema for a metadata attribute used in Knowledge Base vector searches. Metadata attributes provide additional context for documents and can be used for filtering and reranking search results.</p>
 * @public
 */
export interface MetadataAttributeSchema {
    /**
     * <p>The unique identifier for the metadata attribute. This key is used to reference the attribute in filter expressions and reranking configurations.</p>
     * @public
     */
    key: string | undefined;
    /**
     * <p>The data type of the metadata attribute. The type determines how the attribute can be used in filter expressions and reranking.</p>
     * @public
     */
    type: AttributeType | undefined;
    /**
     * <p>An optional description of the metadata attribute that provides additional context about its purpose and usage.</p>
     * @public
     */
    description: string | undefined;
}
/**
 * <p>Configuration for implicit filtering in Knowledge Base vector searches. Implicit filtering allows you to automatically filter search results based on metadata attributes without requiring explicit filter expressions in each query.</p>
 * @public
 */
export interface ImplicitFilterConfiguration {
    /**
     * <p>A list of metadata attribute schemas that define the structure and properties of metadata fields used for implicit filtering. Each attribute defines a key, type, and optional description.</p>
     * @public
     */
    metadataAttributes: MetadataAttributeSchema[] | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the foundation model used for implicit filtering. This model processes the query to extract relevant filtering criteria.</p>
     * @public
     */
    modelArn: string | undefined;
}
/**
 * @public
 * @enum
 */
export declare const SearchType: {
    readonly HYBRID: "HYBRID";
    readonly SEMANTIC: "SEMANTIC";
};
/**
 * @public
 */
export type SearchType = (typeof SearchType)[keyof typeof SearchType];
/**
 * @public
 * @enum
 */
export declare const RerankingMetadataSelectionMode: {
    readonly ALL: "ALL";
    readonly SELECTIVE: "SELECTIVE";
};
/**
 * @public
 */
export type RerankingMetadataSelectionMode = (typeof RerankingMetadataSelectionMode)[keyof typeof RerankingMetadataSelectionMode];
/**
 * <p>Specifies a field to be used during the reranking process in a Knowledge Base vector search. This structure identifies metadata fields that should be considered when reordering search results to improve relevance.</p>
 * @public
 */
export interface FieldForReranking {
    /**
     * <p>The name of the metadata field to be used during the reranking process.</p>
     * @public
     */
    fieldName: string | undefined;
}
/**
 * <p>Configuration for selectively including or excluding metadata fields during the reranking process. This allows you to control which metadata attributes are considered when reordering search results.</p>
 * @public
 */
export type RerankingMetadataSelectiveModeConfiguration = RerankingMetadataSelectiveModeConfiguration.FieldsToExcludeMember | RerankingMetadataSelectiveModeConfiguration.FieldsToIncludeMember | RerankingMetadataSelectiveModeConfiguration.$UnknownMember;
/**
 * @public
 */
export declare namespace RerankingMetadataSelectiveModeConfiguration {
    /**
     * <p>A list of metadata field names to explicitly include in the reranking process. Only these fields will be considered when reordering search results. This parameter cannot be used together with fieldsToExclude.</p>
     * @public
     */
    interface FieldsToIncludeMember {
        fieldsToInclude: FieldForReranking[];
        fieldsToExclude?: never;
        $unknown?: never;
    }
    /**
     * <p>A list of metadata field names to explicitly exclude from the reranking process. All metadata fields except these will be considered when reordering search results. This parameter cannot be used together with fieldsToInclude.</p>
     * @public
     */
    interface FieldsToExcludeMember {
        fieldsToInclude?: never;
        fieldsToExclude: FieldForReranking[];
        $unknown?: never;
    }
    /**
     * @public
     */
    interface $UnknownMember {
        fieldsToInclude?: never;
        fieldsToExclude?: never;
        $unknown: [string, any];
    }
    interface Visitor<T> {
        fieldsToInclude: (value: FieldForReranking[]) => T;
        fieldsToExclude: (value: FieldForReranking[]) => T;
        _: (name: string, value: any) => T;
    }
    const visit: <T>(value: RerankingMetadataSelectiveModeConfiguration, visitor: Visitor<T>) => T;
}
/**
 * <p>Configuration for how metadata should be used during the reranking process in Knowledge Base vector searches. This determines which metadata fields are included or excluded when reordering search results.</p>
 * @public
 */
export interface MetadataConfigurationForReranking {
    /**
     * <p>The mode for selecting which metadata fields to include in the reranking process. Valid values are ALL (use all available metadata fields) or SELECTIVE (use only specified fields).</p>
     * @public
     */
    selectionMode: RerankingMetadataSelectionMode | undefined;
    /**
     * <p>Configuration for selective mode, which allows you to explicitly include or exclude specific metadata fields during reranking. This is only used when selectionMode is set to SELECTIVE.</p>
     * @public
     */
    selectiveModeConfiguration?: RerankingMetadataSelectiveModeConfiguration | undefined;
}
/**
 * <p>Configuration for the Amazon Bedrock foundation model used for reranking vector search results. This specifies which model to use and any additional parameters required by the model.</p>
 * @public
 */
export interface VectorSearchBedrockRerankingModelConfiguration {
    /**
     * <p>The Amazon Resource Name (ARN) of the foundation model to use for reranking. This model processes the query and search results to determine a more relevant ordering.</p>
     * @public
     */
    modelArn: string | undefined;
    /**
     * <p>A list of additional fields to include in the model request during reranking. These fields provide extra context or configuration options specific to the selected foundation model.</p>
     * @public
     */
    additionalModelRequestFields?: Record<string, __DocumentType> | undefined;
}
/**
 * <p>Configuration for using Amazon Bedrock foundation models to rerank Knowledge Base vector search results. This enables more sophisticated relevance ranking using large language models.</p>
 * @public
 */
export interface VectorSearchBedrockRerankingConfiguration {
    /**
     * <p>Configuration for the Amazon Bedrock foundation model used for reranking. This includes the model ARN and any additional request fields required by the model.</p>
     * @public
     */
    modelConfiguration: VectorSearchBedrockRerankingModelConfiguration | undefined;
    /**
     * <p>The maximum number of results to rerank. This limits how many of the initial vector search results will be processed by the reranking model. A smaller number improves performance but may exclude potentially relevant results.</p>
     * @public
     */
    numberOfRerankedResults?: number | undefined;
    /**
     * <p>Configuration for how document metadata should be used during the reranking process. This determines which metadata fields are included when reordering search results.</p>
     * @public
     */
    metadataConfiguration?: MetadataConfigurationForReranking | undefined;
}
/**
 * @public
 * @enum
 */
export declare const VectorSearchRerankingConfigurationType: {
    readonly BEDROCK_RERANKING_MODEL: "BEDROCK_RERANKING_MODEL";
};
/**
 * @public
 */
export type VectorSearchRerankingConfigurationType = (typeof VectorSearchRerankingConfigurationType)[keyof typeof VectorSearchRerankingConfigurationType];
/**
 * <p>Configuration for reranking vector search results to improve relevance. Reranking applies additional relevance models to reorder the initial vector search results based on more sophisticated criteria.</p>
 * @public
 */
export interface VectorSearchRerankingConfiguration {
    /**
     * <p>The type of reranking to apply to vector search results. Currently, the only supported value is BEDROCK, which uses Amazon Bedrock foundation models for reranking.</p>
     * @public
     */
    type: VectorSearchRerankingConfigurationType | undefined;
    /**
     * <p>Configuration for using Amazon Bedrock foundation models to rerank search results. This is required when the reranking type is set to BEDROCK.</p>
     * @public
     */
    bedrockRerankingConfiguration?: VectorSearchBedrockRerankingConfiguration | undefined;
}
/**
 * @public
 * @enum
 */
export declare const RetrieveAndGenerateType: {
    readonly EXTERNAL_SOURCES: "EXTERNAL_SOURCES";
    readonly KNOWLEDGE_BASE: "KNOWLEDGE_BASE";
};
/**
 * @public
 */
export type RetrieveAndGenerateType = (typeof RetrieveAndGenerateType)[keyof typeof RetrieveAndGenerateType];
/**
 * <p>A summary of a RAG source used for a retrieve-and-generate Knowledge Base evaluation job where you provide your own inference response data.</p>
 * @public
 */
export interface EvaluationPrecomputedRetrieveAndGenerateSourceConfig {
    /**
     * <p>A label that identifies the RAG source used for a retrieve-and-generate Knowledge Base evaluation job where you provide your own inference response data.</p>
     * @public
     */
    ragSourceIdentifier: string | undefined;
}
/**
 * <p>A summary of a RAG source used for a retrieve-only Knowledge Base evaluation job where you provide your own inference response data.</p>
 * @public
 */
export interface EvaluationPrecomputedRetrieveSourceConfig {
    /**
     * <p>A label that identifies the RAG source used for a retrieve-only Knowledge Base evaluation job where you provide your own inference response data.</p>
     * @public
     */
    ragSourceIdentifier: string | undefined;
}
/**
 * <p>A summary of a RAG source used for a Knowledge Base evaluation job where you provide your own inference response data.</p>
 * @public
 */
export type EvaluationPrecomputedRagSourceConfig = EvaluationPrecomputedRagSourceConfig.RetrieveAndGenerateSourceConfigMember | EvaluationPrecomputedRagSourceConfig.RetrieveSourceConfigMember | EvaluationPrecomputedRagSourceConfig.$UnknownMember;
/**
 * @public
 */
export declare namespace EvaluationPrecomputedRagSourceConfig {
    /**
     * <p>A summary of a RAG source used for a retrieve-only Knowledge Base evaluation job where you provide your own inference response data.</p>
     * @public
     */
    interface RetrieveSourceConfigMember {
        retrieveSourceConfig: EvaluationPrecomputedRetrieveSourceConfig;
        retrieveAndGenerateSourceConfig?: never;
        $unknown?: never;
    }
    /**
     * <p>A summary of a RAG source used for a retrieve-and-generate Knowledge Base evaluation job where you provide your own inference response data.</p>
     * @public
     */
    interface RetrieveAndGenerateSourceConfigMember {
        retrieveSourceConfig?: never;
        retrieveAndGenerateSourceConfig: EvaluationPrecomputedRetrieveAndGenerateSourceConfig;
        $unknown?: never;
    }
    /**
     * @public
     */
    interface $UnknownMember {
        retrieveSourceConfig?: never;
        retrieveAndGenerateSourceConfig?: never;
        $unknown: [string, any];
    }
    interface Visitor<T> {
        retrieveSourceConfig: (value: EvaluationPrecomputedRetrieveSourceConfig) => T;
        retrieveAndGenerateSourceConfig: (value: EvaluationPrecomputedRetrieveAndGenerateSourceConfig) => T;
        _: (name: string, value: any) => T;
    }
    const visit: <T>(value: EvaluationPrecomputedRagSourceConfig, visitor: Visitor<T>) => T;
}
/**
 * <p>The Amazon S3 location where the results of your evaluation job are saved.</p>
 * @public
 */
export interface EvaluationOutputDataConfig {
    /**
     * <p>The Amazon S3 URI where the results of the evaluation job are saved.</p>
     * @public
     */
    s3Uri: string | undefined;
}
/**
 * @public
 */
export interface CreateEvaluationJobResponse {
    /**
     * <p>The Amazon Resource Name (ARN) of the evaluation job.</p>
     * @public
     */
    jobArn: string | undefined;
}
/**
 * @public
 */
export interface GetEvaluationJobRequest {
    /**
     * <p>The Amazon Resource Name (ARN) of the evaluation job you want get information on.</p>
     * @public
     */
    jobIdentifier: string | undefined;
}
/**
 * @public
 * @enum
 */
export declare const EvaluationJobType: {
    readonly AUTOMATED: "Automated";
    readonly HUMAN: "Human";
};
/**
 * @public
 */
export type EvaluationJobType = (typeof EvaluationJobType)[keyof typeof EvaluationJobType];
/**
 * @public
 * @enum
 */
export declare const SortJobsBy: {
    readonly CREATION_TIME: "CreationTime";
};
/**
 * @public
 */
export type SortJobsBy = (typeof SortJobsBy)[keyof typeof SortJobsBy];
/**
 * @public
 */
export interface ListEvaluationJobsRequest {
    /**
     * <p>A filter to only list evaluation jobs created after a specified time.</p>
     * @public
     */
    creationTimeAfter?: Date | undefined;
    /**
     * <p>A filter to only list evaluation jobs created before a specified time.</p>
     * @public
     */
    creationTimeBefore?: Date | undefined;
    /**
     * <p>A filter to only list evaluation jobs that are of a certain status.</p>
     * @public
     */
    statusEquals?: EvaluationJobStatus | undefined;
    /**
     * <p>A filter to only list evaluation jobs that are either model evaluations or knowledge base evaluations.</p>
     * @public
     */
    applicationTypeEquals?: ApplicationType | undefined;
    /**
     * <p>A filter to only list evaluation jobs that contain a specified string in the job name.</p>
     * @public
     */
    nameContains?: string | undefined;
    /**
     * <p>The maximum number of results to return.</p>
     * @public
     */
    maxResults?: number | undefined;
    /**
     * <p>Continuation token from the previous response, for Amazon Bedrock to list the next set of results.</p>
     * @public
     */
    nextToken?: string | undefined;
    /**
     * <p>Specifies a creation time to sort the list of evaluation jobs by when they were created.</p>
     * @public
     */
    sortBy?: SortJobsBy | undefined;
    /**
     * <p>Specifies whether to sort the list of evaluation jobs by either ascending or descending order.</p>
     * @public
     */
    sortOrder?: SortOrder | undefined;
}
/**
 * <p>A summary of the models used in an Amazon Bedrock model evaluation job. These resources can be models in Amazon Bedrock or models outside of Amazon Bedrock that you use to generate your own inference response data.</p>
 * @public
 */
export interface EvaluationModelConfigSummary {
    /**
     * <p>The Amazon Resource Names (ARNs) of the models used for the evaluation job.</p>
     * @public
     */
    bedrockModelIdentifiers?: string[] | undefined;
    /**
     * <p>A label that identifies the models used for a model evaluation job where you provide your own inference response data.</p>
     * @public
     */
    precomputedInferenceSourceIdentifiers?: string[] | undefined;
}
/**
 * <p>A summary of the RAG resources used in an Amazon Bedrock Knowledge Base evaluation job. These resources can be Knowledge Bases in Amazon Bedrock or RAG sources outside of Amazon Bedrock that you use to generate your own inference response data.</p>
 * @public
 */
export interface EvaluationRagConfigSummary {
    /**
     * <p>The Amazon Resource Names (ARNs) of the Knowledge Base resources used for a Knowledge Base evaluation job where Amazon Bedrock invokes the Knowledge Base for you.</p>
     * @public
     */
    bedrockKnowledgeBaseIdentifiers?: string[] | undefined;
    /**
     * <p>A label that identifies the RAG sources used for a Knowledge Base evaluation job where you provide your own inference response data.</p>
     * @public
     */
    precomputedRagSourceIdentifiers?: string[] | undefined;
}
/**
 * <p>Identifies the models, Knowledge Bases, or other RAG sources evaluated in a model or Knowledge Base evaluation job.</p>
 * @public
 */
export interface EvaluationInferenceConfigSummary {
    /**
     * <p>A summary of the models used in an Amazon Bedrock model evaluation job. These resources can be models in Amazon Bedrock or models outside of Amazon Bedrock that you use to generate your own inference response data.</p>
     * @public
     */
    modelConfigSummary?: EvaluationModelConfigSummary | undefined;
    /**
     * <p>A summary of the RAG resources used in an Amazon Bedrock Knowledge Base evaluation job. These resources can be Knowledge Bases in Amazon Bedrock or RAG sources outside of Amazon Bedrock that you use to generate your own inference response data.</p>
     * @public
     */
    ragConfigSummary?: EvaluationRagConfigSummary | undefined;
}
/**
 * <p>Summary information of an evaluation job.</p>
 * @public
 */
export interface EvaluationSummary {
    /**
     * <p>The Amazon Resource Name (ARN) of the evaluation job.</p>
     * @public
     */
    jobArn: string | undefined;
    /**
     * <p>The name for the evaluation job.</p>
     * @public
     */
    jobName: string | undefined;
    /**
     * <p>The current status of the evaluation job.</p>
     * @public
     */
    status: EvaluationJobStatus | undefined;
    /**
     * <p>The time the evaluation job was created.</p>
     * @public
     */
    creationTime: Date | undefined;
    /**
     * <p>Specifies whether the evaluation job is automated or human-based.</p>
     * @public
     */
    jobType: EvaluationJobType | undefined;
    /**
     * <p>The type of task for model evaluation.</p>
     * @public
     */
    evaluationTaskTypes: EvaluationTaskType[] | undefined;
    /**
     * <p>The Amazon Resource Names (ARNs) of the model(s) used for the evaluation job.</p>
     *
     * @deprecated
     * @public
     */
    modelIdentifiers?: string[] | undefined;
    /**
     * <p>The Amazon Resource Names (ARNs) of the knowledge base resources used for a knowledge base evaluation job.</p>
     *
     * @deprecated
     * @public
     */
    ragIdentifiers?: string[] | undefined;
    /**
     * <p>The Amazon Resource Names (ARNs) of the models used to compute the metrics for a knowledge base evaluation job.</p>
     * @public
     */
    evaluatorModelIdentifiers?: string[] | undefined;
    /**
     * <p>The Amazon Resource Names (ARNs) of the models used to compute custom metrics in an Amazon Bedrock evaluation job.</p>
     * @public
     */
    customMetricsEvaluatorModelIdentifiers?: string[] | undefined;
    /**
     * <p>Identifies the models, Knowledge Bases, or other RAG sources evaluated in a model or Knowledge Base evaluation job.</p>
     * @public
     */
    inferenceConfigSummary?: EvaluationInferenceConfigSummary | undefined;
    /**
     * <p>Specifies whether the evaluation job is for evaluating a model or evaluating a knowledge base (retrieval and response generation).</p>
     * @public
     */
    applicationType?: ApplicationType | undefined;
}
/**
 * @public
 */
export interface ListEvaluationJobsResponse {
    /**
     * <p>Continuation token from the previous response, for Amazon Bedrock to list the next set of results.</p>
     * @public
     */
    nextToken?: string | undefined;
    /**
     * <p>A list of summaries of the evaluation jobs.</p>
     * @public
     */
    jobSummaries?: EvaluationSummary[] | undefined;
}
/**
 * @public
 */
export interface StopEvaluationJobRequest {
    /**
     * <p>The Amazon Resource Name (ARN) of the evaluation job you want to stop.</p>
     * @public
     */
    jobIdentifier: string | undefined;
}
/**
 * @public
 */
export interface StopEvaluationJobResponse {
}
/**
 * @public
 * @enum
 */
export declare const GuardrailContentFilterAction: {
    readonly BLOCK: "BLOCK";
    readonly NONE: "NONE";
};
/**
 * @public
 */
export type GuardrailContentFilterAction = (typeof GuardrailContentFilterAction)[keyof typeof GuardrailContentFilterAction];
/**
 * @public
 * @enum
 */
export declare const GuardrailModality: {
    readonly IMAGE: "IMAGE";
    readonly TEXT: "TEXT";
};
/**
 * @public
 */
export type GuardrailModality = (typeof GuardrailModality)[keyof typeof GuardrailModality];
/**
 * @public
 * @enum
 */
export declare const GuardrailFilterStrength: {
    readonly HIGH: "HIGH";
    readonly LOW: "LOW";
    readonly MEDIUM: "MEDIUM";
    readonly NONE: "NONE";
};
/**
 * @public
 */
export type GuardrailFilterStrength = (typeof GuardrailFilterStrength)[keyof typeof GuardrailFilterStrength];
/**
 * @public
 * @enum
 */
export declare const GuardrailContentFilterType: {
    readonly HATE: "HATE";
    readonly INSULTS: "INSULTS";
    readonly MISCONDUCT: "MISCONDUCT";
    readonly PROMPT_ATTACK: "PROMPT_ATTACK";
    readonly SEXUAL: "SEXUAL";
    readonly VIOLENCE: "VIOLENCE";
};
/**
 * @public
 */
export type GuardrailContentFilterType = (typeof GuardrailContentFilterType)[keyof typeof GuardrailContentFilterType];
/**
 * <p>Contains filter strengths for harmful content. Guardrails support the following content filters to detect and filter harmful user inputs and FM-generated outputs.</p> <ul> <li> <p> <b>Hate</b> – Describes language or a statement that discriminates, criticizes, insults, denounces, or dehumanizes a person or group on the basis of an identity (such as race, ethnicity, gender, religion, sexual orientation, ability, and national origin).</p> </li> <li> <p> <b>Insults</b> – Describes language or a statement that includes demeaning, humiliating, mocking, insulting, or belittling language. This type of language is also labeled as bullying.</p> </li> <li> <p> <b>Sexual</b> – Describes language or a statement that indicates sexual interest, activity, or arousal using direct or indirect references to body parts, physical traits, or sex.</p> </li> <li> <p> <b>Violence</b> – Describes language or a statement that includes glorification of or threats to inflict physical pain, hurt, or injury toward a person, group or thing.</p> </li> </ul> <p>Content filtering depends on the confidence classification of user inputs and FM responses across each of the four harmful categories. All input and output statements are classified into one of four confidence levels (NONE, LOW, MEDIUM, HIGH) for each harmful category. For example, if a statement is classified as <i>Hate</i> with HIGH confidence, the likelihood of the statement representing hateful content is high. A single statement can be classified across multiple categories with varying confidence levels. For example, a single statement can be classified as <i>Hate</i> with HIGH confidence, <i>Insults</i> with LOW confidence, <i>Sexual</i> with NONE confidence, and <i>Violence</i> with MEDIUM confidence.</p> <p>For more information, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/guardrails-filters.html">Guardrails content filters</a>.</p>
 * @public
 */
export interface GuardrailContentFilterConfig {
    /**
     * <p>The harmful category that the content filter is applied to.</p>
     * @public
     */
    type: GuardrailContentFilterType | undefined;
    /**
     * <p>The strength of the content filter to apply to prompts. As you increase the filter strength, the likelihood of filtering harmful content increases and the probability of seeing harmful content in your application reduces.</p>
     * @public
     */
    inputStrength: GuardrailFilterStrength | undefined;
    /**
     * <p>The strength of the content filter to apply to model responses. As you increase the filter strength, the likelihood of filtering harmful content increases and the probability of seeing harmful content in your application reduces.</p>
     * @public
     */
    outputStrength: GuardrailFilterStrength | undefined;
    /**
     * <p>The input modalities selected for the guardrail content filter configuration.</p>
     * @public
     */
    inputModalities?: GuardrailModality[] | undefined;
    /**
     * <p>The output modalities selected for the guardrail content filter configuration.</p>
     * @public
     */
    outputModalities?: GuardrailModality[] | undefined;
    /**
     * <p>Specifies the action to take when harmful content is detected. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    inputAction?: GuardrailContentFilterAction | undefined;
    /**
     * <p>Specifies the action to take when harmful content is detected in the output. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    outputAction?: GuardrailContentFilterAction | undefined;
    /**
     * <p>Specifies whether to enable guardrail evaluation on the input. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    inputEnabled?: boolean | undefined;
    /**
     * <p>Specifies whether to enable guardrail evaluation on the output. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    outputEnabled?: boolean | undefined;
}
/**
 * @public
 * @enum
 */
export declare const GuardrailContentFiltersTierName: {
    readonly CLASSIC: "CLASSIC";
    readonly STANDARD: "STANDARD";
};
/**
 * @public
 */
export type GuardrailContentFiltersTierName = (typeof GuardrailContentFiltersTierName)[keyof typeof GuardrailContentFiltersTierName];
/**
 * <p>The tier that your guardrail uses for content filters. Consider using a tier that balances performance, accuracy, and compatibility with your existing generative AI workflows.</p>
 * @public
 */
export interface GuardrailContentFiltersTierConfig {
    /**
     * <p>The tier that your guardrail uses for content filters. Valid values include:</p> <ul> <li> <p> <code>CLASSIC</code> tier – Provides established guardrails functionality supporting English, French, and Spanish languages.</p> </li> <li> <p> <code>STANDARD</code> tier – Provides a more robust solution than the <code>CLASSIC</code> tier and has more comprehensive language support. This tier requires that your guardrail use <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/guardrails-cross-region.html">cross-Region inference</a>.</p> </li> </ul>
     * @public
     */
    tierName: GuardrailContentFiltersTierName | undefined;
}
/**
 * <p>Contains details about how to handle harmful content.</p>
 * @public
 */
export interface GuardrailContentPolicyConfig {
    /**
     * <p>Contains the type of the content filter and how strongly it should apply to prompts and model responses.</p>
     * @public
     */
    filtersConfig: GuardrailContentFilterConfig[] | undefined;
    /**
     * <p>The tier that your guardrail uses for content filters.</p>
     * @public
     */
    tierConfig?: GuardrailContentFiltersTierConfig | undefined;
}
/**
 * @public
 * @enum
 */
export declare const GuardrailContextualGroundingAction: {
    readonly BLOCK: "BLOCK";
    readonly NONE: "NONE";
};
/**
 * @public
 */
export type GuardrailContextualGroundingAction = (typeof GuardrailContextualGroundingAction)[keyof typeof GuardrailContextualGroundingAction];
/**
 * @public
 * @enum
 */
export declare const GuardrailContextualGroundingFilterType: {
    readonly GROUNDING: "GROUNDING";
    readonly RELEVANCE: "RELEVANCE";
};
/**
 * @public
 */
export type GuardrailContextualGroundingFilterType = (typeof GuardrailContextualGroundingFilterType)[keyof typeof GuardrailContextualGroundingFilterType];
/**
 * <p>The filter configuration details for the guardrails contextual grounding filter.</p>
 * @public
 */
export interface GuardrailContextualGroundingFilterConfig {
    /**
     * <p>The filter details for the guardrails contextual grounding filter.</p>
     * @public
     */
    type: GuardrailContextualGroundingFilterType | undefined;
    /**
     * <p>The threshold details for the guardrails contextual grounding filter.</p>
     * @public
     */
    threshold: number | undefined;
    /**
     * <p>Specifies the action to take when content fails the contextual grounding evaluation. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    action?: GuardrailContextualGroundingAction | undefined;
    /**
     * <p>Specifies whether to enable contextual grounding evaluation. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    enabled?: boolean | undefined;
}
/**
 * <p>The policy configuration details for the guardrails contextual grounding policy.</p>
 * @public
 */
export interface GuardrailContextualGroundingPolicyConfig {
    /**
     * <p>The filter configuration details for the guardrails contextual grounding policy.</p>
     * @public
     */
    filtersConfig: GuardrailContextualGroundingFilterConfig[] | undefined;
}
/**
 * <p>The system-defined guardrail profile that you're using with your guardrail. Guardrail profiles define the destination Amazon Web Services Regions where guardrail inference requests can be automatically routed. Using guardrail profiles helps maintain guardrail performance and reliability when demand increases.</p> <p>For more information, see the <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/guardrails-cross-region.html">Amazon Bedrock User Guide</a>.</p>
 * @public
 */
export interface GuardrailCrossRegionConfig {
    /**
     * <p>The ID or Amazon Resource Name (ARN) of the guardrail profile that your guardrail is using. Guardrail profile availability depends on your current Amazon Web Services Region. For more information, see the <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/guardrails-cross-region-support.html">Amazon Bedrock User Guide</a>.</p>
     * @public
     */
    guardrailProfileIdentifier: string | undefined;
}
/**
 * @public
 * @enum
 */
export declare const GuardrailSensitiveInformationAction: {
    readonly ANONYMIZE: "ANONYMIZE";
    readonly BLOCK: "BLOCK";
    readonly NONE: "NONE";
};
/**
 * @public
 */
export type GuardrailSensitiveInformationAction = (typeof GuardrailSensitiveInformationAction)[keyof typeof GuardrailSensitiveInformationAction];
/**
 * @public
 * @enum
 */
export declare const GuardrailPiiEntityType: {
    readonly ADDRESS: "ADDRESS";
    readonly AGE: "AGE";
    readonly AWS_ACCESS_KEY: "AWS_ACCESS_KEY";
    readonly AWS_SECRET_KEY: "AWS_SECRET_KEY";
    readonly CA_HEALTH_NUMBER: "CA_HEALTH_NUMBER";
    readonly CA_SOCIAL_INSURANCE_NUMBER: "CA_SOCIAL_INSURANCE_NUMBER";
    readonly CREDIT_DEBIT_CARD_CVV: "CREDIT_DEBIT_CARD_CVV";
    readonly CREDIT_DEBIT_CARD_EXPIRY: "CREDIT_DEBIT_CARD_EXPIRY";
    readonly CREDIT_DEBIT_CARD_NUMBER: "CREDIT_DEBIT_CARD_NUMBER";
    readonly DRIVER_ID: "DRIVER_ID";
    readonly EMAIL: "EMAIL";
    readonly INTERNATIONAL_BANK_ACCOUNT_NUMBER: "INTERNATIONAL_BANK_ACCOUNT_NUMBER";
    readonly IP_ADDRESS: "IP_ADDRESS";
    readonly LICENSE_PLATE: "LICENSE_PLATE";
    readonly MAC_ADDRESS: "MAC_ADDRESS";
    readonly NAME: "NAME";
    readonly PASSWORD: "PASSWORD";
    readonly PHONE: "PHONE";
    readonly PIN: "PIN";
    readonly SWIFT_CODE: "SWIFT_CODE";
    readonly UK_NATIONAL_HEALTH_SERVICE_NUMBER: "UK_NATIONAL_HEALTH_SERVICE_NUMBER";
    readonly UK_NATIONAL_INSURANCE_NUMBER: "UK_NATIONAL_INSURANCE_NUMBER";
    readonly UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER: "UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER";
    readonly URL: "URL";
    readonly USERNAME: "USERNAME";
    readonly US_BANK_ACCOUNT_NUMBER: "US_BANK_ACCOUNT_NUMBER";
    readonly US_BANK_ROUTING_NUMBER: "US_BANK_ROUTING_NUMBER";
    readonly US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER: "US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER";
    readonly US_PASSPORT_NUMBER: "US_PASSPORT_NUMBER";
    readonly US_SOCIAL_SECURITY_NUMBER: "US_SOCIAL_SECURITY_NUMBER";
    readonly VEHICLE_IDENTIFICATION_NUMBER: "VEHICLE_IDENTIFICATION_NUMBER";
};
/**
 * @public
 */
export type GuardrailPiiEntityType = (typeof GuardrailPiiEntityType)[keyof typeof GuardrailPiiEntityType];
/**
 * <p>The PII entity to configure for the guardrail.</p>
 * @public
 */
export interface GuardrailPiiEntityConfig {
    /**
     * <p>Configure guardrail type when the PII entity is detected.</p> <p>The following PIIs are used to block or mask sensitive information:</p> <ul> <li> <p> <b>General</b> </p> <ul> <li> <p> <b>ADDRESS</b> </p> <p>A physical address, such as "100 Main Street, Anytown, USA" or "Suite #12, Building 123". An address can include information such as the street, building, location, city, state, country, county, zip code, precinct, and neighborhood. </p> </li> <li> <p> <b>AGE</b> </p> <p>An individual's age, including the quantity and unit of time. For example, in the phrase "I am 40 years old," Guardrails recognizes "40 years" as an age. </p> </li> <li> <p> <b>NAME</b> </p> <p>An individual's name. This entity type does not include titles, such as Dr., Mr., Mrs., or Miss. guardrails doesn't apply this entity type to names that are part of organizations or addresses. For example, guardrails recognizes the "John Doe Organization" as an organization, and it recognizes "Jane Doe Street" as an address. </p> </li> <li> <p> <b>EMAIL</b> </p> <p>An email address, such as <i><EMAIL></i>.</p> </li> <li> <p> <b>PHONE</b> </p> <p>A phone number. This entity type also includes fax and pager numbers. </p> </li> <li> <p> <b>USERNAME</b> </p> <p>A user name that identifies an account, such as a login name, screen name, nick name, or handle. </p> </li> <li> <p> <b>PASSWORD</b> </p> <p>An alphanumeric string that is used as a password, such as "*<i>very20special#pass*</i>". </p> </li> <li> <p> <b>DRIVER_ID</b> </p> <p>The number assigned to a driver's license, which is an official document permitting an individual to operate one or more motorized vehicles on a public road. A driver's license number consists of alphanumeric characters. </p> </li> <li> <p> <b>LICENSE_PLATE</b> </p> <p>A license plate for a vehicle is issued by the state or country where the vehicle is registered. The format for passenger vehicles is typically five to eight digits, consisting of upper-case letters and numbers. The format varies depending on the location of the issuing state or country. </p> </li> <li> <p> <b>VEHICLE_IDENTIFICATION_NUMBER</b> </p> <p>A Vehicle Identification Number (VIN) uniquely identifies a vehicle. VIN content and format are defined in the <i>ISO 3779</i> specification. Each country has specific codes and formats for VINs. </p> </li> </ul> </li> <li> <p> <b>Finance</b> </p> <ul> <li> <p> <b>CREDIT_DEBIT_CARD_CVV</b> </p> <p>A three-digit card verification code (CVV) that is present on VISA, MasterCard, and Discover credit and debit cards. For American Express credit or debit cards, the CVV is a four-digit numeric code. </p> </li> <li> <p> <b>CREDIT_DEBIT_CARD_EXPIRY</b> </p> <p>The expiration date for a credit or debit card. This number is usually four digits long and is often formatted as <i>month/year</i> or <i>MM/YY</i>. Guardrails recognizes expiration dates such as <i>01/21</i>, <i>01/2021</i>, and <i>Jan 2021</i>. </p> </li> <li> <p> <b>CREDIT_DEBIT_CARD_NUMBER</b> </p> <p>The number for a credit or debit card. These numbers can vary from 13 to 16 digits in length. However, Amazon Comprehend also recognizes credit or debit card numbers when only the last four digits are present. </p> </li> <li> <p> <b>PIN</b> </p> <p>A four-digit personal identification number (PIN) with which you can access your bank account. </p> </li> <li> <p> <b>INTERNATIONAL_BANK_ACCOUNT_NUMBER</b> </p> <p>An International Bank Account Number has specific formats in each country. For more information, see <a href="https://www.iban.com/structure">www.iban.com/structure</a>.</p> </li> <li> <p> <b>SWIFT_CODE</b> </p> <p>A SWIFT code is a standard format of Bank Identifier Code (BIC) used to specify a particular bank or branch. Banks use these codes for money transfers such as international wire transfers.</p> <p>SWIFT codes consist of eight or 11 characters. The 11-digit codes refer to specific branches, while eight-digit codes (or 11-digit codes ending in 'XXX') refer to the head or primary office.</p> </li> </ul> </li> <li> <p> <b>IT</b> </p> <ul> <li> <p> <b>IP_ADDRESS</b> </p> <p>An IPv4 address, such as <i>************</i>. </p> </li> <li> <p> <b>MAC_ADDRESS</b> </p> <p>A <i>media access control</i> (MAC) address is a unique identifier assigned to a network interface controller (NIC). </p> </li> <li> <p> <b>URL</b> </p> <p>A web address, such as <i>www.example.com</i>. </p> </li> <li> <p> <b>AWS_ACCESS_KEY</b> </p> <p>A unique identifier that's associated with a secret access key; you use the access key ID and secret access key to sign programmatic Amazon Web Services requests cryptographically. </p> </li> <li> <p> <b>AWS_SECRET_KEY</b> </p> <p>A unique identifier that's associated with an access key. You use the access key ID and secret access key to sign programmatic Amazon Web Services requests cryptographically. </p> </li> </ul> </li> <li> <p> <b>USA specific</b> </p> <ul> <li> <p> <b>US_BANK_ACCOUNT_NUMBER</b> </p> <p>A US bank account number, which is typically 10 to 12 digits long. </p> </li> <li> <p> <b>US_BANK_ROUTING_NUMBER</b> </p> <p>A US bank account routing number. These are typically nine digits long, </p> </li> <li> <p> <b>US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER</b> </p> <p>A US Individual Taxpayer Identification Number (ITIN) is a nine-digit number that starts with a "9" and contain a "7" or "8" as the fourth digit. An ITIN can be formatted with a space or a dash after the third and forth digits. </p> </li> <li> <p> <b>US_PASSPORT_NUMBER</b> </p> <p>A US passport number. Passport numbers range from six to nine alphanumeric characters. </p> </li> <li> <p> <b>US_SOCIAL_SECURITY_NUMBER</b> </p> <p>A US Social Security Number (SSN) is a nine-digit number that is issued to US citizens, permanent residents, and temporary working residents. </p> </li> </ul> </li> <li> <p> <b>Canada specific</b> </p> <ul> <li> <p> <b>CA_HEALTH_NUMBER</b> </p> <p>A Canadian Health Service Number is a 10-digit unique identifier, required for individuals to access healthcare benefits. </p> </li> <li> <p> <b>CA_SOCIAL_INSURANCE_NUMBER</b> </p> <p>A Canadian Social Insurance Number (SIN) is a nine-digit unique identifier, required for individuals to access government programs and benefits.</p> <p>The SIN is formatted as three groups of three digits, such as <i>123-456-789</i>. A SIN can be validated through a simple check-digit process called the <a href="https://www.wikipedia.org/wiki/Luhn_algorithm">Luhn algorithm</a>.</p> </li> </ul> </li> <li> <p> <b>UK Specific</b> </p> <ul> <li> <p> <b>UK_NATIONAL_HEALTH_SERVICE_NUMBER</b> </p> <p>A UK National Health Service Number is a 10-17 digit number, such as <i>************</i>. The current system formats the 10-digit number with spaces after the third and sixth digits. The final digit is an error-detecting checksum.</p> </li> <li> <p> <b>UK_NATIONAL_INSURANCE_NUMBER</b> </p> <p>A UK National Insurance Number (NINO) provides individuals with access to National Insurance (social security) benefits. It is also used for some purposes in the UK tax system.</p> <p>The number is nine digits long and starts with two letters, followed by six numbers and one letter. A NINO can be formatted with a space or a dash after the two letters and after the second, forth, and sixth digits.</p> </li> <li> <p> <b>UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER</b> </p> <p>A UK Unique Taxpayer Reference (UTR) is a 10-digit number that identifies a taxpayer or a business. </p> </li> </ul> </li> <li> <p> <b>Custom</b> </p> <ul> <li> <p> <b>Regex filter</b> - You can use a regular expressions to define patterns for a guardrail to recognize and act upon such as serial number, booking ID etc..</p> </li> </ul> </li> </ul>
     * @public
     */
    type: GuardrailPiiEntityType | undefined;
    /**
     * <p>Configure guardrail action when the PII entity is detected.</p>
     * @public
     */
    action: GuardrailSensitiveInformationAction | undefined;
    /**
     * <p>Specifies the action to take when harmful content is detected in the input. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>ANONYMIZE</code> – Mask the content and replace it with identifier tags.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    inputAction?: GuardrailSensitiveInformationAction | undefined;
    /**
     * <p>Specifies the action to take when harmful content is detected in the output. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>ANONYMIZE</code> – Mask the content and replace it with identifier tags.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    outputAction?: GuardrailSensitiveInformationAction | undefined;
    /**
     * <p>Specifies whether to enable guardrail evaluation on the input. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    inputEnabled?: boolean | undefined;
    /**
     * <p>Specifies whether to enable guardrail evaluation on the output. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    outputEnabled?: boolean | undefined;
}
/**
 * <p>The regular expression to configure for the guardrail.</p>
 * @public
 */
export interface GuardrailRegexConfig {
    /**
     * <p>The name of the regular expression to configure for the guardrail.</p>
     * @public
     */
    name: string | undefined;
    /**
     * <p>The description of the regular expression to configure for the guardrail.</p>
     * @public
     */
    description?: string | undefined;
    /**
     * <p>The regular expression pattern to configure for the guardrail.</p>
     * @public
     */
    pattern: string | undefined;
    /**
     * <p>The guardrail action to configure when matching regular expression is detected.</p>
     * @public
     */
    action: GuardrailSensitiveInformationAction | undefined;
    /**
     * <p>Specifies the action to take when harmful content is detected in the input. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    inputAction?: GuardrailSensitiveInformationAction | undefined;
    /**
     * <p>Specifies the action to take when harmful content is detected in the output. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    outputAction?: GuardrailSensitiveInformationAction | undefined;
    /**
     * <p>Specifies whether to enable guardrail evaluation on the input. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    inputEnabled?: boolean | undefined;
    /**
     * <p>Specifies whether to enable guardrail evaluation on the output. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    outputEnabled?: boolean | undefined;
}
/**
 * <p>Contains details about PII entities and regular expressions to configure for the guardrail.</p>
 * @public
 */
export interface GuardrailSensitiveInformationPolicyConfig {
    /**
     * <p>A list of PII entities to configure to the guardrail.</p>
     * @public
     */
    piiEntitiesConfig?: GuardrailPiiEntityConfig[] | undefined;
    /**
     * <p>A list of regular expressions to configure to the guardrail.</p>
     * @public
     */
    regexesConfig?: GuardrailRegexConfig[] | undefined;
}
/**
 * @public
 * @enum
 */
export declare const GuardrailTopicsTierName: {
    readonly CLASSIC: "CLASSIC";
    readonly STANDARD: "STANDARD";
};
/**
 * @public
 */
export type GuardrailTopicsTierName = (typeof GuardrailTopicsTierName)[keyof typeof GuardrailTopicsTierName];
/**
 * <p>The tier that your guardrail uses for denied topic filters. Consider using a tier that balances performance, accuracy, and compatibility with your existing generative AI workflows.</p>
 * @public
 */
export interface GuardrailTopicsTierConfig {
    /**
     * <p>The tier that your guardrail uses for denied topic filters. Valid values include:</p> <ul> <li> <p> <code>CLASSIC</code> tier – Provides established guardrails functionality supporting English, French, and Spanish languages.</p> </li> <li> <p> <code>STANDARD</code> tier – Provides a more robust solution than the <code>CLASSIC</code> tier and has more comprehensive language support. This tier requires that your guardrail use <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/guardrails-cross-region.html">cross-Region inference</a>.</p> </li> </ul>
     * @public
     */
    tierName: GuardrailTopicsTierName | undefined;
}
/**
 * @public
 * @enum
 */
export declare const GuardrailTopicAction: {
    readonly BLOCK: "BLOCK";
    readonly NONE: "NONE";
};
/**
 * @public
 */
export type GuardrailTopicAction = (typeof GuardrailTopicAction)[keyof typeof GuardrailTopicAction];
/**
 * @public
 * @enum
 */
export declare const GuardrailTopicType: {
    readonly DENY: "DENY";
};
/**
 * @public
 */
export type GuardrailTopicType = (typeof GuardrailTopicType)[keyof typeof GuardrailTopicType];
/**
 * <p>Details about topics for the guardrail to identify and deny.</p>
 * @public
 */
export interface GuardrailTopicConfig {
    /**
     * <p>The name of the topic to deny.</p>
     * @public
     */
    name: string | undefined;
    /**
     * <p>A definition of the topic to deny.</p>
     * @public
     */
    definition: string | undefined;
    /**
     * <p>A list of prompts, each of which is an example of a prompt that can be categorized as belonging to the topic.</p>
     * @public
     */
    examples?: string[] | undefined;
    /**
     * <p>Specifies to deny the topic.</p>
     * @public
     */
    type: GuardrailTopicType | undefined;
    /**
     * <p>Specifies the action to take when harmful content is detected in the input. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    inputAction?: GuardrailTopicAction | undefined;
    /**
     * <p>Specifies the action to take when harmful content is detected in the output. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    outputAction?: GuardrailTopicAction | undefined;
    /**
     * <p>Specifies whether to enable guardrail evaluation on the input. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    inputEnabled?: boolean | undefined;
    /**
     * <p>Specifies whether to enable guardrail evaluation on the output. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    outputEnabled?: boolean | undefined;
}
/**
 * <p>Contains details about topics that the guardrail should identify and deny.</p>
 * @public
 */
export interface GuardrailTopicPolicyConfig {
    /**
     * <p>A list of policies related to topics that the guardrail should deny.</p>
     * @public
     */
    topicsConfig: GuardrailTopicConfig[] | undefined;
    /**
     * <p>The tier that your guardrail uses for denied topic filters.</p>
     * @public
     */
    tierConfig?: GuardrailTopicsTierConfig | undefined;
}
/**
 * @public
 * @enum
 */
export declare const GuardrailWordAction: {
    readonly BLOCK: "BLOCK";
    readonly NONE: "NONE";
};
/**
 * @public
 */
export type GuardrailWordAction = (typeof GuardrailWordAction)[keyof typeof GuardrailWordAction];
/**
 * @public
 * @enum
 */
export declare const GuardrailManagedWordsType: {
    readonly PROFANITY: "PROFANITY";
};
/**
 * @public
 */
export type GuardrailManagedWordsType = (typeof GuardrailManagedWordsType)[keyof typeof GuardrailManagedWordsType];
/**
 * <p>The managed word list to configure for the guardrail.</p>
 * @public
 */
export interface GuardrailManagedWordsConfig {
    /**
     * <p>The managed word type to configure for the guardrail.</p>
     * @public
     */
    type: GuardrailManagedWordsType | undefined;
    /**
     * <p>Specifies the action to take when harmful content is detected in the input. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    inputAction?: GuardrailWordAction | undefined;
    /**
     * <p>Specifies the action to take when harmful content is detected in the output. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    outputAction?: GuardrailWordAction | undefined;
    /**
     * <p>Specifies whether to enable guardrail evaluation on the input. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    inputEnabled?: boolean | undefined;
    /**
     * <p>Specifies whether to enable guardrail evaluation on the output. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    outputEnabled?: boolean | undefined;
}
/**
 * <p>A word to configure for the guardrail.</p>
 * @public
 */
export interface GuardrailWordConfig {
    /**
     * <p>Text of the word configured for the guardrail to block.</p>
     * @public
     */
    text: string | undefined;
    /**
     * <p>Specifies the action to take when harmful content is detected in the input. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    inputAction?: GuardrailWordAction | undefined;
    /**
     * <p>Specifies the action to take when harmful content is detected in the output. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    outputAction?: GuardrailWordAction | undefined;
    /**
     * <p>Specifies whether to enable guardrail evaluation on the intput. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    inputEnabled?: boolean | undefined;
    /**
     * <p>Specifies whether to enable guardrail evaluation on the output. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    outputEnabled?: boolean | undefined;
}
/**
 * <p>Contains details about the word policy to configured for the guardrail.</p>
 * @public
 */
export interface GuardrailWordPolicyConfig {
    /**
     * <p>A list of words to configure for the guardrail.</p>
     * @public
     */
    wordsConfig?: GuardrailWordConfig[] | undefined;
    /**
     * <p>A list of managed words to configure for the guardrail.</p>
     * @public
     */
    managedWordListsConfig?: GuardrailManagedWordsConfig[] | undefined;
}
/**
 * @public
 */
export interface CreateGuardrailRequest {
    /**
     * <p>The name to give the guardrail.</p>
     * @public
     */
    name: string | undefined;
    /**
     * <p>A description of the guardrail.</p>
     * @public
     */
    description?: string | undefined;
    /**
     * <p>The topic policies to configure for the guardrail.</p>
     * @public
     */
    topicPolicyConfig?: GuardrailTopicPolicyConfig | undefined;
    /**
     * <p>The content filter policies to configure for the guardrail.</p>
     * @public
     */
    contentPolicyConfig?: GuardrailContentPolicyConfig | undefined;
    /**
     * <p>The word policy you configure for the guardrail.</p>
     * @public
     */
    wordPolicyConfig?: GuardrailWordPolicyConfig | undefined;
    /**
     * <p>The sensitive information policy to configure for the guardrail.</p>
     * @public
     */
    sensitiveInformationPolicyConfig?: GuardrailSensitiveInformationPolicyConfig | undefined;
    /**
     * <p>The contextual grounding policy configuration used to create a guardrail.</p>
     * @public
     */
    contextualGroundingPolicyConfig?: GuardrailContextualGroundingPolicyConfig | undefined;
    /**
     * <p>The system-defined guardrail profile that you're using with your guardrail. Guardrail profiles define the destination Amazon Web Services Regions where guardrail inference requests can be automatically routed.</p> <p>For more information, see the <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/guardrails-cross-region.html">Amazon Bedrock User Guide</a>.</p>
     * @public
     */
    crossRegionConfig?: GuardrailCrossRegionConfig | undefined;
    /**
     * <p>The message to return when the guardrail blocks a prompt.</p>
     * @public
     */
    blockedInputMessaging: string | undefined;
    /**
     * <p>The message to return when the guardrail blocks a model response.</p>
     * @public
     */
    blockedOutputsMessaging: string | undefined;
    /**
     * <p>The ARN of the KMS key that you use to encrypt the guardrail.</p>
     * @public
     */
    kmsKeyId?: string | undefined;
    /**
     * <p>The tags that you want to attach to the guardrail. </p>
     * @public
     */
    tags?: Tag[] | undefined;
    /**
     * <p>A unique, case-sensitive identifier to ensure that the API request completes no more than once. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href="https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html">Ensuring idempotency</a> in the <i>Amazon S3 User Guide</i>.</p>
     * @public
     */
    clientRequestToken?: string | undefined;
}
/**
 * @public
 */
export interface CreateGuardrailResponse {
    /**
     * <p>The unique identifier of the guardrail that was created.</p>
     * @public
     */
    guardrailId: string | undefined;
    /**
     * <p>The ARN of the guardrail.</p>
     * @public
     */
    guardrailArn: string | undefined;
    /**
     * <p>The version of the guardrail that was created. This value will always be <code>DRAFT</code>.</p>
     * @public
     */
    version: string | undefined;
    /**
     * <p>The time at which the guardrail was created.</p>
     * @public
     */
    createdAt: Date | undefined;
}
/**
 * @public
 */
export interface CreateGuardrailVersionRequest {
    /**
     * <p>The unique identifier of the guardrail. This can be an ID or the ARN.</p>
     * @public
     */
    guardrailIdentifier: string | undefined;
    /**
     * <p>A description of the guardrail version.</p>
     * @public
     */
    description?: string | undefined;
    /**
     * <p>A unique, case-sensitive identifier to ensure that the API request completes no more than once. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href="https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html">Ensuring idempotency</a> in the <i>Amazon S3 User Guide</i>.</p>
     * @public
     */
    clientRequestToken?: string | undefined;
}
/**
 * @public
 */
export interface CreateGuardrailVersionResponse {
    /**
     * <p>The unique identifier of the guardrail.</p>
     * @public
     */
    guardrailId: string | undefined;
    /**
     * <p>The number of the version of the guardrail.</p>
     * @public
     */
    version: string | undefined;
}
/**
 * @public
 */
export interface DeleteGuardrailRequest {
    /**
     * <p>The unique identifier of the guardrail. This can be an ID or the ARN.</p>
     * @public
     */
    guardrailIdentifier: string | undefined;
    /**
     * <p>The version of the guardrail.</p>
     * @public
     */
    guardrailVersion?: string | undefined;
}
/**
 * @public
 */
export interface DeleteGuardrailResponse {
}
/**
 * @public
 */
export interface GetGuardrailRequest {
    /**
     * <p>The unique identifier of the guardrail for which to get details. This can be an ID or the ARN.</p>
     * @public
     */
    guardrailIdentifier: string | undefined;
    /**
     * <p>The version of the guardrail for which to get details. If you don't specify a version, the response returns details for the <code>DRAFT</code> version.</p>
     * @public
     */
    guardrailVersion?: string | undefined;
}
/**
 * <p>Contains filter strengths for harmful content. Guardrails support the following content filters to detect and filter harmful user inputs and FM-generated outputs.</p> <ul> <li> <p> <b>Hate</b> – Describes language or a statement that discriminates, criticizes, insults, denounces, or dehumanizes a person or group on the basis of an identity (such as race, ethnicity, gender, religion, sexual orientation, ability, and national origin).</p> </li> <li> <p> <b>Insults</b> – Describes language or a statement that includes demeaning, humiliating, mocking, insulting, or belittling language. This type of language is also labeled as bullying.</p> </li> <li> <p> <b>Sexual</b> – Describes language or a statement that indicates sexual interest, activity, or arousal using direct or indirect references to body parts, physical traits, or sex.</p> </li> <li> <p> <b>Violence</b> – Describes language or a statement that includes glorification of or threats to inflict physical pain, hurt, or injury toward a person, group or thing.</p> </li> </ul> <p>Content filtering depends on the confidence classification of user inputs and FM responses across each of the four harmful categories. All input and output statements are classified into one of four confidence levels (NONE, LOW, MEDIUM, HIGH) for each harmful category. For example, if a statement is classified as <i>Hate</i> with HIGH confidence, the likelihood of the statement representing hateful content is high. A single statement can be classified across multiple categories with varying confidence levels. For example, a single statement can be classified as <i>Hate</i> with HIGH confidence, <i>Insults</i> with LOW confidence, <i>Sexual</i> with NONE confidence, and <i>Violence</i> with MEDIUM confidence.</p> <p>For more information, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/guardrails-filters.html">Guardrails content filters</a>.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href="https://docs.aws.amazon.com/bedrock/latest/APIReference/API_GetGuardrail.html#API_GetGuardrail_ResponseSyntax">GetGuardrail response body</a> </p> </li> </ul>
 * @public
 */
export interface GuardrailContentFilter {
    /**
     * <p>The harmful category that the content filter is applied to.</p>
     * @public
     */
    type: GuardrailContentFilterType | undefined;
    /**
     * <p>The strength of the content filter to apply to prompts. As you increase the filter strength, the likelihood of filtering harmful content increases and the probability of seeing harmful content in your application reduces.</p>
     * @public
     */
    inputStrength: GuardrailFilterStrength | undefined;
    /**
     * <p>The strength of the content filter to apply to model responses. As you increase the filter strength, the likelihood of filtering harmful content increases and the probability of seeing harmful content in your application reduces.</p>
     * @public
     */
    outputStrength: GuardrailFilterStrength | undefined;
    /**
     * <p>The input modalities selected for the guardrail content filter.</p>
     * @public
     */
    inputModalities?: GuardrailModality[] | undefined;
    /**
     * <p>The output modalities selected for the guardrail content filter.</p>
     * @public
     */
    outputModalities?: GuardrailModality[] | undefined;
    /**
     * <p>The action to take when harmful content is detected in the input. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    inputAction?: GuardrailContentFilterAction | undefined;
    /**
     * <p>The action to take when harmful content is detected in the output. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    outputAction?: GuardrailContentFilterAction | undefined;
    /**
     * <p>Indicates whether guardrail evaluation is enabled on the input. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    inputEnabled?: boolean | undefined;
    /**
     * <p>Indicates whether guardrail evaluation is enabled on the output. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    outputEnabled?: boolean | undefined;
}
/**
 * <p>The tier that your guardrail uses for content filters.</p>
 * @public
 */
export interface GuardrailContentFiltersTier {
    /**
     * <p>The tier that your guardrail uses for content filters. Valid values include:</p> <ul> <li> <p> <code>CLASSIC</code> tier – Provides established guardrails functionality supporting English, French, and Spanish languages.</p> </li> <li> <p> <code>STANDARD</code> tier – Provides a more robust solution than the <code>CLASSIC</code> tier and has more comprehensive language support. This tier requires that your guardrail use <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/guardrails-cross-region.html">cross-Region inference</a>.</p> </li> </ul>
     * @public
     */
    tierName: GuardrailContentFiltersTierName | undefined;
}
/**
 * <p>Contains details about how to handle harmful content.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href="https://docs.aws.amazon.com/bedrock/latest/APIReference/API_GetGuardrail.html#API_GetGuardrail_ResponseSyntax">GetGuardrail response body</a> </p> </li> </ul>
 * @public
 */
export interface GuardrailContentPolicy {
    /**
     * <p>Contains the type of the content filter and how strongly it should apply to prompts and model responses.</p>
     * @public
     */
    filters?: GuardrailContentFilter[] | undefined;
    /**
     * <p>The tier that your guardrail uses for content filters.</p>
     * @public
     */
    tier?: GuardrailContentFiltersTier | undefined;
}
/**
 * <p>The details for the guardrails contextual grounding filter.</p>
 * @public
 */
export interface GuardrailContextualGroundingFilter {
    /**
     * <p>The filter type details for the guardrails contextual grounding filter.</p>
     * @public
     */
    type: GuardrailContextualGroundingFilterType | undefined;
    /**
     * <p>The threshold details for the guardrails contextual grounding filter.</p>
     * @public
     */
    threshold: number | undefined;
    /**
     * <p>The action to take when content fails the contextual grounding evaluation. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    action?: GuardrailContextualGroundingAction | undefined;
    /**
     * <p>Indicates whether contextual grounding is enabled for evaluation. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    enabled?: boolean | undefined;
}
/**
 * <p>The details for the guardrails contextual grounding policy.</p>
 * @public
 */
export interface GuardrailContextualGroundingPolicy {
    /**
     * <p>The filter details for the guardrails contextual grounding policy.</p>
     * @public
     */
    filters: GuardrailContextualGroundingFilter[] | undefined;
}
/**
 * <p>Contains details about the system-defined guardrail profile that you're using with your guardrail for cross-Region inference.</p> <p>For more information, see the <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/guardrails-cross-region.html">Amazon Bedrock User Guide</a>.</p>
 * @public
 */
export interface GuardrailCrossRegionDetails {
    /**
     * <p>The ID of the guardrail profile that your guardrail is using. Profile availability depends on your current Amazon Web Services Region. For more information, see the <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/guardrails-cross-region-support.html">Amazon Bedrock User Guide</a>.</p>
     * @public
     */
    guardrailProfileId?: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the guardrail profile that you're using with your guardrail.</p>
     * @public
     */
    guardrailProfileArn?: string | undefined;
}
/**
 * <p>The PII entity configured for the guardrail.</p>
 * @public
 */
export interface GuardrailPiiEntity {
    /**
     * <p>The type of PII entity. For example, Social Security Number.</p>
     * @public
     */
    type: GuardrailPiiEntityType | undefined;
    /**
     * <p>The configured guardrail action when PII entity is detected.</p>
     * @public
     */
    action: GuardrailSensitiveInformationAction | undefined;
    /**
     * <p>The action to take when harmful content is detected in the input. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>ANONYMIZE</code> – Mask the content and replace it with identifier tags.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    inputAction?: GuardrailSensitiveInformationAction | undefined;
    /**
     * <p>The action to take when harmful content is detected in the output. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>ANONYMIZE</code> – Mask the content and replace it with identifier tags.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    outputAction?: GuardrailSensitiveInformationAction | undefined;
    /**
     * <p>Indicates whether guardrail evaluation is enabled on the input. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    inputEnabled?: boolean | undefined;
    /**
     * <p>Indicates whether guardrail evaluation is enabled on the output. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    outputEnabled?: boolean | undefined;
}
/**
 * <p>The regular expression configured for the guardrail.</p>
 * @public
 */
export interface GuardrailRegex {
    /**
     * <p>The name of the regular expression for the guardrail.</p>
     * @public
     */
    name: string | undefined;
    /**
     * <p>The description of the regular expression for the guardrail.</p>
     * @public
     */
    description?: string | undefined;
    /**
     * <p>The pattern of the regular expression configured for the guardrail.</p>
     * @public
     */
    pattern: string | undefined;
    /**
     * <p>The action taken when a match to the regular expression is detected.</p>
     * @public
     */
    action: GuardrailSensitiveInformationAction | undefined;
    /**
     * <p>The action to take when harmful content is detected in the input. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    inputAction?: GuardrailSensitiveInformationAction | undefined;
    /**
     * <p>The action to take when harmful content is detected in the output. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    outputAction?: GuardrailSensitiveInformationAction | undefined;
    /**
     * <p>Indicates whether guardrail evaluation is enabled on the input. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    inputEnabled?: boolean | undefined;
    /**
     * <p>Indicates whether guardrail evaluation is enabled on the output. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    outputEnabled?: boolean | undefined;
}
/**
 * <p>Contains details about PII entities and regular expressions configured for the guardrail.</p>
 * @public
 */
export interface GuardrailSensitiveInformationPolicy {
    /**
     * <p>The list of PII entities configured for the guardrail.</p>
     * @public
     */
    piiEntities?: GuardrailPiiEntity[] | undefined;
    /**
     * <p>The list of regular expressions configured for the guardrail.</p>
     * @public
     */
    regexes?: GuardrailRegex[] | undefined;
}
/**
 * @public
 * @enum
 */
export declare const GuardrailStatus: {
    readonly CREATING: "CREATING";
    readonly DELETING: "DELETING";
    readonly FAILED: "FAILED";
    readonly READY: "READY";
    readonly UPDATING: "UPDATING";
    readonly VERSIONING: "VERSIONING";
};
/**
 * @public
 */
export type GuardrailStatus = (typeof GuardrailStatus)[keyof typeof GuardrailStatus];
/**
 * <p>The tier that your guardrail uses for denied topic filters.</p>
 * @public
 */
export interface GuardrailTopicsTier {
    /**
     * <p>The tier that your guardrail uses for denied topic filters. Valid values include:</p> <ul> <li> <p> <code>CLASSIC</code> tier – Provides established guardrails functionality supporting English, French, and Spanish languages.</p> </li> <li> <p> <code>STANDARD</code> tier – Provides a more robust solution than the <code>CLASSIC</code> tier and has more comprehensive language support. This tier requires that your guardrail use <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/guardrails-cross-region.html">cross-Region inference</a>.</p> </li> </ul>
     * @public
     */
    tierName: GuardrailTopicsTierName | undefined;
}
/**
 * <p>Details about topics for the guardrail to identify and deny.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href="https://docs.aws.amazon.com/bedrock/latest/APIReference/API_GetGuardrail.html#API_GetGuardrail_ResponseSyntax">GetGuardrail response body</a> </p> </li> </ul>
 * @public
 */
export interface GuardrailTopic {
    /**
     * <p>The name of the topic to deny.</p>
     * @public
     */
    name: string | undefined;
    /**
     * <p>A definition of the topic to deny.</p>
     * @public
     */
    definition: string | undefined;
    /**
     * <p>A list of prompts, each of which is an example of a prompt that can be categorized as belonging to the topic.</p>
     * @public
     */
    examples?: string[] | undefined;
    /**
     * <p>Specifies to deny the topic.</p>
     * @public
     */
    type?: GuardrailTopicType | undefined;
    /**
     * <p>The action to take when harmful content is detected in the input. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    inputAction?: GuardrailTopicAction | undefined;
    /**
     * <p>The action to take when harmful content is detected in the output. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    outputAction?: GuardrailTopicAction | undefined;
    /**
     * <p>Indicates whether guardrail evaluation is enabled on the input. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    inputEnabled?: boolean | undefined;
    /**
     * <p>Indicates whether guardrail evaluation is enabled on the output. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    outputEnabled?: boolean | undefined;
}
/**
 * <p>Contains details about topics that the guardrail should identify and deny.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href="https://docs.aws.amazon.com/bedrock/latest/APIReference/API_GetGuardrail.html#API_GetGuardrail_ResponseSyntax">GetGuardrail response body</a> </p> </li> </ul>
 * @public
 */
export interface GuardrailTopicPolicy {
    /**
     * <p>A list of policies related to topics that the guardrail should deny.</p>
     * @public
     */
    topics: GuardrailTopic[] | undefined;
    /**
     * <p>The tier that your guardrail uses for denied topic filters.</p>
     * @public
     */
    tier?: GuardrailTopicsTier | undefined;
}
/**
 * <p>The managed word list that was configured for the guardrail. (This is a list of words that are pre-defined and managed by guardrails only.)</p>
 * @public
 */
export interface GuardrailManagedWords {
    /**
     * <p>ManagedWords$type The managed word type that was configured for the guardrail. (For now, we only offer profanity word list)</p>
     * @public
     */
    type: GuardrailManagedWordsType | undefined;
    /**
     * <p>The action to take when harmful content is detected in the input. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    inputAction?: GuardrailWordAction | undefined;
    /**
     * <p>The action to take when harmful content is detected in the output. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    outputAction?: GuardrailWordAction | undefined;
    /**
     * <p>Indicates whether guardrail evaluation is enabled on the input. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    inputEnabled?: boolean | undefined;
    /**
     * <p>Indicates whether guardrail evaluation is enabled on the output. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    outputEnabled?: boolean | undefined;
}
/**
 * <p>A word configured for the guardrail.</p>
 * @public
 */
export interface GuardrailWord {
    /**
     * <p>Text of the word configured for the guardrail to block.</p>
     * @public
     */
    text: string | undefined;
    /**
     * <p>The action to take when harmful content is detected in the input. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    inputAction?: GuardrailWordAction | undefined;
    /**
     * <p>The action to take when harmful content is detected in the output. Supported values include:</p> <ul> <li> <p> <code>BLOCK</code> – Block the content and replace it with blocked messaging.</p> </li> <li> <p> <code>NONE</code> – Take no action but return detection information in the trace response.</p> </li> </ul>
     * @public
     */
    outputAction?: GuardrailWordAction | undefined;
    /**
     * <p>Indicates whether guardrail evaluation is enabled on the input. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    inputEnabled?: boolean | undefined;
    /**
     * <p>Indicates whether guardrail evaluation is enabled on the output. When disabled, you aren't charged for the evaluation. The evaluation doesn't appear in the response.</p>
     * @public
     */
    outputEnabled?: boolean | undefined;
}
/**
 * <p>Contains details about the word policy configured for the guardrail.</p>
 * @public
 */
export interface GuardrailWordPolicy {
    /**
     * <p>A list of words configured for the guardrail.</p>
     * @public
     */
    words?: GuardrailWord[] | undefined;
    /**
     * <p>A list of managed words configured for the guardrail.</p>
     * @public
     */
    managedWordLists?: GuardrailManagedWords[] | undefined;
}
/**
 * @public
 */
export interface GetGuardrailResponse {
    /**
     * <p>The name of the guardrail.</p>
     * @public
     */
    name: string | undefined;
    /**
     * <p>The description of the guardrail.</p>
     * @public
     */
    description?: string | undefined;
    /**
     * <p>The unique identifier of the guardrail.</p>
     * @public
     */
    guardrailId: string | undefined;
    /**
     * <p>The ARN of the guardrail.</p>
     * @public
     */
    guardrailArn: string | undefined;
    /**
     * <p>The version of the guardrail.</p>
     * @public
     */
    version: string | undefined;
    /**
     * <p>The status of the guardrail.</p>
     * @public
     */
    status: GuardrailStatus | undefined;
    /**
     * <p>The topic policy that was configured for the guardrail.</p>
     * @public
     */
    topicPolicy?: GuardrailTopicPolicy | undefined;
    /**
     * <p>The content policy that was configured for the guardrail.</p>
     * @public
     */
    contentPolicy?: GuardrailContentPolicy | undefined;
    /**
     * <p>The word policy that was configured for the guardrail.</p>
     * @public
     */
    wordPolicy?: GuardrailWordPolicy | undefined;
    /**
     * <p>The sensitive information policy that was configured for the guardrail.</p>
     * @public
     */
    sensitiveInformationPolicy?: GuardrailSensitiveInformationPolicy | undefined;
    /**
     * <p>The contextual grounding policy used in the guardrail.</p>
     * @public
     */
    contextualGroundingPolicy?: GuardrailContextualGroundingPolicy | undefined;
    /**
     * <p>Details about the system-defined guardrail profile that you're using with your guardrail, including the guardrail profile ID and Amazon Resource Name (ARN).</p>
     * @public
     */
    crossRegionDetails?: GuardrailCrossRegionDetails | undefined;
    /**
     * <p>The date and time at which the guardrail was created.</p>
     * @public
     */
    createdAt: Date | undefined;
    /**
     * <p>The date and time at which the guardrail was updated.</p>
     * @public
     */
    updatedAt: Date | undefined;
    /**
     * <p>Appears if the <code>status</code> is <code>FAILED</code>. A list of reasons for why the guardrail failed to be created, updated, versioned, or deleted.</p>
     * @public
     */
    statusReasons?: string[] | undefined;
    /**
     * <p>Appears if the <code>status</code> of the guardrail is <code>FAILED</code>. A list of recommendations to carry out before retrying the request.</p>
     * @public
     */
    failureRecommendations?: string[] | undefined;
    /**
     * <p>The message that the guardrail returns when it blocks a prompt.</p>
     * @public
     */
    blockedInputMessaging: string | undefined;
    /**
     * <p>The message that the guardrail returns when it blocks a model response.</p>
     * @public
     */
    blockedOutputsMessaging: string | undefined;
    /**
     * <p>The ARN of the KMS key that encrypts the guardrail.</p>
     * @public
     */
    kmsKeyArn?: string | undefined;
}
/**
 * @public
 */
export interface ListGuardrailsRequest {
    /**
     * <p>The unique identifier of the guardrail. This can be an ID or the ARN.</p>
     * @public
     */
    guardrailIdentifier?: string | undefined;
    /**
     * <p>The maximum number of results to return in the response.</p>
     * @public
     */
    maxResults?: number | undefined;
    /**
     * <p>If there are more results than were returned in the response, the response returns a <code>nextToken</code> that you can send in another <code>ListGuardrails</code> request to see the next batch of results.</p>
     * @public
     */
    nextToken?: string | undefined;
}
/**
 * <p>Contains details about a guardrail.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href="https://docs.aws.amazon.com/bedrock/latest/APIReference/API_ListGuardrails.html#API_ListGuardrails_ResponseSyntax">ListGuardrails response body</a> </p> </li> </ul>
 * @public
 */
export interface GuardrailSummary {
    /**
     * <p>The unique identifier of the guardrail.</p>
     * @public
     */
    id: string | undefined;
    /**
     * <p>The ARN of the guardrail.</p>
     * @public
     */
    arn: string | undefined;
    /**
     * <p>The status of the guardrail.</p>
     * @public
     */
    status: GuardrailStatus | undefined;
    /**
     * <p>The name of the guardrail.</p>
     * @public
     */
    name: string | undefined;
    /**
     * <p>A description of the guardrail.</p>
     * @public
     */
    description?: string | undefined;
    /**
     * <p>The version of the guardrail.</p>
     * @public
     */
    version: string | undefined;
    /**
     * <p>The date and time at which the guardrail was created.</p>
     * @public
     */
    createdAt: Date | undefined;
    /**
     * <p>The date and time at which the guardrail was last updated.</p>
     * @public
     */
    updatedAt: Date | undefined;
    /**
     * <p>Details about the system-defined guardrail profile that you're using with your guardrail, including the guardrail profile ID and Amazon Resource Name (ARN).</p>
     * @public
     */
    crossRegionDetails?: GuardrailCrossRegionDetails | undefined;
}
/**
 * @public
 */
export interface ListGuardrailsResponse {
    /**
     * <p>A list of objects, each of which contains details about a guardrail.</p>
     * @public
     */
    guardrails: GuardrailSummary[] | undefined;
    /**
     * <p>If there are more results than were returned in the response, the response returns a <code>nextToken</code> that you can send in another <code>ListGuardrails</code> request to see the next batch of results.</p>
     * @public
     */
    nextToken?: string | undefined;
}
/**
 * @public
 */
export interface UpdateGuardrailRequest {
    /**
     * <p>The unique identifier of the guardrail. This can be an ID or the ARN.</p>
     * @public
     */
    guardrailIdentifier: string | undefined;
    /**
     * <p>A name for the guardrail.</p>
     * @public
     */
    name: string | undefined;
    /**
     * <p>A description of the guardrail.</p>
     * @public
     */
    description?: string | undefined;
    /**
     * <p>The topic policy to configure for the guardrail.</p>
     * @public
     */
    topicPolicyConfig?: GuardrailTopicPolicyConfig | undefined;
    /**
     * <p>The content policy to configure for the guardrail.</p>
     * @public
     */
    contentPolicyConfig?: GuardrailContentPolicyConfig | undefined;
    /**
     * <p>The word policy to configure for the guardrail.</p>
     * @public
     */
    wordPolicyConfig?: GuardrailWordPolicyConfig | undefined;
    /**
     * <p>The sensitive information policy to configure for the guardrail.</p>
     * @public
     */
    sensitiveInformationPolicyConfig?: GuardrailSensitiveInformationPolicyConfig | undefined;
    /**
     * <p>The contextual grounding policy configuration used to update a guardrail.</p>
     * @public
     */
    contextualGroundingPolicyConfig?: GuardrailContextualGroundingPolicyConfig | undefined;
    /**
     * <p>The system-defined guardrail profile that you're using with your guardrail. Guardrail profiles define the destination Amazon Web Services Regions where guardrail inference requests can be automatically routed.</p> <p>For more information, see the <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/guardrails-cross-region.html">Amazon Bedrock User Guide</a>.</p>
     * @public
     */
    crossRegionConfig?: GuardrailCrossRegionConfig | undefined;
    /**
     * <p>The message to return when the guardrail blocks a prompt.</p>
     * @public
     */
    blockedInputMessaging: string | undefined;
    /**
     * <p>The message to return when the guardrail blocks a model response.</p>
     * @public
     */
    blockedOutputsMessaging: string | undefined;
    /**
     * <p>The ARN of the KMS key with which to encrypt the guardrail.</p>
     * @public
     */
    kmsKeyId?: string | undefined;
}
/**
 * @public
 */
export interface UpdateGuardrailResponse {
    /**
     * <p>The unique identifier of the guardrail</p>
     * @public
     */
    guardrailId: string | undefined;
    /**
     * <p>The ARN of the guardrail.</p>
     * @public
     */
    guardrailArn: string | undefined;
    /**
     * <p>The version of the guardrail.</p>
     * @public
     */
    version: string | undefined;
    /**
     * <p>The date and time at which the guardrail was updated.</p>
     * @public
     */
    updatedAt: Date | undefined;
}
/**
 * <p>Contains information about the model or system-defined inference profile that is the source for an inference profile..</p>
 * @public
 */
export type InferenceProfileModelSource = InferenceProfileModelSource.CopyFromMember | InferenceProfileModelSource.$UnknownMember;
/**
 * @public
 */
export declare namespace InferenceProfileModelSource {
    /**
     * <p>The ARN of the model or system-defined inference profile that is the source for the inference profile.</p>
     * @public
     */
    interface CopyFromMember {
        copyFrom: string;
        $unknown?: never;
    }
    /**
     * @public
     */
    interface $UnknownMember {
        copyFrom?: never;
        $unknown: [string, any];
    }
    interface Visitor<T> {
        copyFrom: (value: string) => T;
        _: (name: string, value: any) => T;
    }
    const visit: <T>(value: InferenceProfileModelSource, visitor: Visitor<T>) => T;
}
/**
 * @public
 */
export interface CreateInferenceProfileRequest {
    /**
     * <p>A name for the inference profile.</p>
     * @public
     */
    inferenceProfileName: string | undefined;
    /**
     * <p>A description for the inference profile.</p>
     * @public
     */
    description?: string | undefined;
    /**
     * <p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href="https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html">Ensuring idempotency</a>.</p>
     * @public
     */
    clientRequestToken?: string | undefined;
    /**
     * <p>The foundation model or system-defined inference profile that the inference profile will track metrics and costs for.</p>
     * @public
     */
    modelSource: InferenceProfileModelSource | undefined;
    /**
     * <p>An array of objects, each of which contains a tag and its value. For more information, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/what-is-service.html">Tagging resources</a> in the <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/what-is-service.html">Amazon Bedrock User Guide</a>.</p>
     * @public
     */
    tags?: Tag[] | undefined;
}
/**
 * @public
 * @enum
 */
export declare const InferenceProfileStatus: {
    readonly ACTIVE: "ACTIVE";
};
/**
 * @public
 */
export type InferenceProfileStatus = (typeof InferenceProfileStatus)[keyof typeof InferenceProfileStatus];
/**
 * @public
 */
export interface CreateInferenceProfileResponse {
    /**
     * <p>The ARN of the inference profile that you created.</p>
     * @public
     */
    inferenceProfileArn: string | undefined;
    /**
     * <p>The status of the inference profile. <code>ACTIVE</code> means that the inference profile is ready to be used.</p>
     * @public
     */
    status?: InferenceProfileStatus | undefined;
}
/**
 * @public
 */
export interface DeleteInferenceProfileRequest {
    /**
     * <p>The Amazon Resource Name (ARN) or ID of the application inference profile to delete.</p>
     * @public
     */
    inferenceProfileIdentifier: string | undefined;
}
/**
 * @public
 */
export interface DeleteInferenceProfileResponse {
}
/**
 * @public
 */
export interface GetInferenceProfileRequest {
    /**
     * <p>The ID or Amazon Resource Name (ARN) of the inference profile.</p>
     * @public
     */
    inferenceProfileIdentifier: string | undefined;
}
/**
 * <p>Contains information about a model.</p>
 * @public
 */
export interface InferenceProfileModel {
    /**
     * <p>The Amazon Resource Name (ARN) of the model.</p>
     * @public
     */
    modelArn?: string | undefined;
}
/**
 * @public
 * @enum
 */
export declare const InferenceProfileType: {
    readonly APPLICATION: "APPLICATION";
    readonly SYSTEM_DEFINED: "SYSTEM_DEFINED";
};
/**
 * @public
 */
export type InferenceProfileType = (typeof InferenceProfileType)[keyof typeof InferenceProfileType];
/**
 * @public
 */
export interface GetInferenceProfileResponse {
    /**
     * <p>The name of the inference profile.</p>
     * @public
     */
    inferenceProfileName: string | undefined;
    /**
     * <p>The description of the inference profile.</p>
     * @public
     */
    description?: string | undefined;
    /**
     * <p>The time at which the inference profile was created.</p>
     * @public
     */
    createdAt?: Date | undefined;
    /**
     * <p>The time at which the inference profile was last updated.</p>
     * @public
     */
    updatedAt?: Date | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the inference profile.</p>
     * @public
     */
    inferenceProfileArn: string | undefined;
    /**
     * <p>A list of information about each model in the inference profile.</p>
     * @public
     */
    models: InferenceProfileModel[] | undefined;
    /**
     * <p>The unique identifier of the inference profile.</p>
     * @public
     */
    inferenceProfileId: string | undefined;
    /**
     * <p>The status of the inference profile. <code>ACTIVE</code> means that the inference profile is ready to be used.</p>
     * @public
     */
    status: InferenceProfileStatus | undefined;
    /**
     * <p>The type of the inference profile. The following types are possible:</p> <ul> <li> <p> <code>SYSTEM_DEFINED</code> – The inference profile is defined by Amazon Bedrock. You can route inference requests across regions with these inference profiles.</p> </li> <li> <p> <code>APPLICATION</code> – The inference profile was created by a user. This type of inference profile can track metrics and costs when invoking the model in it. The inference profile may route requests to one or multiple regions.</p> </li> </ul>
     * @public
     */
    type: InferenceProfileType | undefined;
}
/**
 * @public
 */
export interface ListInferenceProfilesRequest {
    /**
     * <p>The maximum number of results to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>
     * @public
     */
    maxResults?: number | undefined;
    /**
     * <p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results.</p>
     * @public
     */
    nextToken?: string | undefined;
    /**
     * <p>Filters for inference profiles that match the type you specify.</p> <ul> <li> <p> <code>SYSTEM_DEFINED</code> – The inference profile is defined by Amazon Bedrock. You can route inference requests across regions with these inference profiles.</p> </li> <li> <p> <code>APPLICATION</code> – The inference profile was created by a user. This type of inference profile can track metrics and costs when invoking the model in it. The inference profile may route requests to one or multiple regions.</p> </li> </ul>
     * @public
     */
    typeEquals?: InferenceProfileType | undefined;
}
/**
 * <p>Contains information about an inference profile.</p>
 * @public
 */
export interface InferenceProfileSummary {
    /**
     * <p>The name of the inference profile.</p>
     * @public
     */
    inferenceProfileName: string | undefined;
    /**
     * <p>The description of the inference profile.</p>
     * @public
     */
    description?: string | undefined;
    /**
     * <p>The time at which the inference profile was created.</p>
     * @public
     */
    createdAt?: Date | undefined;
    /**
     * <p>The time at which the inference profile was last updated.</p>
     * @public
     */
    updatedAt?: Date | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the inference profile.</p>
     * @public
     */
    inferenceProfileArn: string | undefined;
    /**
     * <p>A list of information about each model in the inference profile.</p>
     * @public
     */
    models: InferenceProfileModel[] | undefined;
    /**
     * <p>The unique identifier of the inference profile.</p>
     * @public
     */
    inferenceProfileId: string | undefined;
    /**
     * <p>The status of the inference profile. <code>ACTIVE</code> means that the inference profile is ready to be used.</p>
     * @public
     */
    status: InferenceProfileStatus | undefined;
    /**
     * <p>The type of the inference profile. The following types are possible:</p> <ul> <li> <p> <code>SYSTEM_DEFINED</code> – The inference profile is defined by Amazon Bedrock. You can route inference requests across regions with these inference profiles.</p> </li> <li> <p> <code>APPLICATION</code> – The inference profile was created by a user. This type of inference profile can track metrics and costs when invoking the model in it. The inference profile may route requests to one or multiple regions.</p> </li> </ul>
     * @public
     */
    type: InferenceProfileType | undefined;
}
/**
 * @public
 */
export interface ListInferenceProfilesResponse {
    /**
     * <p>A list of information about each inference profile that you can use.</p>
     * @public
     */
    inferenceProfileSummaries?: InferenceProfileSummary[] | undefined;
    /**
     * <p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, use this token when making another request in the <code>nextToken</code> field to return the next batch of results.</p>
     * @public
     */
    nextToken?: string | undefined;
}
/**
 * @public
 */
export interface DeleteModelInvocationLoggingConfigurationRequest {
}
/**
 * @public
 */
export interface DeleteModelInvocationLoggingConfigurationResponse {
}
/**
 * @public
 */
export interface GetModelInvocationLoggingConfigurationRequest {
}
/**
 * <p>S3 configuration for storing log data.</p>
 * @public
 */
export interface S3Config {
    /**
     * <p>S3 bucket name.</p>
     * @public
     */
    bucketName: string | undefined;
    /**
     * <p>S3 prefix. </p>
     * @public
     */
    keyPrefix?: string | undefined;
}
/**
 * <p>CloudWatch logging configuration.</p>
 * @public
 */
export interface CloudWatchConfig {
    /**
     * <p>The log group name.</p>
     * @public
     */
    logGroupName: string | undefined;
    /**
     * <p>The role Amazon Resource Name (ARN).</p>
     * @public
     */
    roleArn: string | undefined;
    /**
     * <p>S3 configuration for delivering a large amount of data.</p>
     * @public
     */
    largeDataDeliveryS3Config?: S3Config | undefined;
}
/**
 * <p>Configuration fields for invocation logging.</p>
 * @public
 */
export interface LoggingConfig {
    /**
     * <p>CloudWatch logging configuration.</p>
     * @public
     */
    cloudWatchConfig?: CloudWatchConfig | undefined;
    /**
     * <p>S3 configuration for storing log data.</p>
     * @public
     */
    s3Config?: S3Config | undefined;
    /**
     * <p>Set to include text data in the log delivery.</p>
     * @public
     */
    textDataDeliveryEnabled?: boolean | undefined;
    /**
     * <p>Set to include image data in the log delivery.</p>
     * @public
     */
    imageDataDeliveryEnabled?: boolean | undefined;
    /**
     * <p>Set to include embeddings data in the log delivery.</p>
     * @public
     */
    embeddingDataDeliveryEnabled?: boolean | undefined;
    /**
     * <p>Set to include video data in the log delivery.</p>
     * @public
     */
    videoDataDeliveryEnabled?: boolean | undefined;
}
/**
 * @public
 */
export interface GetModelInvocationLoggingConfigurationResponse {
    /**
     * <p>The current configuration values.</p>
     * @public
     */
    loggingConfig?: LoggingConfig | undefined;
}
/**
 * @public
 */
export interface PutModelInvocationLoggingConfigurationRequest {
    /**
     * <p>The logging configuration values to set.</p>
     * @public
     */
    loggingConfig: LoggingConfig | undefined;
}
/**
 * @public
 */
export interface PutModelInvocationLoggingConfigurationResponse {
}
/**
 * @public
 */
export interface CreateModelCopyJobRequest {
    /**
     * <p>The Amazon Resource Name (ARN) of the model to be copied.</p>
     * @public
     */
    sourceModelArn: string | undefined;
    /**
     * <p>A name for the copied model.</p>
     * @public
     */
    targetModelName: string | undefined;
    /**
     * <p>The ARN of the KMS key that you use to encrypt the model copy.</p>
     * @public
     */
    modelKmsKeyId?: string | undefined;
    /**
     * <p>Tags to associate with the target model. For more information, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/tagging.html">Tag resources</a> in the <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/what-is-service.html">Amazon Bedrock User Guide</a>.</p>
     * @public
     */
    targetModelTags?: Tag[] | undefined;
    /**
     * <p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href="https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html">Ensuring idempotency</a>.</p>
     * @public
     */
    clientRequestToken?: string | undefined;
}
/**
 * @public
 */
export interface CreateModelCopyJobResponse {
    /**
     * <p>The Amazon Resource Name (ARN) of the model copy job.</p>
     * @public
     */
    jobArn: string | undefined;
}
/**
 * @public
 */
export interface GetModelCopyJobRequest {
    /**
     * <p>The Amazon Resource Name (ARN) of the model copy job.</p>
     * @public
     */
    jobArn: string | undefined;
}
/**
 * @public
 * @enum
 */
export declare const ModelCopyJobStatus: {
    readonly COMPLETED: "Completed";
    readonly FAILED: "Failed";
    readonly IN_PROGRESS: "InProgress";
};
/**
 * @public
 */
export type ModelCopyJobStatus = (typeof ModelCopyJobStatus)[keyof typeof ModelCopyJobStatus];
/**
 * @public
 */
export interface GetModelCopyJobResponse {
    /**
     * <p>The Amazon Resource Name (ARN) of the model copy job.</p>
     * @public
     */
    jobArn: string | undefined;
    /**
     * <p>The status of the model copy job.</p>
     * @public
     */
    status: ModelCopyJobStatus | undefined;
    /**
     * <p>The time at which the model copy job was created.</p>
     * @public
     */
    creationTime: Date | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the copied model.</p>
     * @public
     */
    targetModelArn: string | undefined;
    /**
     * <p>The name of the copied model.</p>
     * @public
     */
    targetModelName?: string | undefined;
    /**
     * <p>The unique identifier of the account that the model being copied originated from.</p>
     * @public
     */
    sourceAccountId: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the original model being copied.</p>
     * @public
     */
    sourceModelArn: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the KMS key encrypting the copied model.</p>
     * @public
     */
    targetModelKmsKeyArn?: string | undefined;
    /**
     * <p>The tags associated with the copied model.</p>
     * @public
     */
    targetModelTags?: Tag[] | undefined;
    /**
     * <p>An error message for why the model copy job failed.</p>
     * @public
     */
    failureMessage?: string | undefined;
    /**
     * <p>The name of the original model being copied.</p>
     * @public
     */
    sourceModelName?: string | undefined;
}
/**
 * @public
 */
export interface ListModelCopyJobsRequest {
    /**
     * <p>Filters for model copy jobs created after the specified time.</p>
     * @public
     */
    creationTimeAfter?: Date | undefined;
    /**
     * <p>Filters for model copy jobs created before the specified time. </p>
     * @public
     */
    creationTimeBefore?: Date | undefined;
    /**
     * <p>Filters for model copy jobs whose status matches the value that you specify.</p>
     * @public
     */
    statusEquals?: ModelCopyJobStatus | undefined;
    /**
     * <p>Filters for model copy jobs in which the account that the source model belongs to is equal to the value that you specify.</p>
     * @public
     */
    sourceAccountEquals?: string | undefined;
    /**
     * <p>Filters for model copy jobs in which the Amazon Resource Name (ARN) of the source model to is equal to the value that you specify.</p>
     * @public
     */
    sourceModelArnEquals?: string | undefined;
    /**
     * <p>Filters for model copy jobs in which the name of the copied model contains the string that you specify.</p>
     * @public
     */
    targetModelNameContains?: string | undefined;
    /**
     * <p>The maximum number of results to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>
     * @public
     */
    maxResults?: number | undefined;
    /**
     * <p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results.</p>
     * @public
     */
    nextToken?: string | undefined;
    /**
     * <p>The field to sort by in the returned list of model copy jobs.</p>
     * @public
     */
    sortBy?: SortJobsBy | undefined;
    /**
     * <p>Specifies whether to sort the results in ascending or descending order.</p>
     * @public
     */
    sortOrder?: SortOrder | undefined;
}
/**
 * <p>Contains details about each model copy job.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href="https://docs.aws.amazon.com/bedrock/latest/APIReference/API_ListModelCopyJobs.html#API_ListModelCopyJobs_ResponseSyntax">ListModelCopyJobs response</a> </p> </li> </ul>
 * @public
 */
export interface ModelCopyJobSummary {
    /**
     * <p>The Amazon Resoource Name (ARN) of the model copy job.</p>
     * @public
     */
    jobArn: string | undefined;
    /**
     * <p>The status of the model copy job.</p>
     * @public
     */
    status: ModelCopyJobStatus | undefined;
    /**
     * <p>The time that the model copy job was created.</p>
     * @public
     */
    creationTime: Date | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the copied model.</p>
     * @public
     */
    targetModelArn: string | undefined;
    /**
     * <p>The name of the copied model.</p>
     * @public
     */
    targetModelName?: string | undefined;
    /**
     * <p>The unique identifier of the account that the model being copied originated from.</p>
     * @public
     */
    sourceAccountId: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the original model being copied.</p>
     * @public
     */
    sourceModelArn: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the KMS key used to encrypt the copied model.</p>
     * @public
     */
    targetModelKmsKeyArn?: string | undefined;
    /**
     * <p>Tags associated with the copied model.</p>
     * @public
     */
    targetModelTags?: Tag[] | undefined;
    /**
     * <p>If a model fails to be copied, a message describing why the job failed is included here.</p>
     * @public
     */
    failureMessage?: string | undefined;
    /**
     * <p>The name of the original model being copied.</p>
     * @public
     */
    sourceModelName?: string | undefined;
}
/**
 * @public
 */
export interface ListModelCopyJobsResponse {
    /**
     * <p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, use this token when making another request in the <code>nextToken</code> field to return the next batch of results.</p>
     * @public
     */
    nextToken?: string | undefined;
    /**
     * <p>A list of information about each model copy job.</p>
     * @public
     */
    modelCopyJobSummaries?: ModelCopyJobSummary[] | undefined;
}
/**
 * @public
 */
export interface CreateModelImportJobRequest {
    /**
     * <p>The name of the import job.</p>
     * @public
     */
    jobName: string | undefined;
    /**
     * <p>The name of the imported model.</p>
     * @public
     */
    importedModelName: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the model import job.</p>
     * @public
     */
    roleArn: string | undefined;
    /**
     * <p>The data source for the imported model.</p>
     * @public
     */
    modelDataSource: ModelDataSource | undefined;
    /**
     * <p>Tags to attach to this import job. </p>
     * @public
     */
    jobTags?: Tag[] | undefined;
    /**
     * <p>Tags to attach to the imported model.</p>
     * @public
     */
    importedModelTags?: Tag[] | undefined;
    /**
     * <p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href="https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html">Ensuring idempotency</a>.</p>
     * @public
     */
    clientRequestToken?: string | undefined;
    /**
     * <p>VPC configuration parameters for the private Virtual Private Cloud (VPC) that contains the resources you are using for the import job.</p>
     * @public
     */
    vpcConfig?: VpcConfig | undefined;
    /**
     * <p>The imported model is encrypted at rest using this key.</p>
     * @public
     */
    importedModelKmsKeyId?: string | undefined;
}
/**
 * @public
 */
export interface CreateModelImportJobResponse {
    /**
     * <p>The Amazon Resource Name (ARN) of the model import job.</p>
     * @public
     */
    jobArn: string | undefined;
}
/**
 * @public
 */
export interface DeleteImportedModelRequest {
    /**
     * <p>Name of the imported model to delete.</p>
     * @public
     */
    modelIdentifier: string | undefined;
}
/**
 * @public
 */
export interface DeleteImportedModelResponse {
}
/**
 * @public
 */
export interface GetImportedModelRequest {
    /**
     * <p>Name or Amazon Resource Name (ARN) of the imported model.</p>
     * @public
     */
    modelIdentifier: string | undefined;
}
/**
 * <p>A <code>CustomModelUnit</code> (CMU) is an abstract view of the hardware utilization that Amazon Bedrock needs to host a single copy of your custom model. A model copy represents a single instance of your imported model that is ready to serve inference requests. Amazon Bedrock determines the number of custom model units that a model copy needs when you import the custom model. </p> <p>You can use <code>CustomModelUnits</code> to estimate the cost of running your custom model. For more information, see Calculate the cost of running a custom model in the Amazon Bedrock user guide. </p>
 * @public
 */
export interface CustomModelUnits {
    /**
     * <p>The number of custom model units used to host a model copy. </p>
     * @public
     */
    customModelUnitsPerModelCopy?: number | undefined;
    /**
     * <p>The version of the custom model unit. Use to determine the billing rate for the custom model unit.</p>
     * @public
     */
    customModelUnitsVersion?: string | undefined;
}
/**
 * @public
 */
export interface GetImportedModelResponse {
    /**
     * <p>The Amazon Resource Name (ARN) associated with this imported model.</p>
     * @public
     */
    modelArn?: string | undefined;
    /**
     * <p>The name of the imported model.</p>
     * @public
     */
    modelName?: string | undefined;
    /**
     * <p>Job name associated with the imported model.</p>
     * @public
     */
    jobName?: string | undefined;
    /**
     * <p>Job Amazon Resource Name (ARN) associated with the imported model.</p>
     * @public
     */
    jobArn?: string | undefined;
    /**
     * <p>The data source for this imported model.</p>
     * @public
     */
    modelDataSource?: ModelDataSource | undefined;
    /**
     * <p>Creation time of the imported model.</p>
     * @public
     */
    creationTime?: Date | undefined;
    /**
     * <p>The architecture of the imported model.</p>
     * @public
     */
    modelArchitecture?: string | undefined;
    /**
     * <p>The imported model is encrypted at rest using this key.</p>
     * @public
     */
    modelKmsKeyArn?: string | undefined;
    /**
     * <p>Specifies if the imported model supports converse.</p>
     * @public
     */
    instructSupported?: boolean | undefined;
    /**
     * <p>Information about the hardware utilization for a single copy of the model.</p>
     * @public
     */
    customModelUnits?: CustomModelUnits | undefined;
}
/**
 * @public
 */
export interface GetModelImportJobRequest {
    /**
     * <p>The identifier of the import job.</p>
     * @public
     */
    jobIdentifier: string | undefined;
}
/**
 * @public
 * @enum
 */
export declare const ModelImportJobStatus: {
    readonly COMPLETED: "Completed";
    readonly FAILED: "Failed";
    readonly IN_PROGRESS: "InProgress";
};
/**
 * @public
 */
export type ModelImportJobStatus = (typeof ModelImportJobStatus)[keyof typeof ModelImportJobStatus];
/**
 * @public
 */
export interface GetModelImportJobResponse {
    /**
     * <p>The Amazon Resource Name (ARN) of the import job.</p>
     * @public
     */
    jobArn?: string | undefined;
    /**
     * <p>The name of the import job.</p>
     * @public
     */
    jobName?: string | undefined;
    /**
     * <p>The name of the imported model.</p>
     * @public
     */
    importedModelName?: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the imported model.</p>
     * @public
     */
    importedModelArn?: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the IAM role associated with this job.</p>
     * @public
     */
    roleArn?: string | undefined;
    /**
     * <p>The data source for the imported model.</p>
     * @public
     */
    modelDataSource?: ModelDataSource | undefined;
    /**
     * <p>The status of the job. A successful job transitions from in-progress to completed when the imported model is ready to use. If the job failed, the failure message contains information about why the job failed.</p>
     * @public
     */
    status?: ModelImportJobStatus | undefined;
    /**
     * <p>Information about why the import job failed.</p>
     * @public
     */
    failureMessage?: string | undefined;
    /**
     * <p>The time the resource was created.</p>
     * @public
     */
    creationTime?: Date | undefined;
    /**
     * <p>Time the resource was last modified.</p>
     * @public
     */
    lastModifiedTime?: Date | undefined;
    /**
     * <p>Time that the resource transitioned to terminal state.</p>
     * @public
     */
    endTime?: Date | undefined;
    /**
     * <p>The Virtual Private Cloud (VPC) configuration of the import model job.</p>
     * @public
     */
    vpcConfig?: VpcConfig | undefined;
    /**
     * <p>The imported model is encrypted at rest using this key.</p>
     * @public
     */
    importedModelKmsKeyArn?: string | undefined;
}
/**
 * @public
 */
export interface ListImportedModelsRequest {
    /**
     * <p>Return imported models that created before the specified time.</p>
     * @public
     */
    creationTimeBefore?: Date | undefined;
    /**
     * <p>Return imported models that were created after the specified time.</p>
     * @public
     */
    creationTimeAfter?: Date | undefined;
    /**
     * <p>Return imported models only if the model name contains these characters.</p>
     * @public
     */
    nameContains?: string | undefined;
    /**
     * <p>The maximum number of results to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>
     * @public
     */
    maxResults?: number | undefined;
    /**
     * <p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results.</p>
     * @public
     */
    nextToken?: string | undefined;
    /**
     * <p>The field to sort by in the returned list of imported models.</p>
     * @public
     */
    sortBy?: SortModelsBy | undefined;
    /**
     * <p>Specifies whetehr to sort the results in ascending or descending order.</p>
     * @public
     */
    sortOrder?: SortOrder | undefined;
}
/**
 * <p>Information about the imported model.</p>
 * @public
 */
export interface ImportedModelSummary {
    /**
     * <p>The Amazon Resource Name (ARN) of the imported model.</p>
     * @public
     */
    modelArn: string | undefined;
    /**
     * <p>Name of the imported model.</p>
     * @public
     */
    modelName: string | undefined;
    /**
     * <p>Creation time of the imported model.</p>
     * @public
     */
    creationTime: Date | undefined;
    /**
     * <p>Specifies if the imported model supports converse.</p>
     * @public
     */
    instructSupported?: boolean | undefined;
    /**
     * <p>The architecture of the imported model.</p>
     * @public
     */
    modelArchitecture?: string | undefined;
}
/**
 * @public
 */
export interface ListImportedModelsResponse {
    /**
     * <p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, use this token when making another request in the <code>nextToken</code> field to return the next batch of results.</p>
     * @public
     */
    nextToken?: string | undefined;
    /**
     * <p>Model summaries.</p>
     * @public
     */
    modelSummaries?: ImportedModelSummary[] | undefined;
}
/**
 * @public
 */
export interface ListModelImportJobsRequest {
    /**
     * <p>Return import jobs that were created after the specified time.</p>
     * @public
     */
    creationTimeAfter?: Date | undefined;
    /**
     * <p>Return import jobs that were created before the specified time.</p>
     * @public
     */
    creationTimeBefore?: Date | undefined;
    /**
     * <p>Return imported jobs with the specified status.</p>
     * @public
     */
    statusEquals?: ModelImportJobStatus | undefined;
    /**
     * <p>Return imported jobs only if the job name contains these characters.</p>
     * @public
     */
    nameContains?: string | undefined;
    /**
     * <p>The maximum number of results to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>
     * @public
     */
    maxResults?: number | undefined;
    /**
     * <p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results.</p>
     * @public
     */
    nextToken?: string | undefined;
    /**
     * <p>The field to sort by in the returned list of imported jobs.</p>
     * @public
     */
    sortBy?: SortJobsBy | undefined;
    /**
     * <p>Specifies whether to sort the results in ascending or descending order.</p>
     * @public
     */
    sortOrder?: SortOrder | undefined;
}
/**
 * <p>Information about the import job.</p>
 * @public
 */
export interface ModelImportJobSummary {
    /**
     * <p>The Amazon Resource Name (ARN) of the import job.</p>
     * @public
     */
    jobArn: string | undefined;
    /**
     * <p>The name of the import job.</p>
     * @public
     */
    jobName: string | undefined;
    /**
     * <p>The status of the imported job. </p>
     * @public
     */
    status: ModelImportJobStatus | undefined;
    /**
     * <p>The time when the import job was last modified.</p>
     * @public
     */
    lastModifiedTime?: Date | undefined;
    /**
     * <p>The time import job was created.</p>
     * @public
     */
    creationTime: Date | undefined;
    /**
     * <p>The time when import job ended.</p>
     * @public
     */
    endTime?: Date | undefined;
    /**
     * <p>The Amazon resource Name (ARN) of the imported model.</p>
     * @public
     */
    importedModelArn?: string | undefined;
    /**
     * <p>The name of the imported model.</p>
     * @public
     */
    importedModelName?: string | undefined;
}
/**
 * @public
 */
export interface ListModelImportJobsResponse {
    /**
     * <p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results.</p>
     * @public
     */
    nextToken?: string | undefined;
    /**
     * <p>Import job summaries.</p>
     * @public
     */
    modelImportJobSummaries?: ModelImportJobSummary[] | undefined;
}
/**
 * @public
 * @enum
 */
export declare const S3InputFormat: {
    readonly JSONL: "JSONL";
};
/**
 * @public
 */
export type S3InputFormat = (typeof S3InputFormat)[keyof typeof S3InputFormat];
/**
 * <p>Contains the configuration of the S3 location of the input data.</p>
 * @public
 */
export interface ModelInvocationJobS3InputDataConfig {
    /**
     * <p>The format of the input data.</p>
     * @public
     */
    s3InputFormat?: S3InputFormat | undefined;
    /**
     * <p>The S3 location of the input data.</p>
     * @public
     */
    s3Uri: string | undefined;
    /**
     * <p>The ID of the Amazon Web Services account that owns the S3 bucket containing the input data.</p>
     * @public
     */
    s3BucketOwner?: string | undefined;
}
/**
 * <p>Details about the location of the input to the batch inference job.</p>
 * @public
 */
export type ModelInvocationJobInputDataConfig = ModelInvocationJobInputDataConfig.S3InputDataConfigMember | ModelInvocationJobInputDataConfig.$UnknownMember;
/**
 * @public
 */
export declare namespace ModelInvocationJobInputDataConfig {
    /**
     * <p>Contains the configuration of the S3 location of the input data.</p>
     * @public
     */
    interface S3InputDataConfigMember {
        s3InputDataConfig: ModelInvocationJobS3InputDataConfig;
        $unknown?: never;
    }
    /**
     * @public
     */
    interface $UnknownMember {
        s3InputDataConfig?: never;
        $unknown: [string, any];
    }
    interface Visitor<T> {
        s3InputDataConfig: (value: ModelInvocationJobS3InputDataConfig) => T;
        _: (name: string, value: any) => T;
    }
    const visit: <T>(value: ModelInvocationJobInputDataConfig, visitor: Visitor<T>) => T;
}
/**
 * <p>Contains the configuration of the S3 location of the output data.</p>
 * @public
 */
export interface ModelInvocationJobS3OutputDataConfig {
    /**
     * <p>The S3 location of the output data.</p>
     * @public
     */
    s3Uri: string | undefined;
    /**
     * <p>The unique identifier of the key that encrypts the S3 location of the output data.</p>
     * @public
     */
    s3EncryptionKeyId?: string | undefined;
    /**
     * <p>The ID of the Amazon Web Services account that owns the S3 bucket containing the output data.</p>
     * @public
     */
    s3BucketOwner?: string | undefined;
}
/**
 * <p>Contains the configuration of the S3 location of the output data.</p>
 * @public
 */
export type ModelInvocationJobOutputDataConfig = ModelInvocationJobOutputDataConfig.S3OutputDataConfigMember | ModelInvocationJobOutputDataConfig.$UnknownMember;
/**
 * @public
 */
export declare namespace ModelInvocationJobOutputDataConfig {
    /**
     * <p>Contains the configuration of the S3 location of the output data.</p>
     * @public
     */
    interface S3OutputDataConfigMember {
        s3OutputDataConfig: ModelInvocationJobS3OutputDataConfig;
        $unknown?: never;
    }
    /**
     * @public
     */
    interface $UnknownMember {
        s3OutputDataConfig?: never;
        $unknown: [string, any];
    }
    interface Visitor<T> {
        s3OutputDataConfig: (value: ModelInvocationJobS3OutputDataConfig) => T;
        _: (name: string, value: any) => T;
    }
    const visit: <T>(value: ModelInvocationJobOutputDataConfig, visitor: Visitor<T>) => T;
}
/**
 * @public
 */
export interface CreateModelInvocationJobRequest {
    /**
     * <p>A name to give the batch inference job.</p>
     * @public
     */
    jobName: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the service role with permissions to carry out and manage batch inference. You can use the console to create a default service role or follow the steps at <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/batch-iam-sr.html">Create a service role for batch inference</a>.</p>
     * @public
     */
    roleArn: string | undefined;
    /**
     * <p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href="https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html">Ensuring idempotency</a>.</p>
     * @public
     */
    clientRequestToken?: string | undefined;
    /**
     * <p>The unique identifier of the foundation model to use for the batch inference job.</p>
     * @public
     */
    modelId: string | undefined;
    /**
     * <p>Details about the location of the input to the batch inference job.</p>
     * @public
     */
    inputDataConfig: ModelInvocationJobInputDataConfig | undefined;
    /**
     * <p>Details about the location of the output of the batch inference job.</p>
     * @public
     */
    outputDataConfig: ModelInvocationJobOutputDataConfig | undefined;
    /**
     * <p>The configuration of the Virtual Private Cloud (VPC) for the data in the batch inference job. For more information, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/batch-vpc">Protect batch inference jobs using a VPC</a>.</p>
     * @public
     */
    vpcConfig?: VpcConfig | undefined;
    /**
     * <p>The number of hours after which to force the batch inference job to time out.</p>
     * @public
     */
    timeoutDurationInHours?: number | undefined;
    /**
     * <p>Any tags to associate with the batch inference job. For more information, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/tagging.html">Tagging Amazon Bedrock resources</a>.</p>
     * @public
     */
    tags?: Tag[] | undefined;
}
/**
 * @public
 */
export interface CreateModelInvocationJobResponse {
    /**
     * <p>The Amazon Resource Name (ARN) of the batch inference job.</p>
     * @public
     */
    jobArn: string | undefined;
}
/**
 * @public
 */
export interface GetModelInvocationJobRequest {
    /**
     * <p>The Amazon Resource Name (ARN) of the batch inference job.</p>
     * @public
     */
    jobIdentifier: string | undefined;
}
/**
 * @public
 * @enum
 */
export declare const ModelInvocationJobStatus: {
    readonly COMPLETED: "Completed";
    readonly EXPIRED: "Expired";
    readonly FAILED: "Failed";
    readonly IN_PROGRESS: "InProgress";
    readonly PARTIALLY_COMPLETED: "PartiallyCompleted";
    readonly SCHEDULED: "Scheduled";
    readonly STOPPED: "Stopped";
    readonly STOPPING: "Stopping";
    readonly SUBMITTED: "Submitted";
    readonly VALIDATING: "Validating";
};
/**
 * @public
 */
export type ModelInvocationJobStatus = (typeof ModelInvocationJobStatus)[keyof typeof ModelInvocationJobStatus];
/**
 * @public
 */
export interface GetModelInvocationJobResponse {
    /**
     * <p>The Amazon Resource Name (ARN) of the batch inference job.</p>
     * @public
     */
    jobArn: string | undefined;
    /**
     * <p>The name of the batch inference job.</p>
     * @public
     */
    jobName?: string | undefined;
    /**
     * <p>The unique identifier of the foundation model used for model inference.</p>
     * @public
     */
    modelId: string | undefined;
    /**
     * <p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href="https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html">Ensuring idempotency</a>.</p>
     * @public
     */
    clientRequestToken?: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the service role with permissions to carry out and manage batch inference. You can use the console to create a default service role or follow the steps at <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/batch-iam-sr.html">Create a service role for batch inference</a>.</p>
     * @public
     */
    roleArn: string | undefined;
    /**
     * <p>The status of the batch inference job.</p> <p>The following statuses are possible:</p> <ul> <li> <p>Submitted – This job has been submitted to a queue for validation.</p> </li> <li> <p>Validating – This job is being validated for the requirements described in <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/batch-inference-data.html">Format and upload your batch inference data</a>. The criteria include the following:</p> <ul> <li> <p>Your IAM service role has access to the Amazon S3 buckets containing your files.</p> </li> <li> <p>Your files are .jsonl files and each individual record is a JSON object in the correct format. Note that validation doesn't check if the <code>modelInput</code> value matches the request body for the model.</p> </li> <li> <p>Your files fulfill the requirements for file size and number of records. For more information, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/quotas.html">Quotas for Amazon Bedrock</a>.</p> </li> </ul> </li> <li> <p>Scheduled – This job has been validated and is now in a queue. The job will automatically start when it reaches its turn.</p> </li> <li> <p>Expired – This job timed out because it was scheduled but didn't begin before the set timeout duration. Submit a new job request.</p> </li> <li> <p>InProgress – This job has begun. You can start viewing the results in the output S3 location.</p> </li> <li> <p>Completed – This job has successfully completed. View the output files in the output S3 location.</p> </li> <li> <p>PartiallyCompleted – This job has partially completed. Not all of your records could be processed in time. View the output files in the output S3 location.</p> </li> <li> <p>Failed – This job has failed. Check the failure message for any further details. For further assistance, reach out to the <a href="https://console.aws.amazon.com/support/home/">Amazon Web ServicesSupport Center</a>.</p> </li> <li> <p>Stopped – This job was stopped by a user.</p> </li> <li> <p>Stopping – This job is being stopped by a user.</p> </li> </ul>
     * @public
     */
    status?: ModelInvocationJobStatus | undefined;
    /**
     * <p>If the batch inference job failed, this field contains a message describing why the job failed.</p>
     * @public
     */
    message?: string | undefined;
    /**
     * <p>The time at which the batch inference job was submitted.</p>
     * @public
     */
    submitTime: Date | undefined;
    /**
     * <p>The time at which the batch inference job was last modified.</p>
     * @public
     */
    lastModifiedTime?: Date | undefined;
    /**
     * <p>The time at which the batch inference job ended.</p>
     * @public
     */
    endTime?: Date | undefined;
    /**
     * <p>Details about the location of the input to the batch inference job.</p>
     * @public
     */
    inputDataConfig: ModelInvocationJobInputDataConfig | undefined;
    /**
     * <p>Details about the location of the output of the batch inference job.</p>
     * @public
     */
    outputDataConfig: ModelInvocationJobOutputDataConfig | undefined;
    /**
     * <p>The configuration of the Virtual Private Cloud (VPC) for the data in the batch inference job. For more information, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/batch-vpc">Protect batch inference jobs using a VPC</a>.</p>
     * @public
     */
    vpcConfig?: VpcConfig | undefined;
    /**
     * <p>The number of hours after which batch inference job was set to time out.</p>
     * @public
     */
    timeoutDurationInHours?: number | undefined;
    /**
     * <p>The time at which the batch inference job times or timed out.</p>
     * @public
     */
    jobExpirationTime?: Date | undefined;
}
/**
 * @public
 */
export interface ListModelInvocationJobsRequest {
    /**
     * <p>Specify a time to filter for batch inference jobs that were submitted after the time you specify.</p>
     * @public
     */
    submitTimeAfter?: Date | undefined;
    /**
     * <p>Specify a time to filter for batch inference jobs that were submitted before the time you specify.</p>
     * @public
     */
    submitTimeBefore?: Date | undefined;
    /**
     * <p>Specify a status to filter for batch inference jobs whose statuses match the string you specify.</p> <p>The following statuses are possible:</p> <ul> <li> <p>Submitted – This job has been submitted to a queue for validation.</p> </li> <li> <p>Validating – This job is being validated for the requirements described in <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/batch-inference-data.html">Format and upload your batch inference data</a>. The criteria include the following:</p> <ul> <li> <p>Your IAM service role has access to the Amazon S3 buckets containing your files.</p> </li> <li> <p>Your files are .jsonl files and each individual record is a JSON object in the correct format. Note that validation doesn't check if the <code>modelInput</code> value matches the request body for the model.</p> </li> <li> <p>Your files fulfill the requirements for file size and number of records. For more information, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/quotas.html">Quotas for Amazon Bedrock</a>.</p> </li> </ul> </li> <li> <p>Scheduled – This job has been validated and is now in a queue. The job will automatically start when it reaches its turn.</p> </li> <li> <p>Expired – This job timed out because it was scheduled but didn't begin before the set timeout duration. Submit a new job request.</p> </li> <li> <p>InProgress – This job has begun. You can start viewing the results in the output S3 location.</p> </li> <li> <p>Completed – This job has successfully completed. View the output files in the output S3 location.</p> </li> <li> <p>PartiallyCompleted – This job has partially completed. Not all of your records could be processed in time. View the output files in the output S3 location.</p> </li> <li> <p>Failed – This job has failed. Check the failure message for any further details. For further assistance, reach out to the <a href="https://console.aws.amazon.com/support/home/">Amazon Web ServicesSupport Center</a>.</p> </li> <li> <p>Stopped – This job was stopped by a user.</p> </li> <li> <p>Stopping – This job is being stopped by a user.</p> </li> </ul>
     * @public
     */
    statusEquals?: ModelInvocationJobStatus | undefined;
    /**
     * <p>Specify a string to filter for batch inference jobs whose names contain the string.</p>
     * @public
     */
    nameContains?: string | undefined;
    /**
     * <p>The maximum number of results to return. If there are more results than the number that you specify, a <code>nextToken</code> value is returned. Use the <code>nextToken</code> in a request to return the next batch of results.</p>
     * @public
     */
    maxResults?: number | undefined;
    /**
     * <p>If there were more results than the value you specified in the <code>maxResults</code> field in a previous <code>ListModelInvocationJobs</code> request, the response would have returned a <code>nextToken</code> value. To see the next batch of results, send the <code>nextToken</code> value in another request.</p>
     * @public
     */
    nextToken?: string | undefined;
    /**
     * <p>An attribute by which to sort the results.</p>
     * @public
     */
    sortBy?: SortJobsBy | undefined;
    /**
     * <p>Specifies whether to sort the results by ascending or descending order.</p>
     * @public
     */
    sortOrder?: SortOrder | undefined;
}
/**
 * <p>A summary of a batch inference job.</p>
 * @public
 */
export interface ModelInvocationJobSummary {
    /**
     * <p>The Amazon Resource Name (ARN) of the batch inference job.</p>
     * @public
     */
    jobArn: string | undefined;
    /**
     * <p>The name of the batch inference job.</p>
     * @public
     */
    jobName: string | undefined;
    /**
     * <p>The unique identifier of the foundation model used for model inference.</p>
     * @public
     */
    modelId: string | undefined;
    /**
     * <p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href="https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html">Ensuring idempotency</a>.</p>
     * @public
     */
    clientRequestToken?: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the service role with permissions to carry out and manage batch inference. You can use the console to create a default service role or follow the steps at <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/batch-iam-sr.html">Create a service role for batch inference</a>.</p>
     * @public
     */
    roleArn: string | undefined;
    /**
     * <p>The status of the batch inference job.</p> <p>The following statuses are possible:</p> <ul> <li> <p>Submitted – This job has been submitted to a queue for validation.</p> </li> <li> <p>Validating – This job is being validated for the requirements described in <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/batch-inference-data.html">Format and upload your batch inference data</a>. The criteria include the following:</p> <ul> <li> <p>Your IAM service role has access to the Amazon S3 buckets containing your files.</p> </li> <li> <p>Your files are .jsonl files and each individual record is a JSON object in the correct format. Note that validation doesn't check if the <code>modelInput</code> value matches the request body for the model.</p> </li> <li> <p>Your files fulfill the requirements for file size and number of records. For more information, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/quotas.html">Quotas for Amazon Bedrock</a>.</p> </li> </ul> </li> <li> <p>Scheduled – This job has been validated and is now in a queue. The job will automatically start when it reaches its turn.</p> </li> <li> <p>Expired – This job timed out because it was scheduled but didn't begin before the set timeout duration. Submit a new job request.</p> </li> <li> <p>InProgress – This job has begun. You can start viewing the results in the output S3 location.</p> </li> <li> <p>Completed – This job has successfully completed. View the output files in the output S3 location.</p> </li> <li> <p>PartiallyCompleted – This job has partially completed. Not all of your records could be processed in time. View the output files in the output S3 location.</p> </li> <li> <p>Failed – This job has failed. Check the failure message for any further details. For further assistance, reach out to the <a href="https://console.aws.amazon.com/support/home/">Amazon Web ServicesSupport Center</a>.</p> </li> <li> <p>Stopped – This job was stopped by a user.</p> </li> <li> <p>Stopping – This job is being stopped by a user.</p> </li> </ul>
     * @public
     */
    status?: ModelInvocationJobStatus | undefined;
    /**
     * <p>If the batch inference job failed, this field contains a message describing why the job failed.</p>
     * @public
     */
    message?: string | undefined;
    /**
     * <p>The time at which the batch inference job was submitted.</p>
     * @public
     */
    submitTime: Date | undefined;
    /**
     * <p>The time at which the batch inference job was last modified.</p>
     * @public
     */
    lastModifiedTime?: Date | undefined;
    /**
     * <p>The time at which the batch inference job ended.</p>
     * @public
     */
    endTime?: Date | undefined;
    /**
     * <p>Details about the location of the input to the batch inference job.</p>
     * @public
     */
    inputDataConfig: ModelInvocationJobInputDataConfig | undefined;
    /**
     * <p>Details about the location of the output of the batch inference job.</p>
     * @public
     */
    outputDataConfig: ModelInvocationJobOutputDataConfig | undefined;
    /**
     * <p>The configuration of the Virtual Private Cloud (VPC) for the data in the batch inference job. For more information, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/batch-vpc">Protect batch inference jobs using a VPC</a>.</p>
     * @public
     */
    vpcConfig?: VpcConfig | undefined;
    /**
     * <p>The number of hours after which the batch inference job was set to time out.</p>
     * @public
     */
    timeoutDurationInHours?: number | undefined;
    /**
     * <p>The time at which the batch inference job times or timed out.</p>
     * @public
     */
    jobExpirationTime?: Date | undefined;
}
/**
 * @public
 */
export interface ListModelInvocationJobsResponse {
    /**
     * <p>If there are more results than can fit in the response, a <code>nextToken</code> is returned. Use the <code>nextToken</code> in a request to return the next batch of results.</p>
     * @public
     */
    nextToken?: string | undefined;
    /**
     * <p>A list of items, each of which contains a summary about a batch inference job.</p>
     * @public
     */
    invocationJobSummaries?: ModelInvocationJobSummary[] | undefined;
}
/**
 * @public
 */
export interface StopModelInvocationJobRequest {
    /**
     * <p>The Amazon Resource Name (ARN) of the batch inference job to stop.</p>
     * @public
     */
    jobIdentifier: string | undefined;
}
/**
 * @public
 */
export interface StopModelInvocationJobResponse {
}
/**
 * @public
 */
export interface GetFoundationModelRequest {
    /**
     * <p>The model identifier. </p>
     * @public
     */
    modelIdentifier: string | undefined;
}
/**
 * @public
 * @enum
 */
export declare const ModelCustomization: {
    readonly CONTINUED_PRE_TRAINING: "CONTINUED_PRE_TRAINING";
    readonly DISTILLATION: "DISTILLATION";
    readonly FINE_TUNING: "FINE_TUNING";
};
/**
 * @public
 */
export type ModelCustomization = (typeof ModelCustomization)[keyof typeof ModelCustomization];
/**
 * @public
 * @enum
 */
export declare const InferenceType: {
    readonly ON_DEMAND: "ON_DEMAND";
    readonly PROVISIONED: "PROVISIONED";
};
/**
 * @public
 */
export type InferenceType = (typeof InferenceType)[keyof typeof InferenceType];
/**
 * @public
 * @enum
 */
export declare const ModelModality: {
    readonly EMBEDDING: "EMBEDDING";
    readonly IMAGE: "IMAGE";
    readonly TEXT: "TEXT";
};
/**
 * @public
 */
export type ModelModality = (typeof ModelModality)[keyof typeof ModelModality];
/**
 * @public
 * @enum
 */
export declare const FoundationModelLifecycleStatus: {
    readonly ACTIVE: "ACTIVE";
    readonly LEGACY: "LEGACY";
};
/**
 * @public
 */
export type FoundationModelLifecycleStatus = (typeof FoundationModelLifecycleStatus)[keyof typeof FoundationModelLifecycleStatus];
/**
 * <p>Details about whether a model version is available or deprecated.</p>
 * @public
 */
export interface FoundationModelLifecycle {
    /**
     * <p>Specifies whether a model version is available (<code>ACTIVE</code>) or deprecated (<code>LEGACY</code>.</p>
     * @public
     */
    status: FoundationModelLifecycleStatus | undefined;
}
/**
 * <p>Information about a foundation model.</p>
 * @public
 */
export interface FoundationModelDetails {
    /**
     * <p>The model Amazon Resource Name (ARN).</p>
     * @public
     */
    modelArn: string | undefined;
    /**
     * <p>The model identifier.</p>
     * @public
     */
    modelId: string | undefined;
    /**
     * <p>The model name.</p>
     * @public
     */
    modelName?: string | undefined;
    /**
     * <p>The model's provider name.</p>
     * @public
     */
    providerName?: string | undefined;
    /**
     * <p>The input modalities that the model supports.</p>
     * @public
     */
    inputModalities?: ModelModality[] | undefined;
    /**
     * <p>The output modalities that the model supports.</p>
     * @public
     */
    outputModalities?: ModelModality[] | undefined;
    /**
     * <p>Indicates whether the model supports streaming.</p>
     * @public
     */
    responseStreamingSupported?: boolean | undefined;
    /**
     * <p>The customization that the model supports.</p>
     * @public
     */
    customizationsSupported?: ModelCustomization[] | undefined;
    /**
     * <p>The inference types that the model supports.</p>
     * @public
     */
    inferenceTypesSupported?: InferenceType[] | undefined;
    /**
     * <p>Contains details about whether a model version is available or deprecated</p>
     * @public
     */
    modelLifecycle?: FoundationModelLifecycle | undefined;
}
/**
 * @public
 */
export interface GetFoundationModelResponse {
    /**
     * <p>Information about the foundation model.</p>
     * @public
     */
    modelDetails?: FoundationModelDetails | undefined;
}
/**
 * @public
 */
export interface ListFoundationModelsRequest {
    /**
     * <p>Return models belonging to the model provider that you specify.</p>
     * @public
     */
    byProvider?: string | undefined;
    /**
     * <p>Return models that support the customization type that you specify. For more information, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/custom-models.html">Custom models</a> in the <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/what-is-service.html">Amazon Bedrock User Guide</a>.</p>
     * @public
     */
    byCustomizationType?: ModelCustomization | undefined;
    /**
     * <p>Return models that support the output modality that you specify.</p>
     * @public
     */
    byOutputModality?: ModelModality | undefined;
    /**
     * <p>Return models that support the inference type that you specify. For more information, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/prov-throughput.html">Provisioned Throughput</a> in the <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/what-is-service.html">Amazon Bedrock User Guide</a>.</p>
     * @public
     */
    byInferenceType?: InferenceType | undefined;
}
/**
 * <p>Summary information for a foundation model.</p>
 * @public
 */
export interface FoundationModelSummary {
    /**
     * <p>The Amazon Resource Name (ARN) of the foundation model.</p>
     * @public
     */
    modelArn: string | undefined;
    /**
     * <p>The model ID of the foundation model.</p>
     * @public
     */
    modelId: string | undefined;
    /**
     * <p>The name of the model.</p>
     * @public
     */
    modelName?: string | undefined;
    /**
     * <p>The model's provider name.</p>
     * @public
     */
    providerName?: string | undefined;
    /**
     * <p>The input modalities that the model supports.</p>
     * @public
     */
    inputModalities?: ModelModality[] | undefined;
    /**
     * <p>The output modalities that the model supports.</p>
     * @public
     */
    outputModalities?: ModelModality[] | undefined;
    /**
     * <p>Indicates whether the model supports streaming.</p>
     * @public
     */
    responseStreamingSupported?: boolean | undefined;
    /**
     * <p>Whether the model supports fine-tuning or continual pre-training.</p>
     * @public
     */
    customizationsSupported?: ModelCustomization[] | undefined;
    /**
     * <p>The inference types that the model supports.</p>
     * @public
     */
    inferenceTypesSupported?: InferenceType[] | undefined;
    /**
     * <p>Contains details about whether a model version is available or deprecated.</p>
     * @public
     */
    modelLifecycle?: FoundationModelLifecycle | undefined;
}
/**
 * @public
 */
export interface ListFoundationModelsResponse {
    /**
     * <p>A list of Amazon Bedrock foundation models.</p>
     * @public
     */
    modelSummaries?: FoundationModelSummary[] | undefined;
}
/**
 * <p>The target model for a prompt router.</p>
 * @public
 */
export interface PromptRouterTargetModel {
    /**
     * <p>The target model's ARN.</p>
     * @public
     */
    modelArn: string | undefined;
}
/**
 * <p>Routing criteria for a prompt router.</p>
 * @public
 */
export interface RoutingCriteria {
    /**
     * <p>The criteria's response quality difference.</p>
     * @public
     */
    responseQualityDifference: number | undefined;
}
/**
 * @public
 */
export interface CreatePromptRouterRequest {
    /**
     * <p>A unique, case-sensitive identifier that you provide to ensure idempotency of your requests. If not specified, the Amazon Web Services SDK automatically generates one for you.</p>
     * @public
     */
    clientRequestToken?: string | undefined;
    /**
     * <p>The name of the prompt router. The name must be unique within your Amazon Web Services account in the current region.</p>
     * @public
     */
    promptRouterName: string | undefined;
    /**
     * <p>A list of foundation models that the prompt router can route requests to. At least one model must be specified.</p>
     * @public
     */
    models: PromptRouterTargetModel[] | undefined;
    /**
     * <p>An optional description of the prompt router to help identify its purpose.</p>
     * @public
     */
    description?: string | undefined;
    /**
     * <p>The criteria, which is the response quality difference, used to determine how incoming requests are routed to different models.</p>
     * @public
     */
    routingCriteria: RoutingCriteria | undefined;
    /**
     * <p>The default model to use when the routing criteria is not met.</p>
     * @public
     */
    fallbackModel: PromptRouterTargetModel | undefined;
    /**
     * <p>An array of key-value pairs to apply to this resource as tags. You can use tags to categorize and manage your Amazon Web Services resources.</p>
     * @public
     */
    tags?: Tag[] | undefined;
}
/**
 * @public
 */
export interface CreatePromptRouterResponse {
    /**
     * <p>The Amazon Resource Name (ARN) that uniquely identifies the prompt router.</p>
     * @public
     */
    promptRouterArn?: string | undefined;
}
/**
 * @public
 */
export interface DeletePromptRouterRequest {
    /**
     * <p>The Amazon Resource Name (ARN) of the prompt router to delete.</p>
     * @public
     */
    promptRouterArn: string | undefined;
}
/**
 * @public
 */
export interface DeletePromptRouterResponse {
}
/**
 * @public
 */
export interface GetPromptRouterRequest {
    /**
     * <p>The prompt router's ARN</p>
     * @public
     */
    promptRouterArn: string | undefined;
}
/**
 * @public
 * @enum
 */
export declare const PromptRouterStatus: {
    readonly AVAILABLE: "AVAILABLE";
};
/**
 * @public
 */
export type PromptRouterStatus = (typeof PromptRouterStatus)[keyof typeof PromptRouterStatus];
/**
 * @public
 * @enum
 */
export declare const PromptRouterType: {
    readonly CUSTOM: "custom";
    readonly DEFAULT: "default";
};
/**
 * @public
 */
export type PromptRouterType = (typeof PromptRouterType)[keyof typeof PromptRouterType];
/**
 * @public
 */
export interface GetPromptRouterResponse {
    /**
     * <p>The router's name.</p>
     * @public
     */
    promptRouterName: string | undefined;
    /**
     * <p>The router's routing criteria.</p>
     * @public
     */
    routingCriteria: RoutingCriteria | undefined;
    /**
     * <p>The router's description.</p>
     * @public
     */
    description?: string | undefined;
    /**
     * <p>When the router was created.</p>
     * @public
     */
    createdAt?: Date | undefined;
    /**
     * <p>When the router was updated.</p>
     * @public
     */
    updatedAt?: Date | undefined;
    /**
     * <p>The prompt router's ARN</p>
     * @public
     */
    promptRouterArn: string | undefined;
    /**
     * <p>The router's models.</p>
     * @public
     */
    models: PromptRouterTargetModel[] | undefined;
    /**
     * <p>The router's fallback model.</p>
     * @public
     */
    fallbackModel: PromptRouterTargetModel | undefined;
    /**
     * <p>The router's status.</p>
     * @public
     */
    status: PromptRouterStatus | undefined;
    /**
     * <p>The router's type.</p>
     * @public
     */
    type: PromptRouterType | undefined;
}
/**
 * @public
 */
export interface ListPromptRoutersRequest {
    /**
     * <p>The maximum number of prompt routers to return in one page of results.</p>
     * @public
     */
    maxResults?: number | undefined;
    /**
     * <p>Specify the pagination token from a previous request to retrieve the next page of results.</p>
     * @public
     */
    nextToken?: string | undefined;
    /**
     * <p>The type of the prompt routers, such as whether it's default or custom.</p>
     * @public
     */
    type?: PromptRouterType | undefined;
}
/**
 * <p>Details about a prompt router.</p>
 * @public
 */
export interface PromptRouterSummary {
    /**
     * <p>The router's name.</p>
     * @public
     */
    promptRouterName: string | undefined;
    /**
     * <p>The router's routing criteria.</p>
     * @public
     */
    routingCriteria: RoutingCriteria | undefined;
    /**
     * <p>The router's description.</p>
     * @public
     */
    description?: string | undefined;
    /**
     * <p>When the router was created.</p>
     * @public
     */
    createdAt?: Date | undefined;
    /**
     * <p>When the router was updated.</p>
     * @public
     */
    updatedAt?: Date | undefined;
    /**
     * <p>The router's ARN.</p>
     * @public
     */
    promptRouterArn: string | undefined;
    /**
     * <p>The router's models.</p>
     * @public
     */
    models: PromptRouterTargetModel[] | undefined;
    /**
     * <p>The router's fallback model.</p>
     * @public
     */
    fallbackModel: PromptRouterTargetModel | undefined;
    /**
     * <p>The router's status.</p>
     * @public
     */
    status: PromptRouterStatus | undefined;
    /**
     * <p>The summary's type.</p>
     * @public
     */
    type: PromptRouterType | undefined;
}
/**
 * @public
 */
export interface ListPromptRoutersResponse {
    /**
     * <p>A list of prompt router summaries.</p>
     * @public
     */
    promptRouterSummaries?: PromptRouterSummary[] | undefined;
    /**
     * <p>Specify the pagination token from a previous request to retrieve the next page of results.</p>
     * @public
     */
    nextToken?: string | undefined;
}
/**
 * @public
 * @enum
 */
export declare const CommitmentDuration: {
    readonly ONE_MONTH: "OneMonth";
    readonly SIX_MONTHS: "SixMonths";
};
/**
 * @public
 */
export type CommitmentDuration = (typeof CommitmentDuration)[keyof typeof CommitmentDuration];
/**
 * @public
 */
export interface CreateProvisionedModelThroughputRequest {
    /**
     * <p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href="https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html">Ensuring idempotency</a> in the Amazon S3 User Guide.</p>
     * @public
     */
    clientRequestToken?: string | undefined;
    /**
     * <p>Number of model units to allocate. A model unit delivers a specific throughput level for the specified model. The throughput level of a model unit specifies the total number of input and output tokens that it can process and generate within a span of one minute. By default, your account has no model units for purchasing Provisioned Throughputs with commitment. You must first visit the <a href="https://console.aws.amazon.com/support/home#/case/create?issueType=service-limit-increase">Amazon Web Services support center</a> to request MUs.</p> <p>For model unit quotas, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/quotas.html#prov-thru-quotas">Provisioned Throughput quotas</a> in the <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/what-is-service.html">Amazon Bedrock User Guide</a>.</p> <p>For more information about what an MU specifies, contact your Amazon Web Services account manager.</p>
     * @public
     */
    modelUnits: number | undefined;
    /**
     * <p>The name for this Provisioned Throughput.</p>
     * @public
     */
    provisionedModelName: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) or name of the model to associate with this Provisioned Throughput. For a list of models for which you can purchase Provisioned Throughput, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/model-ids.html#prov-throughput-models">Amazon Bedrock model IDs for purchasing Provisioned Throughput</a> in the <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/what-is-service.html">Amazon Bedrock User Guide</a>.</p>
     * @public
     */
    modelId: string | undefined;
    /**
     * <p>The commitment duration requested for the Provisioned Throughput. Billing occurs hourly and is discounted for longer commitment terms. To request a no-commit Provisioned Throughput, omit this field.</p> <p>Custom models support all levels of commitment. To see which base models support no commitment, see <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/pt-supported.html">Supported regions and models for Provisioned Throughput</a> in the <a href="https://docs.aws.amazon.com/bedrock/latest/userguide/what-is-service.html">Amazon Bedrock User Guide</a> </p>
     * @public
     */
    commitmentDuration?: CommitmentDuration | undefined;
    /**
     * <p>Tags to associate with this Provisioned Throughput.</p>
     * @public
     */
    tags?: Tag[] | undefined;
}
/**
 * @public
 */
export interface CreateProvisionedModelThroughputResponse {
    /**
     * <p>The Amazon Resource Name (ARN) for this Provisioned Throughput.</p>
     * @public
     */
    provisionedModelArn: string | undefined;
}
/**
 * @public
 */
export interface DeleteProvisionedModelThroughputRequest {
    /**
     * <p>The Amazon Resource Name (ARN) or name of the Provisioned Throughput.</p>
     * @public
     */
    provisionedModelId: string | undefined;
}
/**
 * @public
 */
export interface DeleteProvisionedModelThroughputResponse {
}
/**
 * @public
 */
export interface GetProvisionedModelThroughputRequest {
    /**
     * <p>The Amazon Resource Name (ARN) or name of the Provisioned Throughput.</p>
     * @public
     */
    provisionedModelId: string | undefined;
}
/**
 * @public
 * @enum
 */
export declare const ProvisionedModelStatus: {
    readonly CREATING: "Creating";
    readonly FAILED: "Failed";
    readonly IN_SERVICE: "InService";
    readonly UPDATING: "Updating";
};
/**
 * @public
 */
export type ProvisionedModelStatus = (typeof ProvisionedModelStatus)[keyof typeof ProvisionedModelStatus];
/**
 * @public
 */
export interface GetProvisionedModelThroughputResponse {
    /**
     * <p>The number of model units allocated to this Provisioned Throughput.</p>
     * @public
     */
    modelUnits: number | undefined;
    /**
     * <p>The number of model units that was requested for this Provisioned Throughput.</p>
     * @public
     */
    desiredModelUnits: number | undefined;
    /**
     * <p>The name of the Provisioned Throughput.</p>
     * @public
     */
    provisionedModelName: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the Provisioned Throughput.</p>
     * @public
     */
    provisionedModelArn: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the model associated with this Provisioned Throughput.</p>
     * @public
     */
    modelArn: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the model requested to be associated to this Provisioned Throughput. This value differs from the <code>modelArn</code> if updating hasn't completed.</p>
     * @public
     */
    desiredModelArn: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the base model for which the Provisioned Throughput was created, or of the base model that the custom model for which the Provisioned Throughput was created was customized.</p>
     * @public
     */
    foundationModelArn: string | undefined;
    /**
     * <p>The status of the Provisioned Throughput. </p>
     * @public
     */
    status: ProvisionedModelStatus | undefined;
    /**
     * <p>The timestamp of the creation time for this Provisioned Throughput. </p>
     * @public
     */
    creationTime: Date | undefined;
    /**
     * <p>The timestamp of the last time that this Provisioned Throughput was modified. </p>
     * @public
     */
    lastModifiedTime: Date | undefined;
    /**
     * <p>A failure message for any issues that occurred during creation, updating, or deletion of the Provisioned Throughput.</p>
     * @public
     */
    failureMessage?: string | undefined;
    /**
     * <p>Commitment duration of the Provisioned Throughput.</p>
     * @public
     */
    commitmentDuration?: CommitmentDuration | undefined;
    /**
     * <p>The timestamp for when the commitment term for the Provisioned Throughput expires.</p>
     * @public
     */
    commitmentExpirationTime?: Date | undefined;
}
/**
 * @public
 * @enum
 */
export declare const SortByProvisionedModels: {
    readonly CREATION_TIME: "CreationTime";
};
/**
 * @public
 */
export type SortByProvisionedModels = (typeof SortByProvisionedModels)[keyof typeof SortByProvisionedModels];
/**
 * @public
 */
export interface ListProvisionedModelThroughputsRequest {
    /**
     * <p>A filter that returns Provisioned Throughputs created after the specified time. </p>
     * @public
     */
    creationTimeAfter?: Date | undefined;
    /**
     * <p>A filter that returns Provisioned Throughputs created before the specified time. </p>
     * @public
     */
    creationTimeBefore?: Date | undefined;
    /**
     * <p>A filter that returns Provisioned Throughputs if their statuses matches the value that you specify.</p>
     * @public
     */
    statusEquals?: ProvisionedModelStatus | undefined;
    /**
     * <p>A filter that returns Provisioned Throughputs whose model Amazon Resource Name (ARN) is equal to the value that you specify.</p>
     * @public
     */
    modelArnEquals?: string | undefined;
    /**
     * <p>A filter that returns Provisioned Throughputs if their name contains the expression that you specify.</p>
     * @public
     */
    nameContains?: string | undefined;
    /**
     * <p>THe maximum number of results to return in the response. If there are more results than the number you specified, the response returns a <code>nextToken</code> value. To see the next batch of results, send the <code>nextToken</code> value in another list request.</p>
     * @public
     */
    maxResults?: number | undefined;
    /**
     * <p>If there are more results than the number you specified in the <code>maxResults</code> field, the response returns a <code>nextToken</code> value. To see the next batch of results, specify the <code>nextToken</code> value in this field.</p>
     * @public
     */
    nextToken?: string | undefined;
    /**
     * <p>The field by which to sort the returned list of Provisioned Throughputs.</p>
     * @public
     */
    sortBy?: SortByProvisionedModels | undefined;
    /**
     * <p>The sort order of the results.</p>
     * @public
     */
    sortOrder?: SortOrder | undefined;
}
/**
 * <p>A summary of information about a Provisioned Throughput.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href="https://docs.aws.amazon.com/bedrock/latest/APIReference/API_ListProvisionedModelThroughputs.html#API_ListProvisionedModelThroughputs_ResponseSyntax">ListProvisionedThroughputs response</a> </p> </li> </ul>
 * @public
 */
export interface ProvisionedModelSummary {
    /**
     * <p>The name of the Provisioned Throughput.</p>
     * @public
     */
    provisionedModelName: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the Provisioned Throughput.</p>
     * @public
     */
    provisionedModelArn: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the model associated with the Provisioned Throughput.</p>
     * @public
     */
    modelArn: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the model requested to be associated to this Provisioned Throughput. This value differs from the <code>modelArn</code> if updating hasn't completed.</p>
     * @public
     */
    desiredModelArn: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the base model for which the Provisioned Throughput was created, or of the base model that the custom model for which the Provisioned Throughput was created was customized.</p>
     * @public
     */
    foundationModelArn: string | undefined;
    /**
     * <p>The number of model units allocated to the Provisioned Throughput.</p>
     * @public
     */
    modelUnits: number | undefined;
    /**
     * <p>The number of model units that was requested to be allocated to the Provisioned Throughput.</p>
     * @public
     */
    desiredModelUnits: number | undefined;
    /**
     * <p>The status of the Provisioned Throughput.</p>
     * @public
     */
    status: ProvisionedModelStatus | undefined;
    /**
     * <p>The duration for which the Provisioned Throughput was committed.</p>
     * @public
     */
    commitmentDuration?: CommitmentDuration | undefined;
    /**
     * <p>The timestamp for when the commitment term of the Provisioned Throughput expires.</p>
     * @public
     */
    commitmentExpirationTime?: Date | undefined;
    /**
     * <p>The time that the Provisioned Throughput was created. </p>
     * @public
     */
    creationTime: Date | undefined;
    /**
     * <p>The time that the Provisioned Throughput was last modified. </p>
     * @public
     */
    lastModifiedTime: Date | undefined;
}
/**
 * @public
 */
export interface ListProvisionedModelThroughputsResponse {
    /**
     * <p>If there are more results than the number you specified in the <code>maxResults</code> field, this value is returned. To see the next batch of results, include this value in the <code>nextToken</code> field in another list request.</p>
     * @public
     */
    nextToken?: string | undefined;
    /**
     * <p>A list of summaries, one for each Provisioned Throughput in the response.</p>
     * @public
     */
    provisionedModelSummaries?: ProvisionedModelSummary[] | undefined;
}
/**
 * @public
 */
export interface UpdateProvisionedModelThroughputRequest {
    /**
     * <p>The Amazon Resource Name (ARN) or name of the Provisioned Throughput to update.</p>
     * @public
     */
    provisionedModelId: string | undefined;
    /**
     * <p>The new name for this Provisioned Throughput.</p>
     * @public
     */
    desiredProvisionedModelName?: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the new model to associate with this Provisioned Throughput. You can't specify this field if this Provisioned Throughput is associated with a base model.</p> <p>If this Provisioned Throughput is associated with a custom model, you can specify one of the following options:</p> <ul> <li> <p>The base model from which the custom model was customized.</p> </li> <li> <p>Another custom model that was customized from the same base model as the custom model.</p> </li> </ul>
     * @public
     */
    desiredModelId?: string | undefined;
}
/**
 * @public
 */
export interface UpdateProvisionedModelThroughputResponse {
}
/**
 * @public
 */
export interface CreateFoundationModelAgreementRequest {
    /**
     * <p>An offer token encapsulates the information for an offer.</p>
     * @public
     */
    offerToken: string | undefined;
    /**
     * <p>Model Id of the model for the access request.</p>
     * @public
     */
    modelId: string | undefined;
}
/**
 * @public
 */
export interface CreateFoundationModelAgreementResponse {
    /**
     * <p>Model Id of the model for the access request.</p>
     * @public
     */
    modelId: string | undefined;
}
/**
 * @public
 */
export interface DeleteFoundationModelAgreementRequest {
    /**
     * <p>Model Id of the model access to delete.</p>
     * @public
     */
    modelId: string | undefined;
}
/**
 * @public
 */
export interface DeleteFoundationModelAgreementResponse {
}
/**
 * @public
 */
export interface GetFoundationModelAvailabilityRequest {
    /**
     * <p>The model Id of the foundation model.</p>
     * @public
     */
    modelId: string | undefined;
}
/**
 * @public
 * @enum
 */
export declare const AuthorizationStatus: {
    readonly AUTHORIZED: "AUTHORIZED";
    readonly NOT_AUTHORIZED: "NOT_AUTHORIZED";
};
/**
 * @public
 */
export type AuthorizationStatus = (typeof AuthorizationStatus)[keyof typeof AuthorizationStatus];
/**
 * @internal
 */
export declare const RequestMetadataBaseFiltersFilterSensitiveLog: (obj: RequestMetadataBaseFilters) => any;
/**
 * @internal
 */
export declare const RequestMetadataFiltersFilterSensitiveLog: (obj: RequestMetadataFilters) => any;
/**
 * @internal
 */
export declare const InvocationLogsConfigFilterSensitiveLog: (obj: InvocationLogsConfig) => any;
/**
 * @internal
 */
export declare const TrainingDataConfigFilterSensitiveLog: (obj: TrainingDataConfig) => any;
/**
 * @internal
 */
export declare const GetCustomModelResponseFilterSensitiveLog: (obj: GetCustomModelResponse) => any;
/**
 * @internal
 */
export declare const BatchDeleteEvaluationJobRequestFilterSensitiveLog: (obj: BatchDeleteEvaluationJobRequest) => any;
/**
 * @internal
 */
export declare const BatchDeleteEvaluationJobErrorFilterSensitiveLog: (obj: BatchDeleteEvaluationJobError) => any;
/**
 * @internal
 */
export declare const BatchDeleteEvaluationJobItemFilterSensitiveLog: (obj: BatchDeleteEvaluationJobItem) => any;
/**
 * @internal
 */
export declare const BatchDeleteEvaluationJobResponseFilterSensitiveLog: (obj: BatchDeleteEvaluationJobResponse) => any;
/**
 * @internal
 */
export declare const CustomMetricDefinitionFilterSensitiveLog: (obj: CustomMetricDefinition) => any;
/**
 * @internal
 */
export declare const AutomatedEvaluationCustomMetricSourceFilterSensitiveLog: (obj: AutomatedEvaluationCustomMetricSource) => any;
/**
 * @internal
 */
export declare const AutomatedEvaluationCustomMetricConfigFilterSensitiveLog: (obj: AutomatedEvaluationCustomMetricConfig) => any;
/**
 * @internal
 */
export declare const EvaluationDatasetFilterSensitiveLog: (obj: EvaluationDataset) => any;
/**
 * @internal
 */
export declare const EvaluationDatasetMetricConfigFilterSensitiveLog: (obj: EvaluationDatasetMetricConfig) => any;
/**
 * @internal
 */
export declare const AutomatedEvaluationConfigFilterSensitiveLog: (obj: AutomatedEvaluationConfig) => any;
/**
 * @internal
 */
export declare const HumanEvaluationCustomMetricFilterSensitiveLog: (obj: HumanEvaluationCustomMetric) => any;
/**
 * @internal
 */
export declare const HumanWorkflowConfigFilterSensitiveLog: (obj: HumanWorkflowConfig) => any;
/**
 * @internal
 */
export declare const HumanEvaluationConfigFilterSensitiveLog: (obj: HumanEvaluationConfig) => any;
/**
 * @internal
 */
export declare const EvaluationConfigFilterSensitiveLog: (obj: EvaluationConfig) => any;
/**
 * @internal
 */
export declare const EvaluationBedrockModelFilterSensitiveLog: (obj: EvaluationBedrockModel) => any;
/**
 * @internal
 */
export declare const EvaluationModelConfigFilterSensitiveLog: (obj: EvaluationModelConfig) => any;
/**
 * @internal
 */
export declare const PromptTemplateFilterSensitiveLog: (obj: PromptTemplate) => any;
/**
 * @internal
 */
export declare const ExternalSourcesGenerationConfigurationFilterSensitiveLog: (obj: ExternalSourcesGenerationConfiguration) => any;
/**
 * @internal
 */
export declare const ByteContentDocFilterSensitiveLog: (obj: ByteContentDoc) => any;
/**
 * @internal
 */
export declare const ExternalSourceFilterSensitiveLog: (obj: ExternalSource) => any;
/**
 * @internal
 */
export declare const ExternalSourcesRetrieveAndGenerateConfigurationFilterSensitiveLog: (obj: ExternalSourcesRetrieveAndGenerateConfiguration) => any;
/**
 * @internal
 */
export declare const GenerationConfigurationFilterSensitiveLog: (obj: GenerationConfiguration) => any;
/**
 * @internal
 */
export declare const MetadataAttributeSchemaFilterSensitiveLog: (obj: MetadataAttributeSchema) => any;
/**
 * @internal
 */
export declare const ImplicitFilterConfigurationFilterSensitiveLog: (obj: ImplicitFilterConfiguration) => any;
/**
 * @internal
 */
export declare const RerankingMetadataSelectiveModeConfigurationFilterSensitiveLog: (obj: RerankingMetadataSelectiveModeConfiguration) => any;
/**
 * @internal
 */
export declare const MetadataConfigurationForRerankingFilterSensitiveLog: (obj: MetadataConfigurationForReranking) => any;
/**
 * @internal
 */
export declare const VectorSearchBedrockRerankingConfigurationFilterSensitiveLog: (obj: VectorSearchBedrockRerankingConfiguration) => any;
/**
 * @internal
 */
export declare const VectorSearchRerankingConfigurationFilterSensitiveLog: (obj: VectorSearchRerankingConfiguration) => any;
/**
 * @internal
 */
export declare const GetEvaluationJobRequestFilterSensitiveLog: (obj: GetEvaluationJobRequest) => any;
/**
 * @internal
 */
export declare const StopEvaluationJobRequestFilterSensitiveLog: (obj: StopEvaluationJobRequest) => any;
/**
 * @internal
 */
export declare const GuardrailContentFilterConfigFilterSensitiveLog: (obj: GuardrailContentFilterConfig) => any;
/**
 * @internal
 */
export declare const GuardrailContentFiltersTierConfigFilterSensitiveLog: (obj: GuardrailContentFiltersTierConfig) => any;
/**
 * @internal
 */
export declare const GuardrailContentPolicyConfigFilterSensitiveLog: (obj: GuardrailContentPolicyConfig) => any;
/**
 * @internal
 */
export declare const GuardrailContextualGroundingFilterConfigFilterSensitiveLog: (obj: GuardrailContextualGroundingFilterConfig) => any;
/**
 * @internal
 */
export declare const GuardrailContextualGroundingPolicyConfigFilterSensitiveLog: (obj: GuardrailContextualGroundingPolicyConfig) => any;
/**
 * @internal
 */
export declare const GuardrailTopicsTierConfigFilterSensitiveLog: (obj: GuardrailTopicsTierConfig) => any;
/**
 * @internal
 */
export declare const GuardrailTopicConfigFilterSensitiveLog: (obj: GuardrailTopicConfig) => any;
/**
 * @internal
 */
export declare const GuardrailTopicPolicyConfigFilterSensitiveLog: (obj: GuardrailTopicPolicyConfig) => any;
/**
 * @internal
 */
export declare const GuardrailManagedWordsConfigFilterSensitiveLog: (obj: GuardrailManagedWordsConfig) => any;
/**
 * @internal
 */
export declare const GuardrailWordConfigFilterSensitiveLog: (obj: GuardrailWordConfig) => any;
/**
 * @internal
 */
export declare const GuardrailWordPolicyConfigFilterSensitiveLog: (obj: GuardrailWordPolicyConfig) => any;
/**
 * @internal
 */
export declare const CreateGuardrailRequestFilterSensitiveLog: (obj: CreateGuardrailRequest) => any;
/**
 * @internal
 */
export declare const CreateGuardrailVersionRequestFilterSensitiveLog: (obj: CreateGuardrailVersionRequest) => any;
/**
 * @internal
 */
export declare const GuardrailContentFilterFilterSensitiveLog: (obj: GuardrailContentFilter) => any;
/**
 * @internal
 */
export declare const GuardrailContentFiltersTierFilterSensitiveLog: (obj: GuardrailContentFiltersTier) => any;
/**
 * @internal
 */
export declare const GuardrailContentPolicyFilterSensitiveLog: (obj: GuardrailContentPolicy) => any;
/**
 * @internal
 */
export declare const GuardrailContextualGroundingFilterFilterSensitiveLog: (obj: GuardrailContextualGroundingFilter) => any;
/**
 * @internal
 */
export declare const GuardrailContextualGroundingPolicyFilterSensitiveLog: (obj: GuardrailContextualGroundingPolicy) => any;
/**
 * @internal
 */
export declare const GuardrailTopicsTierFilterSensitiveLog: (obj: GuardrailTopicsTier) => any;
/**
 * @internal
 */
export declare const GuardrailTopicFilterSensitiveLog: (obj: GuardrailTopic) => any;
/**
 * @internal
 */
export declare const GuardrailTopicPolicyFilterSensitiveLog: (obj: GuardrailTopicPolicy) => any;
/**
 * @internal
 */
export declare const GuardrailManagedWordsFilterSensitiveLog: (obj: GuardrailManagedWords) => any;
/**
 * @internal
 */
export declare const GuardrailWordFilterSensitiveLog: (obj: GuardrailWord) => any;
/**
 * @internal
 */
export declare const GuardrailWordPolicyFilterSensitiveLog: (obj: GuardrailWordPolicy) => any;
/**
 * @internal
 */
export declare const GetGuardrailResponseFilterSensitiveLog: (obj: GetGuardrailResponse) => any;
/**
 * @internal
 */
export declare const GuardrailSummaryFilterSensitiveLog: (obj: GuardrailSummary) => any;
/**
 * @internal
 */
export declare const ListGuardrailsResponseFilterSensitiveLog: (obj: ListGuardrailsResponse) => any;
/**
 * @internal
 */
export declare const UpdateGuardrailRequestFilterSensitiveLog: (obj: UpdateGuardrailRequest) => any;
/**
 * @internal
 */
export declare const CreateInferenceProfileRequestFilterSensitiveLog: (obj: CreateInferenceProfileRequest) => any;
/**
 * @internal
 */
export declare const GetInferenceProfileResponseFilterSensitiveLog: (obj: GetInferenceProfileResponse) => any;
/**
 * @internal
 */
export declare const InferenceProfileSummaryFilterSensitiveLog: (obj: InferenceProfileSummary) => any;
/**
 * @internal
 */
export declare const ListInferenceProfilesResponseFilterSensitiveLog: (obj: ListInferenceProfilesResponse) => any;
/**
 * @internal
 */
export declare const GetModelInvocationJobResponseFilterSensitiveLog: (obj: GetModelInvocationJobResponse) => any;
/**
 * @internal
 */
export declare const ModelInvocationJobSummaryFilterSensitiveLog: (obj: ModelInvocationJobSummary) => any;
/**
 * @internal
 */
export declare const ListModelInvocationJobsResponseFilterSensitiveLog: (obj: ListModelInvocationJobsResponse) => any;
/**
 * @internal
 */
export declare const CreatePromptRouterRequestFilterSensitiveLog: (obj: CreatePromptRouterRequest) => any;
/**
 * @internal
 */
export declare const GetPromptRouterResponseFilterSensitiveLog: (obj: GetPromptRouterResponse) => any;
/**
 * @internal
 */
export declare const PromptRouterSummaryFilterSensitiveLog: (obj: PromptRouterSummary) => any;
/**
 * @internal
 */
export declare const ListPromptRoutersResponseFilterSensitiveLog: (obj: ListPromptRoutersResponse) => any;
