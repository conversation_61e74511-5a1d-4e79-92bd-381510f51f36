import {
  BedrockRuntimeClient,
  InvokeModelCommand,
  InvokeModelCommandInput,
} from '@aws-sdk/client-bedrock-runtime';
import { config } from '../config/environment';
import { BedrockError, logger } from '../middleware/errorHandler';

/**
 * AWS Bedrock service for AI text generation and embeddings
 */
export class BedrockService {
  private client: BedrockRuntimeClient;

  constructor() {
    this.client = new BedrockRuntimeClient({
      region: config.aws.region,
      credentials: {
        accessKeyId: config.aws.accessKeyId,
        secretAccessKey: config.aws.secretAccessKey,
      },
    });
  }

  /**
   * Generate AI response using Claude model
   */
  async generateResponse(prompt: string, systemPrompt?: string): Promise<string> {
    try {
      const messages = [
        {
          role: 'user',
          content: prompt,
        },
      ];

      const requestBody = {
        anthropic_version: 'bedrock-2023-05-31',
        max_tokens: config.bedrock.maxTokens,
        temperature: config.bedrock.temperature,
        messages,
        ...(systemPrompt && { system: systemPrompt }),
      };

      const input: InvokeModelCommandInput = {
        modelId: config.bedrock.modelId,
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify(requestBody),
      };

      const command = new InvokeModelCommand(input);
      const response = await this.client.send(command);

      if (!response.body) {
        throw new Error('No response body from Bedrock');
      }

      const responseBody = JSON.parse(new TextDecoder().decode(response.body));
      
      if (responseBody.content && responseBody.content.length > 0) {
        return responseBody.content[0].text;
      }

      throw new BedrockError('Invalid response format from Bedrock');
    } catch (error) {
      logger.error('Error generating AI response', error instanceof Error ? error : new Error(String(error)));
      if (error instanceof BedrockError) {
        throw error;
      }
      throw new BedrockError(`Failed to generate AI response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate embeddings for text using Amazon Titan Embeddings
   */
  async generateEmbedding(text: string): Promise<number[]> {
    try {
      const requestBody = {
        inputText: text,
      };

      const input: InvokeModelCommandInput = {
        modelId: 'amazon.titan-embed-text-v1',
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify(requestBody),
      };

      const command = new InvokeModelCommand(input);
      const response = await this.client.send(command);

      if (!response.body) {
        throw new Error('No response body from Bedrock');
      }

      const responseBody = JSON.parse(new TextDecoder().decode(response.body));
      
      if (responseBody.embedding && Array.isArray(responseBody.embedding)) {
        return responseBody.embedding;
      }

      throw new BedrockError('Invalid embedding response format from Bedrock');
    } catch (error) {
      logger.error('Error generating embedding', error instanceof Error ? error : new Error(String(error)));
      if (error instanceof BedrockError) {
        throw error;
      }
      throw new BedrockError(`Failed to generate embedding: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate embeddings for multiple texts in batch
   */
  async generateEmbeddings(texts: string[]): Promise<number[][]> {
    try {
      const embeddings: number[][] = [];
      
      // Process in batches to avoid rate limits
      const batchSize = 5;
      for (let i = 0; i < texts.length; i += batchSize) {
        const batch = texts.slice(i, i + batchSize);
        const batchPromises = batch.map(text => this.generateEmbedding(text));
        const batchResults = await Promise.all(batchPromises);
        embeddings.push(...batchResults);
        
        // Add small delay between batches
        if (i + batchSize < texts.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }
      
      return embeddings;
    } catch (error) {
      logger.error('Error generating batch embeddings', error instanceof Error ? error : new Error(String(error)));
      if (error instanceof BedrockError) {
        throw error;
      }
      throw new BedrockError(`Failed to generate batch embeddings: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create a conversational AI prompt for business recommendations
   */
  createBusinessRecommendationPrompt(
    userQuery: string,
    businessData: any[],
    conversationContext?: string
  ): string {
    const contextSection = conversationContext 
      ? `Previous conversation context:\n${conversationContext}\n\n`
      : '';

    const businessSection = businessData.length > 0
      ? `Here are some relevant businesses I found:\n${businessData.map((business, index) => 
          `${index + 1}. ${business.name || business.restaurant_name || business.shop_name || business.club_name}
             - Type: ${business.entity_type || 'business'}
             - Description: ${business.description || business.branch_description || 'No description available'}
             - Location: ${business.branch_address || business.club_location || 'Location not specified'}
             - Phone: ${business.phone_number || 'Not available'}
             ${business.average_spend ? `- Average spend: AED ${business.average_spend}` : ''}
             ${business.branch_tags ? `- Tags: ${business.branch_tags.join(', ')}` : ''}
             ${business.club_tags ? `- Tags: ${business.club_tags.join(', ')}` : ''}
          `).join('\n\n')}\n\n`
      : 'I couldn\'t find any specific businesses matching your query in my database.\n\n';

    return `${contextSection}User query: "${userQuery}"

${businessSection}Please provide a helpful, conversational response about local businesses in Dubai/UAE. If you found relevant businesses above, recommend them naturally and mention their key details. If no specific businesses were found, provide general guidance about the type of businesses they're looking for and suggest they try different search terms.

Keep your response:
- Conversational and friendly
- Focused on helping them find what they need
- Include practical information like location, contact details, and pricing when available
- Suggest alternatives if the exact request isn't available
- Maximum 200 words

Response:`;
  }

  /**
   * Create a system prompt for the conversational AI
   */
  getSystemPrompt(): string {
    return `You are Cravin Concierge, a helpful AI assistant specializing in local business recommendations in Dubai and the UAE. You help users discover restaurants, shops, clubs, and other local businesses.

Your role:
- Provide personalized business recommendations based on user queries
- Share practical information like location, contact details, pricing, and operating hours
- Maintain a friendly, conversational tone
- Focus on helping users make informed decisions about local businesses
- If you don't have specific information, be honest and suggest alternatives

Guidelines:
- Always be helpful and professional
- Provide accurate information based on the business data available
- Suggest alternatives when exact matches aren't found
- Keep responses concise but informative
- Include contact information and location details when available`;
  }
}

// Export singleton instance
export const bedrockService = new BedrockService();
