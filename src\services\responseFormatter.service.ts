import { ConversationResponse, VectorSearchResult, TimingSlot } from '../models/business.models';

/**
 * Response Formatter Service for creating professional, conversational responses
 */
export class ResponseFormatterService {

  /**
   * Format the complete conversation response
   * @param aiResponse - Raw AI response text
   * @param relevantBusinesses - Businesses found through vector search
   * @param suggestions - Follow-up question suggestions
   * @param contextId - Conversation context ID
   * @returns Formatted conversation response
   */
  formatConversationResponse(
    aiResponse: string,
    relevantBusinesses: VectorSearchResult[],
    suggestions?: string[],
    contextId?: string
  ): ConversationResponse {
    return {
      response: this.formatResponseText(aiResponse),
      relevant_businesses: this.formatBusinessResults(relevantBusinesses),
      suggestions: suggestions || [],
      context_id: contextId || undefined
    };
  }

  /**
   * Format the AI response text for better readability
   * @param responseText - Raw AI response
   * @returns Formatted response text
   */
  private formatResponseText(responseText: string): string {
    // Clean up the response text
    let formatted = responseText.trim();
    
    // Ensure proper spacing after periods
    formatted = formatted.replace(/\.([A-Z])/g, '. $1');
    
    // Ensure proper spacing after commas
    formatted = formatted.replace(/,([A-Z])/g, ', $1');
    
    // Remove excessive whitespace
    formatted = formatted.replace(/\s+/g, ' ');
    
    // Ensure the response ends with proper punctuation
    if (!formatted.match(/[.!?]$/)) {
      formatted += '.';
    }
    
    return formatted;
  }

  /**
   * Format business search results with enhanced information
   * @param businesses - Raw vector search results
   * @returns Enhanced business results
   */
  private formatBusinessResults(businesses: VectorSearchResult[]): VectorSearchResult[] {
    return businesses.map(business => ({
      ...business,
      content: this.enhanceBusinessContent(business),
      metadata: {
        ...business.metadata,
        formatted_location: this.formatLocation(business.metadata.location),
        formatted_tags: this.formatTags(business.metadata.tags)
      }
    }));
  }

  /**
   * Enhance business content with additional formatting
   * @param business - Business search result
   * @returns Enhanced content string
   */
  private enhanceBusinessContent(business: VectorSearchResult): string {
    let content = business.content;
    
    // Add business type context
    const typeContext = this.getBusinessTypeContext(business.metadata.type);
    if (typeContext && !content.toLowerCase().includes(typeContext.toLowerCase())) {
      content = `${typeContext} - ${content}`;
    }
    
    return content;
  }

  /**
   * Get contextual information based on business type
   * @param type - Business type
   * @returns Context string
   */
  private getBusinessTypeContext(type: string): string {
    switch (type) {
      case 'club':
        return 'Sports & Recreation';
      case 'restaurant':
        return 'Dining & Cuisine';
      case 'shop':
        return 'Shopping & Services';
      default:
        return '';
    }
  }

  /**
   * Format location information
   * @param location - Location string
   * @returns Formatted location
   */
  private formatLocation(location?: string): string {
    if (!location) return 'Location not specified';
    
    // Capitalize first letter of each word
    return location.replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Format tags array
   * @param tags - Tags array
   * @returns Formatted tags
   */
  private formatTags(tags?: string[]): string[] {
    if (!tags || tags.length === 0) return [];
    
    return tags.map(tag => 
      tag.replace(/\b\w/g, l => l.toUpperCase())
    );
  }

  /**
   * Format business hours for display
   * @param timings - Business timing slots
   * @returns Formatted hours string
   */
  formatBusinessHours(timings?: TimingSlot[]): string {
    if (!timings || timings.length === 0) {
      return 'Hours not available';
    }

    const today = new Date().toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
    const todayTiming = timings.find(t => t.day.toLowerCase() === today);
    
    if (todayTiming) {
      if (todayTiming.isOpen) {
        return `Open today: ${todayTiming.open} - ${todayTiming.close}`;
      } else {
        return 'Closed today';
      }
    }

    // Return general hours if today's hours not found
    const openDays = timings.filter(t => t.isOpen);
    if (openDays.length > 0) {
      const firstOpen = openDays[0];
      return `Generally open: ${firstOpen?.open} - ${firstOpen?.close}`;
    }

    return 'Hours not available';
  }

  /**
   * Format contact information
   * @param phoneNumber - Phone number
   * @param whatsappId - WhatsApp ID
   * @returns Formatted contact info
   */
  formatContactInfo(phoneNumber?: string, whatsappId?: string): string {
    const contacts: string[] = [];
    
    if (phoneNumber) {
      contacts.push(`📞 ${this.formatPhoneNumber(phoneNumber)}`);
    }
    
    if (whatsappId) {
      contacts.push(`💬 WhatsApp available`);
    }
    
    return contacts.length > 0 ? contacts.join(' | ') : 'Contact info not available';
  }

  /**
   * Format phone number for display
   * @param phoneNumber - Raw phone number
   * @returns Formatted phone number
   */
  private formatPhoneNumber(phoneNumber: string): string {
    // Remove all non-digit characters
    const digits = phoneNumber.replace(/\D/g, '');
    
    // Format UAE phone numbers
    if (digits.startsWith('971')) {
      return `+971 ${digits.slice(3, 5)} ${digits.slice(5, 8)} ${digits.slice(8)}`;
    }
    
    // Format local UAE numbers
    if (digits.startsWith('0') && digits.length === 10) {
      return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6)}`;
    }
    
    // Return original if format not recognized
    return phoneNumber;
  }

  /**
   * Format price range information
   * @param averageSpend - Average spend amount
   * @returns Formatted price range
   */
  formatPriceRange(averageSpend?: number): string {
    if (!averageSpend) return 'Price range not available';
    
    if (averageSpend < 100) {
      return '💰 Budget-friendly (Under AED 100)';
    } else if (averageSpend <= 300) {
      return '💰💰 Moderate (AED 100-300)';
    } else {
      return '💰💰💰 Premium (Above AED 300)';
    }
  }

  /**
   * Create a summary card for a business
   * @param business - Business information
   * @returns Formatted business card
   */
  createBusinessCard(business: any, type: 'club' | 'restaurant' | 'shop'): string {
    const lines: string[] = [];
    
    // Business name and type
    lines.push(`🏢 **${business.name || business.club_name || business.restaurant_name || business.shop_name}**`);
    
    // Location
    if (business.location || business.club_location || business.branch_address) {
      const location = business.location || business.club_location || business.branch_address;
      lines.push(`📍 ${this.formatLocation(location)}`);
    }
    
    // Contact info
    const contact = this.formatContactInfo(business.phone_number, business.whatsapp_id);
    if (contact !== 'Contact info not available') {
      lines.push(contact);
    }
    
    // Hours
    const hours = this.formatBusinessHours(business.timings || business.club_timings || business.restaurant_timings || business.shop_timings);
    if (hours !== 'Hours not available') {
      lines.push(`🕒 ${hours}`);
    }
    
    // Price range
    if (business.average_spend) {
      lines.push(this.formatPriceRange(business.average_spend));
    }
    
    // Special features based on type
    if (type === 'club' && business.take_booking) {
      lines.push('📅 Booking available');
    } else if (type === 'restaurant' && business.take_orders) {
      lines.push('🍽️ Online ordering available');
    } else if (type === 'shop' && business.take_orders) {
      lines.push('🛒 Online shopping available');
    }
    
    return lines.join('\n');
  }

  /**
   * Generate error response for failed queries
   * @param error - Error message
   * @returns Formatted error response
   */
  generateErrorResponse(error: string): ConversationResponse {
    return {
      response: `I apologize, but I'm having trouble processing your request right now. ${error} Please try again in a moment, or feel free to rephrase your question.`,
      relevant_businesses: [],
      suggestions: [
        'Can you help me find restaurants nearby?',
        'What sports clubs are available?',
        'Show me local shops and services'
      ]
    };
  }

  /**
   * Generate fallback response when no businesses are found
   * @param query - Original user query
   * @returns Formatted fallback response
   */
  generateNoResultsResponse(query: string): ConversationResponse {
    return {
      response: `I couldn't find any specific businesses matching "${query}" in our current database. However, I'd be happy to help you explore other options or search with different criteria. Could you try being more specific about the type of business, location, or service you're looking for?`,
      relevant_businesses: [],
      suggestions: [
        'Show me all restaurants in Dubai',
        'Find sports clubs near me',
        'What shops are available for shopping?'
      ]
    };
  }
}
