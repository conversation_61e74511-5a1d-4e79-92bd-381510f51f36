# Server Configuration
PORT=3000
HOST=localhost
NODE_ENV=development

# API Configuration
API_PREFIX=/api
API_VERSION=v1

# Security Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/cravin_concierge
# OR use individual database settings:
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cravin_concierge
DB_USERNAME=your_username
DB_PASSWORD=your_password

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key

# AWS Bedrock Configuration
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0
BEDROCK_MAX_TOKENS=4000
BEDROCK_TEMPERATURE=0.7

# Vector Search Configuration
VECTOR_SIMILARITY_THRESHOLD=0.7
VECTOR_MAX_RESULTS=5
EMBEDDING_DIMENSIONS=1536

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=combined

# Application Configuration
APP_NAME=Cravin Concierge API
APP_VERSION=1.0.0
APP_DESCRIPTION=Conversational AI for Local Business Discovery

# WhatsApp Configuration (if needed)
WHATSAPP_VERIFY_TOKEN=your_whatsapp_verify_token
