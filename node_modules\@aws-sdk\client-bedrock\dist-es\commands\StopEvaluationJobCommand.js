import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { StopEvaluationJobRequestFilterSensitiveLog, } from "../models/models_0";
import { de_StopEvaluationJobCommand, se_StopEvaluationJobCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class StopEvaluationJobCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockControlPlaneService", "StopEvaluationJob", {})
    .n("BedrockClient", "StopEvaluationJobCommand")
    .f(StopEvaluationJobRequestFilterSensitiveLog, void 0)
    .ser(se_StopEvaluationJobCommand)
    .de(de_StopEvaluationJobCommand)
    .build() {
}
