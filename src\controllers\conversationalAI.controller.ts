import { Request, Response } from 'express';
import { VectorSearchService } from '../services/vectorSearch.service';
import { BedrockService } from '../services/bedrock.service';
import { ResponseFormatterService } from '../services/responseFormatter.service';
import { ConversationQuery, ConversationResponse } from '../models/business.models';

/**
 * Conversational AI Controller
 * Handles user queries and orchestrates AI responses with business data
 */
export class ConversationalAIController {
  private vectorSearchService: VectorSearchService;
  private bedrockService: BedrockService;
  private responseFormatter: ResponseFormatterService;

  constructor() {
    this.vectorSearchService = new VectorSearchService();
    this.bedrockService = new BedrockService();
    this.responseFormatter = new ResponseFormatterService();
  }

  /**
   * Handle conversational query from user
   * @param req - Express request object
   * @param res - Express response object
   */
  async handleQuery(req: Request, res: Response): Promise<void> {
    try {
      const query: ConversationQuery = req.body;

      // Validate request
      if (!query.message || query.message.trim().length === 0) {
        res.status(400).json({
          success: false,
          error: 'Message is required',
          data: null
        });
        return;
      }

      // Generate embeddings for the user query
      const queryEmbeddings = await this.bedrockService.generateEmbeddings(query.message);

      // Perform vector search to find relevant businesses
      const relevantBusinesses = await this.vectorSearchService.searchSimilarBusinesses(
        queryEmbeddings,
        query.preferences?.business_type,
        query.user_location?.emirate || query.user_location?.address
      );

      // Generate AI response using Bedrock
      const aiResponse = await this.bedrockService.generateConversationalResponse(
        query,
        relevantBusinesses
      );

      // Generate follow-up suggestions
      const suggestions = await this.bedrockService.generateSuggestions(
        query,
        aiResponse,
        relevantBusinesses
      );

      // Format the complete response
      const formattedResponse = this.responseFormatter.formatConversationResponse(
        aiResponse,
        relevantBusinesses,
        suggestions,
        this.generateContextId()
      );

      res.status(200).json({
        success: true,
        data: formattedResponse,
        metadata: {
          query_processed_at: new Date().toISOString(),
          businesses_found: relevantBusinesses.length,
          response_time: Date.now() - req.startTime
        }
      });

    } catch (error) {
      console.error('Error in conversational AI query:', error);
      
      const errorResponse = this.responseFormatter.generateErrorResponse(
        'There was an issue processing your request.'
      );

      res.status(500).json({
        success: false,
        error: 'Internal server error',
        data: errorResponse
      });
    }
  }

  /**
   * Search for specific business types
   * @param req - Express request object
   * @param res - Express response object
   */
  async searchBusinessType(req: Request, res: Response): Promise<void> {
    try {
      const { type, query: searchQuery, location, filters } = req.body;

      if (!type || !['club', 'restaurant', 'shop'].includes(type)) {
        res.status(400).json({
          success: false,
          error: 'Valid business type (club, restaurant, shop) is required',
          data: null
        });
        return;
      }

      if (!searchQuery || searchQuery.trim().length === 0) {
        res.status(400).json({
          success: false,
          error: 'Search query is required',
          data: null
        });
        return;
      }

      // Generate embeddings for the search query
      const queryEmbeddings = await this.bedrockService.generateEmbeddings(searchQuery);

      let relevantBusinesses;

      // Perform type-specific search
      switch (type) {
        case 'club':
          relevantBusinesses = await this.vectorSearchService.searchClubs(
            queryEmbeddings,
            filters?.sport_type
          );
          break;
        case 'restaurant':
          relevantBusinesses = await this.vectorSearchService.searchRestaurants(
            queryEmbeddings,
            filters?.cuisine_type,
            filters?.price_range
          );
          break;
        case 'shop':
          relevantBusinesses = await this.vectorSearchService.searchShops(
            queryEmbeddings,
            filters?.product_type
          );
          break;
        default:
          relevantBusinesses = [];
      }

      // Create a conversational query object for AI response
      const conversationQuery: ConversationQuery = {
        message: searchQuery,
        preferences: {
          business_type: type,
          ...filters
        },
        user_location: location
      };

      // Generate AI response
      const aiResponse = await this.bedrockService.generateConversationalResponse(
        conversationQuery,
        relevantBusinesses
      );

      // Generate suggestions
      const suggestions = await this.bedrockService.generateSuggestions(
        conversationQuery,
        aiResponse,
        relevantBusinesses
      );

      // Format response
      const formattedResponse = this.responseFormatter.formatConversationResponse(
        aiResponse,
        relevantBusinesses,
        suggestions
      );

      res.status(200).json({
        success: true,
        data: formattedResponse,
        metadata: {
          business_type: type,
          query_processed_at: new Date().toISOString(),
          businesses_found: relevantBusinesses.length
        }
      });

    } catch (error) {
      console.error('Error in business type search:', error);
      
      const errorResponse = this.responseFormatter.generateErrorResponse(
        'There was an issue searching for businesses.'
      );

      res.status(500).json({
        success: false,
        error: 'Internal server error',
        data: errorResponse
      });
    }
  }

  /**
   * Get detailed information about a specific business
   * @param req - Express request object
   * @param res - Express response object
   */
  async getBusinessDetails(req: Request, res: Response): Promise<void> {
    try {
      const { businessId, businessType } = req.params;

      if (!businessId || !businessType) {
        res.status(400).json({
          success: false,
          error: 'Business ID and type are required',
          data: null
        });
        return;
      }

      if (!['club', 'restaurant', 'shop'].includes(businessType)) {
        res.status(400).json({
          success: false,
          error: 'Invalid business type',
          data: null
        });
        return;
      }

      // Get business details from database
      const businessDetails = await this.vectorSearchService.getBusinessDetails(
        businessId,
        businessType as 'club' | 'restaurant' | 'shop'
      );

      if (!businessDetails) {
        res.status(404).json({
          success: false,
          error: 'Business not found',
          data: null
        });
        return;
      }

      // Create a business card
      const businessCard = this.responseFormatter.createBusinessCard(
        businessDetails,
        businessType as 'club' | 'restaurant' | 'shop'
      );

      res.status(200).json({
        success: true,
        data: {
          business_details: businessDetails,
          formatted_card: businessCard
        },
        metadata: {
          business_id: businessId,
          business_type: businessType,
          retrieved_at: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error('Error getting business details:', error);
      
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        data: null
      });
    }
  }

  /**
   * Health check endpoint for the AI services
   * @param req - Express request object
   * @param res - Express response object
   */
  async healthCheck(req: Request, res: Response): Promise<void> {
    try {
      // Test Bedrock connection
      const bedrockStatus = await this.bedrockService.testConnection();

      // Test database connection (vector search service)
      let databaseStatus = false;
      try {
        await this.vectorSearchService.initializeEmbeddingsTable();
        databaseStatus = true;
      } catch (error) {
        console.error('Database health check failed:', error);
      }

      const overallStatus = bedrockStatus && databaseStatus;

      res.status(overallStatus ? 200 : 503).json({
        success: overallStatus,
        data: {
          status: overallStatus ? 'healthy' : 'unhealthy',
          services: {
            bedrock: bedrockStatus ? 'connected' : 'disconnected',
            database: databaseStatus ? 'connected' : 'disconnected',
            vector_search: databaseStatus ? 'available' : 'unavailable'
          },
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error('Health check error:', error);
      
      res.status(503).json({
        success: false,
        error: 'Health check failed',
        data: {
          status: 'unhealthy',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  /**
   * Generate a unique context ID for conversation tracking
   * @returns Context ID string
   */
  private generateContextId(): string {
    return `ctx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
