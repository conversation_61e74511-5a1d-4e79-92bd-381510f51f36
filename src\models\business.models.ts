/**
 * Business-related database models and interfaces
 */

// Common interfaces
export interface TimingSlot {
  day: string;
  open: string;
  close: string;
  isOpen: boolean;
}

export interface Location {
  latitude?: number;
  longitude?: number;
  address?: string;
  emirate?: string;
}

export interface SocialLinks {
  facebook?: string;
  instagram?: string;
  twitter?: string;
  website?: string;
}

export interface PaymentMethod {
  type: string;
  enabled: boolean;
  details?: any;
}

// Club-related interfaces
export interface Club {
  club_id: string;
  club_name: string;
  take_booking: boolean;
  booking_link?: string;
  phone_number: string;
  club_logo_url?: string;
  club_bg_img_url?: string;
  club_location?: string;
  club_location_url?: string;
  created_at: Date;
  booking_policy?: string[];
  club_social_links?: SocialLinks;
  club_cancellation_number?: string;
  club_timings?: TimingSlot[];
  club_emails?: string[];
  club_whatsapp_id?: string;
  club_whatsapp_token?: string;
  payment_method?: string;
  payment_api_key?: string;
  payment_webhook_secret?: string;
  club_sender_email?: string;
  club_email_password?: string;
  club_business_account_id?: string;
  stripe_subscription_id?: string;
  stripe_customer_id?: string;
  subscription_status: boolean;
  club_whatsapp_app_id?: string;
  enable_support: boolean;
  parent_club?: string;
  inbox_access_enabled?: boolean;
  network_outlet_id?: string;
  club_partial_payment?: number;
  subscription_graceperiod: number;
  auto_refundable?: boolean;
  payment_methods?: PaymentMethod[];
  club_emirate?: string;
  average_spend?: number;
  club_tags?: string[];
  is_only_for_listing?: boolean;
}

export interface Facility {
  fk_club_id: string;
  facility_id: string;
  facility_name: string;
  facility_category: string;
  facility_type: string;
  time_slot_duration: string;
  court_types: string[];
  pricing?: any;
  facility_timing?: TimingSlot[];
  status: boolean;
  pricing_option?: string;
  created_at?: Date;
  break_timing?: TimingSlot[];
  partial_payment_status: boolean;
  banner_images?: string[];
  pay_at_venue_status: boolean;
  facility_timezone?: string;
  facility_min_duration: number;
  facility_group_id?: string;
  facility_banner_link?: string;
}

export interface Court {
  court_id: string;
  fk_facility_id: string;
  court_name: string;
  status: boolean;
  created_at?: string;
}

// Restaurant-related interfaces
export interface Restaurant {
  restaurant_id: string;
  restaurant_name: string;
  restaurant_logo_url?: string;
  restaurant_bg_img_url?: string;
  restaurant_social_links?: SocialLinks;
  restaurant_cancellation_number?: string;
  restaurant_timings?: TimingSlot[];
  restaurant_whatsapp_id?: string;
  restaurant_whatsapp_token?: string;
  restaurant_whatsapp_app_id?: string;
  restaurant_business_account_id?: string;
  take_orders: boolean;
  phone_number: string;
  order_policy?: string[];
  payment_methods?: PaymentMethod[];
  stripe_subscription_id?: string;
  stripe_customer_id?: string;
  subscription_status: boolean;
  created_at: Date;
  order_link?: string;
  inbox_access_enabled?: boolean;
  enable_support: boolean;
  wa_address_form_id?: string;
  payment_webhook_secret?: string;
  restaurant_auto_accept?: boolean;
  restaurant_menu_type?: string;
  restaurant_emails?: string[];
  restaurant_email_password?: string;
  restaurant_sender_email?: string;
  subscription_graceperiod: number;
  restaurant_auto_out_for_delivery?: boolean;
  customer_review_link?: string;
  is_only_for_listing?: boolean;
}

export interface Branch {
  fk_restaurant_id: string;
  branch_id: string;
  branch_name: string;
  branch_address: string;
  phone_number: string;
  branch_timings?: TimingSlot[];
  status: boolean;
  created_at: Date;
  branch_logo?: string;
  branch_description?: string;
  branch_maps_url?: string;
  branch_category_order?: string[];
  branch_promotional_banner?: string[];
  branch_min_cart_amount?: number;
  branch_location?: Location;
  branch_preparation_time?: number;
  inbox_enabled?: boolean;
  branch_payment_modes?: string[];
  payment_methods?: PaymentMethod[];
  payment_webhook_secret?: string;
  auto_refundable?: boolean;
  trn_number?: number;
  pre_orders?: boolean;
  branch_emirate?: string;
  average_spend?: number;
  branch_tags?: string[];
  branch_display_name?: string;
  branch_timezone?: string;
  cancellation_number?: string;
  delivery_module?: boolean;
  break_timings?: TimingSlot[];
  break_status_switch?: boolean;
  break_override_until?: Date;
}

export interface Category {
  category_id: string;
  category_name: string;
  category_description?: string;
  category_availability: string;
  category_availability_timings?: TimingSlot[];
  status: boolean;
  created_at: Date;
  fk_branch_id?: string;
}

export interface Item {
  item_id: string;
  fk_branch_id?: string;
  item_name: string;
  item_description?: string;
  item_type: string;
  fk_category_id?: string;
  item_price: number;
  item_image_link?: string;
  item_status: string;
  item_variants?: any;
  created_at: Date;
  item_add_ons_group?: any;
  item_combos?: any;
}

// Shop-related interfaces
export interface Shop {
  shop_id: string;
  shop_name: string;
  shop_logo_url?: string;
  shop_bg_img_url?: string;
  shop_social_links?: SocialLinks;
  shop_cancellation_number?: string;
  shop_timings?: TimingSlot[];
  shop_whatsapp_id?: string;
  shop_whatsapp_token?: string;
  shop_whatsapp_app_id?: string;
  shop_business_account_id?: string;
  take_orders: boolean;
  phone_number: string;
  order_policy?: string[];
  payment_methods?: PaymentMethod[];
  stripe_subscription_id?: string;
  stripe_customer_id?: string;
  subscription_status: boolean;
  created_at: Date;
  order_link?: string;
  inbox_access_enabled?: boolean;
  enable_support: boolean;
  wa_address_form_id?: string;
  payment_webhook_secret?: string;
  shop_auto_accept?: boolean;
  shop_emails?: string[];
  shop_sender_email?: string;
  shop_email_password?: string;
  generate_quotation?: boolean;
  subscription_graceperiod: number;
  is_only_for_listing?: boolean;
}

export interface ShopBranch {
  fk_shop_id: string;
  branch_id: string;
  branch_name: string;
  branch_address: string;
  phone_number: string;
  branch_timings?: TimingSlot[];
  status: boolean;
  created_at: Date;
  branch_logo?: string;
  branch_description?: string;
  branch_maps_url?: string;
  branch_category_order?: string[];
  branch_promotional_banner?: string[];
  branch_min_cart_amount?: number;
  branch_location?: Location;
  inbox_enabled?: boolean;
  branch_payment_modes?: string[];
  payment_methods?: PaymentMethod[];
  payment_webhook_secret?: string;
  auto_refundable?: boolean;
  trn_number?: number;
  branch_emirate?: string;
  average_spend?: number;
  branch_tags?: string[];
  branch_display_name?: string;
  branch_timezone?: string;
  cancellation_number?: string;
  break_timings?: TimingSlot[];
  break_status_switch?: boolean;
  break_override_until?: Date;
}

export interface ShopCategory {
  category_id: string;
  category_name: string;
  category_description?: string;
  category_availability: string;
  category_availability_timings?: TimingSlot[];
  status: boolean;
  created_at: Date;
  fk_branch_id?: string;
}

export interface ShopItem {
  item_id: string;
  fk_branch_id?: string;
  item_name: string;
  item_description?: string;
  item_type: string;
  fk_category_id?: string;
  item_price: number;
  item_image_links?: string[];
  item_status: string;
  item_variants?: any;
  created_at: Date;
  item_add_ons_group?: any;
  item_quantity?: number;
  item_combos?: any;
}

// Vector search related interfaces
export interface VectorSearchResult {
  id: string;
  content: string;
  metadata: {
    type: 'club' | 'restaurant' | 'shop';
    name: string;
    category?: string;
    location?: string;
    tags?: string[];
  };
  similarity_score: number;
}

// Conversational AI interfaces
export interface ConversationQuery {
  message: string;
  context?: string;
  user_location?: Location;
  preferences?: {
    business_type?: 'club' | 'restaurant' | 'shop';
    price_range?: 'low' | 'medium' | 'high';
    cuisine_type?: string;
    sport_type?: string;
  };
}

export interface ConversationResponse {
  response: string;
  relevant_businesses: VectorSearchResult[];
  suggestions?: string[];
  context_id?: string;
}
