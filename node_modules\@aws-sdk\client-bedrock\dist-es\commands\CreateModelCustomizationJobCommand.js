import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { CreateModelCustomizationJobRequestFilterSensitiveLog, } from "../models/models_1";
import { de_CreateModelCustomizationJobCommand, se_CreateModelCustomizationJobCommand, } from "../protocols/Aws_restJson1";
export { $Command };
export class CreateModelCustomizationJobCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockControlPlaneService", "CreateModelCustomizationJob", {})
    .n("BedrockClient", "CreateModelCustomizationJobCommand")
    .f(CreateModelCustomizationJobRequestFilterSensitiveLog, void 0)
    .ser(se_CreateModelCustomizationJobCommand)
    .de(de_CreateModelCustomizationJobCommand)
    .build() {
}
