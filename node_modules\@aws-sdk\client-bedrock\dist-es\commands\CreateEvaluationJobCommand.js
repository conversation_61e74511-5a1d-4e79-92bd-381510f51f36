import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { CreateEvaluationJobRequestFilterSensitiveLog } from "../models/models_1";
import { de_CreateEvaluationJobCommand, se_CreateEvaluationJobCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class CreateEvaluationJobCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockControlPlaneService", "CreateEvaluationJob", {})
    .n("BedrockClient", "CreateEvaluationJobCommand")
    .f(CreateEvaluationJobRequestFilterSensitiveLog, void 0)
    .ser(se_CreateEvaluationJobCommand)
    .de(de_CreateEvaluationJobCommand)
    .build() {
}
