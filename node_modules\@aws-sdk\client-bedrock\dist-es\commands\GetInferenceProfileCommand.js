import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { GetInferenceProfileResponseFilterSensitiveLog, } from "../models/models_0";
import { de_GetInferenceProfileCommand, se_GetInferenceProfileCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class GetInferenceProfileCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockControlPlaneService", "GetInferenceProfile", {})
    .n("BedrockClient", "GetInferenceProfileCommand")
    .f(void 0, GetInferenceProfileResponseFilterSensitiveLog)
    .ser(se_GetInferenceProfileCommand)
    .de(de_GetInferenceProfileCommand)
    .build() {
}
