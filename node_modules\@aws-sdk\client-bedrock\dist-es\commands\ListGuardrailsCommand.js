import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { ListGuardrailsResponseFilterSensitiveLog, } from "../models/models_0";
import { de_ListGuardrailsCommand, se_ListGuardrailsCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class ListGuardrailsCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockControlPlaneService", "ListGuardrails", {})
    .n("BedrockClient", "ListGuardrailsCommand")
    .f(void 0, ListGuardrailsResponseFilterSensitiveLog)
    .ser(se_ListGuardrailsCommand)
    .de(de_ListGuardrailsCommand)
    .build() {
}
