import { Request, Response } from 'express';
import { conversationalAIService } from '../services/ConversationalAIService';
import { v4 as uuidv4 } from 'uuid';

/**
 * Interface for chat request
 */
interface ChatRequest {
  query: string;
  sessionId?: string;
  userId?: string;
  userPreferences?: {
    location?: string;
    priceRange?: { min?: number; max?: number };
    cuisineTypes?: string[];
    businessTypes?: string[];
  };
}

/**
 * Interface for chat response
 */
interface ChatResponse {
  success: boolean;
  data?: {
    response: string;
    sessionId: string;
    businessRecommendations: any[];
    confidence: number;
    suggestedFollowUps: string[];
    timestamp: string;
  };
  error?: string;
  timestamp: string;
}

/**
 * Controller for conversational AI bot functionality
 */
export class ConversationalAIController {
  
  /**
   * Handle chat requests from users
   */
  static async chat(req: Request, res: Response): Promise<void> {
    try {
      const { query, sessionId, userId, userPreferences }: ChatRequest = req.body;

      // Validate required fields
      if (!query || typeof query !== 'string' || query.trim().length === 0) {
        res.status(400).json({
          success: false,
          error: 'Query is required and must be a non-empty string',
          timestamp: new Date().toISOString()
        } as ChatResponse);
        return;
      }

      // Generate session ID if not provided
      const currentSessionId = sessionId || uuidv4();

      // Process the query through the conversational AI service
      const aiResponse = await conversationalAIService.processQuery(
        query.trim(),
        currentSessionId,
        userId,
        { userPreferences }
      );

      // Format business recommendations for response
      const formattedRecommendations = aiResponse.businessRecommendations.map(result => ({
        id: result.entity.id,
        name: result.entity.name,
        description: result.entity.description,
        type: result.entity_type,
        similarity_score: result.similarity_score,
        details: {
          phone_number: result.entity.phone_number,
          address: result.entity.branch_address || result.entity.club_location,
          emirate: result.entity.branch_emirate || result.entity.club_emirate,
          average_spend: result.entity.average_spend,
          tags: result.entity.branch_tags || result.entity.club_tags,
          logo: result.entity.branch_logo || result.entity.club_logo_url || result.entity.restaurant_logo_url,
          maps_url: result.entity.branch_maps_url,
          timings: result.entity.branch_timings || result.entity.club_timings,
          // Add specific fields based on entity type
          ...(result.entity_type === 'restaurant' && {
            menu_type: result.entity.restaurant_menu_type,
            takes_orders: result.entity.take_orders,
            order_link: result.entity.order_link
          }),
          ...(result.entity_type === 'club' && {
            takes_booking: result.entity.take_booking,
            booking_link: result.entity.booking_link
          }),
          ...(result.entity_type === 'food_item' && {
            price: result.entity.item_price,
            item_type: result.entity.item_type,
            restaurant_name: result.entity.restaurant_name
          }),
          ...(result.entity_type === 'facility' && {
            facility_type: result.entity.facility_type,
            facility_category: result.entity.facility_category,
            club_name: result.entity.club_name
          })
        }
      }));

      const response: ChatResponse = {
        success: true,
        data: {
          response: aiResponse.response,
          sessionId: currentSessionId,
          businessRecommendations: formattedRecommendations,
          confidence: aiResponse.confidence,
          suggestedFollowUps: aiResponse.suggestedFollowUps || [],
          timestamp: new Date().toISOString()
        },
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);

    } catch (error) {
      console.error('Error in chat controller:', error);
      
      const errorResponse: ChatResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        timestamp: new Date().toISOString()
      };

      res.status(500).json(errorResponse);
    }
  }

  /**
   * Get conversation context for a session
   */
  static async getContext(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;

      if (!sessionId) {
        res.status(400).json({
          success: false,
          error: 'Session ID is required',
          timestamp: new Date().toISOString()
        });
        return;
      }

      const context = conversationalAIService.getContext(sessionId);

      if (!context) {
        res.status(404).json({
          success: false,
          error: 'Session not found',
          timestamp: new Date().toISOString()
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: {
          sessionId: context.sessionId,
          userId: context.userId,
          previousQueries: context.previousQueries,
          userPreferences: context.userPreferences,
          queryCount: context.previousQueries.length
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error getting context:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Clear conversation context for a session
   */
  static async clearContext(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;

      if (!sessionId) {
        res.status(400).json({
          success: false,
          error: 'Session ID is required',
          timestamp: new Date().toISOString()
        });
        return;
      }

      conversationalAIService.clearContext(sessionId);

      res.status(200).json({
        success: true,
        message: 'Context cleared successfully',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error clearing context:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Update user preferences for a session
   */
  static async updatePreferences(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;
      const { preferences } = req.body;

      if (!sessionId) {
        res.status(400).json({
          success: false,
          error: 'Session ID is required',
          timestamp: new Date().toISOString()
        });
        return;
      }

      if (!preferences || typeof preferences !== 'object') {
        res.status(400).json({
          success: false,
          error: 'Preferences object is required',
          timestamp: new Date().toISOString()
        });
        return;
      }

      conversationalAIService.updateUserPreferences(sessionId, preferences);

      res.status(200).json({
        success: true,
        message: 'Preferences updated successfully',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error updating preferences:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Health check for AI services
   */
  static async healthCheck(req: Request, res: Response): Promise<void> {
    try {
      // Test a simple query to verify all services are working
      const testSessionId = 'health-check-' + Date.now();
      const testResponse = await conversationalAIService.processQuery(
        'Hello',
        testSessionId
      );

      // Clean up test session
      conversationalAIService.clearContext(testSessionId);

      res.status(200).json({
        success: true,
        message: 'Conversational AI services are healthy',
        data: {
          aiServiceStatus: 'operational',
          vectorSearchStatus: 'operational',
          bedrockStatus: 'operational',
          testResponseLength: testResponse.response.length,
          testConfidence: testResponse.confidence
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Health check failed:', error);
      res.status(503).json({
        success: false,
        error: 'One or more AI services are not available',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  }
}
