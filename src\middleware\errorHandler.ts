import { Request, Response, NextFunction } from 'express';

/**
 * Custom error class for API errors
 */
export class APIError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public code?: string;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true, code?: string) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.code = code;
    this.name = 'APIError';

    // Maintains proper stack trace for where our error was thrown
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Custom error classes for specific AI service errors
 */
export class BedrockError extends APIError {
  constructor(message: string, code?: string) {
    super(`AWS Bedrock Error: ${message}`, 503, true, code);
    this.name = 'BedrockError';
  }
}

export class VectorSearchError extends APIError {
  constructor(message: string, code?: string) {
    super(`Vector Search Error: ${message}`, 500, true, code);
    this.name = 'VectorSearchError';
  }
}

export class DatabaseError extends APIError {
  constructor(message: string, code?: string) {
    super(`Database Error: ${message}`, 500, true, code);
    this.name = 'DatabaseError';
  }
}

export class ValidationError extends APIError {
  constructor(message: string, field?: string) {
    super(`Validation Error: ${message}`, 400, true, field);
    this.name = 'ValidationError';
  }
}

export class RateLimitError extends APIError {
  constructor(message: string = 'Too many requests') {
    super(message, 429, true, 'RATE_LIMIT_EXCEEDED');
    this.name = 'RateLimitError';
  }
}

/**
 * Error response interface
 */
interface ErrorResponse {
  success: false;
  error: {
    message: string;
    code?: string;
    statusCode: number;
    timestamp: string;
    path?: string;
    method?: string;
    stack?: string;
  };
}

/**
 * Global error handler middleware
 */
export const errorHandler = (
  error: Error | APIError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Log the error
  console.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    path: req.path,
    method: req.method,
    body: req.body,
    query: req.query,
    params: req.params,
    timestamp: new Date().toISOString()
  });

  // Default error values
  let statusCode = 500;
  let message = 'Internal Server Error';
  let code: string | undefined;

  // Handle different error types
  if (error instanceof APIError) {
    statusCode = error.statusCode;
    message = error.message;
    code = error.code;
  } else if (error.name === 'ValidationError') {
    statusCode = 400;
    message = error.message;
    code = 'VALIDATION_ERROR';
  } else if (error.name === 'CastError') {
    statusCode = 400;
    message = 'Invalid ID format';
    code = 'INVALID_ID';
  } else if (error.name === 'MongoError' || error.name === 'MongoServerError') {
    statusCode = 500;
    message = 'Database operation failed';
    code = 'DATABASE_ERROR';
  } else if (error.message.includes('AWS')) {
    statusCode = 503;
    message = 'External service unavailable';
    code = 'SERVICE_UNAVAILABLE';
  }

  // Create error response
  const errorResponse: ErrorResponse = {
    success: false,
    error: {
      message,
      code,
      statusCode,
      timestamp: new Date().toISOString(),
      path: req.path,
      method: req.method
    }
  };

  // Include stack trace in development
  if (process.env.NODE_ENV === 'development') {
    errorResponse.error.stack = error.stack;
  }

  // Send error response
  res.status(statusCode).json(errorResponse);
};

/**
 * 404 handler for unmatched routes
 */
export const notFoundHandler = (req: Request, res: Response): void => {
  const errorResponse: ErrorResponse = {
    success: false,
    error: {
      message: `Route ${req.method} ${req.path} not found`,
      code: 'ROUTE_NOT_FOUND',
      statusCode: 404,
      timestamp: new Date().toISOString(),
      path: req.path,
      method: req.method
    }
  };

  res.status(404).json(errorResponse);
};

/**
 * Async error wrapper for route handlers
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Validation helper functions
 */
export const validateRequired = (value: any, fieldName: string): void => {
  if (value === undefined || value === null || value === '') {
    throw new ValidationError(`${fieldName} is required`, fieldName);
  }
};

export const validateString = (value: any, fieldName: string, minLength?: number, maxLength?: number): void => {
  validateRequired(value, fieldName);
  
  if (typeof value !== 'string') {
    throw new ValidationError(`${fieldName} must be a string`, fieldName);
  }
  
  if (minLength && value.length < minLength) {
    throw new ValidationError(`${fieldName} must be at least ${minLength} characters long`, fieldName);
  }
  
  if (maxLength && value.length > maxLength) {
    throw new ValidationError(`${fieldName} must be no more than ${maxLength} characters long`, fieldName);
  }
};

export const validateNumber = (value: any, fieldName: string, min?: number, max?: number): void => {
  if (value !== undefined && value !== null) {
    const numValue = Number(value);
    
    if (isNaN(numValue)) {
      throw new ValidationError(`${fieldName} must be a valid number`, fieldName);
    }
    
    if (min !== undefined && numValue < min) {
      throw new ValidationError(`${fieldName} must be at least ${min}`, fieldName);
    }
    
    if (max !== undefined && numValue > max) {
      throw new ValidationError(`${fieldName} must be no more than ${max}`, fieldName);
    }
  }
};

export const validateArray = (value: any, fieldName: string, minLength?: number, maxLength?: number): void => {
  if (value !== undefined && value !== null) {
    if (!Array.isArray(value)) {
      throw new ValidationError(`${fieldName} must be an array`, fieldName);
    }
    
    if (minLength && value.length < minLength) {
      throw new ValidationError(`${fieldName} must contain at least ${minLength} items`, fieldName);
    }
    
    if (maxLength && value.length > maxLength) {
      throw new ValidationError(`${fieldName} must contain no more than ${maxLength} items`, fieldName);
    }
  }
};

/**
 * Logger utility for consistent error logging
 */
export const logger = {
  error: (message: string, error?: Error, context?: any) => {
    console.error(`[ERROR] ${message}`, {
      error: error?.message,
      stack: error?.stack,
      context,
      timestamp: new Date().toISOString()
    });
  },
  
  warn: (message: string, context?: any) => {
    console.warn(`[WARN] ${message}`, {
      context,
      timestamp: new Date().toISOString()
    });
  },
  
  info: (message: string, context?: any) => {
    console.log(`[INFO] ${message}`, {
      context,
      timestamp: new Date().toISOString()
    });
  },
  
  debug: (message: string, context?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[DEBUG] ${message}`, {
        context,
        timestamp: new Date().toISOString()
      });
    }
  }
};
