import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { CreateInferenceProfileRequestFilterSensitiveLog, } from "../models/models_0";
import { de_CreateInferenceProfileCommand, se_CreateInferenceProfileCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class CreateInferenceProfileCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockControlPlaneService", "CreateInferenceProfile", {})
    .n("BedrockClient", "CreateInferenceProfileCommand")
    .f(CreateInferenceProfileRequestFilterSensitiveLog, void 0)
    .ser(se_CreateInferenceProfileCommand)
    .de(de_CreateInferenceProfileCommand)
    .build() {
}
