import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { GetGuardrailResponseFilterSensitiveLog } from "../models/models_0";
import { de_GetGuardrailCommand, se_GetGuardrailCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class GetGuardrailCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockControlPlaneService", "GetGuardrail", {})
    .n("BedrockClient", "GetGuardrailCommand")
    .f(void 0, GetGuardrailResponseFilterSensitiveLog)
    .ser(se_GetGuardrailCommand)
    .de(de_GetGuardrailCommand)
    .build() {
}
