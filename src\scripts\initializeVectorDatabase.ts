import { query, getClient } from '../config/database';
import { vectorSearchService } from '../services/VectorSearchService';
import { config } from '../config/environment';

/**
 * Database initialization script for vector search capabilities
 */
export class VectorDatabaseInitializer {

  /**
   * Initialize the database with vector extension and add embedding columns
   */
  static async initialize(): Promise<void> {
    console.log('🚀 Starting vector database initialization...');

    try {
      // Step 1: Enable pgvector extension
      await this.enablePgVectorExtension();

      // Step 2: Add embedding columns to existing tables
      await this.addEmbeddingColumns();

      // Step 3: Create vector indexes for better performance
      await this.createVectorIndexes();

      // Step 4: Generate embeddings for existing data (optional - can be run separately)
      // await this.generateEmbeddingsForExistingData();

      console.log('✅ Vector database initialization completed successfully!');
      console.log('📝 Next steps:');
      console.log('   1. Run generateEmbeddingsForExistingData() to create embeddings for existing business data');
      console.log('   2. Set up a cron job to update embeddings when data changes');
      console.log('   3. Monitor vector search performance and adjust similarity thresholds as needed');

    } catch (error) {
      console.error('❌ Vector database initialization failed:', error);
      throw error;
    }
  }

  /**
   * Enable pgvector extension
   */
  private static async enablePgVectorExtension(): Promise<void> {
    console.log('📦 Enabling pgvector extension...');

    try {
      await query('CREATE EXTENSION IF NOT EXISTS vector;');
      console.log('✅ pgvector extension enabled successfully');
    } catch (error) {
      console.error('❌ Failed to enable pgvector extension:', error);
      throw error;
    }
  }

  /**
   * Add embedding columns to business tables
   */
  private static async addEmbeddingColumns(): Promise<void> {
    console.log('🗃️  Adding embedding columns to tables...');

    const tables = [
      'Restaurants',
      'Branches',
      'Shops',
      'ShopBranches',
      'Clubs',
      'Items',
      'ShopItems',
      'Categories',
      'ShopCategories',
      'Facilities'
    ];

    const embeddingDimension = config.vectorSearch.embeddingDimensions;

    for (const table of tables) {
      try {
        // Check if embedding column already exists
        const columnExists = await query(`
          SELECT column_name 
          FROM information_schema.columns 
          WHERE table_name = $1 AND column_name = 'embedding'
        `, [table]);

        if (columnExists.rows.length === 0) {
          await query(`ALTER TABLE "${table}" ADD COLUMN embedding vector(${embeddingDimension});`);
          console.log(`✅ Added embedding column to ${table}`);
        } else {
          console.log(`ℹ️  Embedding column already exists in ${table}`);
        }
      } catch (error) {
        console.error(`❌ Failed to add embedding column to ${table}:`, error);
        // Continue with other tables even if one fails
      }
    }
  }

  /**
   * Create vector indexes for better search performance
   */
  private static async createVectorIndexes(): Promise<void> {
    console.log('🔍 Creating vector indexes...');

    const indexConfigurations = [
      { table: 'Restaurants', indexName: 'restaurants_embedding_idx' },
      { table: 'Branches', indexName: 'branches_embedding_idx' },
      { table: 'Shops', indexName: 'shops_embedding_idx' },
      { table: 'ShopBranches', indexName: 'shop_branches_embedding_idx' },
      { table: 'Clubs', indexName: 'clubs_embedding_idx' },
      { table: 'Items', indexName: 'items_embedding_idx' },
      { table: 'ShopItems', indexName: 'shop_items_embedding_idx' },
      { table: 'Categories', indexName: 'categories_embedding_idx' },
      { table: 'ShopCategories', indexName: 'shop_categories_embedding_idx' },
      { table: 'Facilities', indexName: 'facilities_embedding_idx' }
    ];

    for (const { table, indexName } of indexConfigurations) {
      try {
        // Check if index already exists
        const indexExists = await query(`
          SELECT indexname 
          FROM pg_indexes 
          WHERE tablename = $1 AND indexname = $2
        `, [table, indexName]);

        if (indexExists.rows.length === 0) {
          // Create HNSW index for approximate nearest neighbor search
          await query(`
            CREATE INDEX ${indexName} ON "${table}" 
            USING hnsw (embedding vector_cosine_ops)
            WITH (m = 16, ef_construction = 64);
          `);
          console.log(`✅ Created vector index ${indexName} on ${table}`);
        } else {
          console.log(`ℹ️  Vector index ${indexName} already exists on ${table}`);
        }
      } catch (error) {
        console.error(`❌ Failed to create vector index on ${table}:`, error);
        // Continue with other indexes even if one fails
      }
    }
  }

  /**
   * Generate embeddings for existing business data
   * This is a separate method that can be run independently
   */
  static async generateEmbeddingsForExistingData(): Promise<void> {
    console.log('🧠 Generating embeddings for existing business data...');
    console.log('⚠️  This process may take a while depending on the amount of data...');

    try {
      // Generate embeddings for restaurants
      await this.generateEmbeddingsForTable('Restaurants', 'restaurant');
      
      // Generate embeddings for restaurant branches
      await this.generateEmbeddingsForTable('Branches', 'branch');
      
      // Generate embeddings for shops
      await this.generateEmbeddingsForTable('Shops', 'shop');
      
      // Generate embeddings for shop branches
      await this.generateEmbeddingsForTable('ShopBranches', 'shop_branch');
      
      // Generate embeddings for clubs
      await this.generateEmbeddingsForTable('Clubs', 'club');
      
      // Generate embeddings for food items
      await this.generateEmbeddingsForTable('Items', 'food_item');
      
      // Generate embeddings for shop items
      await this.generateEmbeddingsForTable('ShopItems', 'shop_item');
      
      // Generate embeddings for facilities
      await this.generateEmbeddingsForTable('Facilities', 'facility');

      console.log('✅ Embedding generation completed for all tables!');

    } catch (error) {
      console.error('❌ Failed to generate embeddings:', error);
      throw error;
    }
  }

  /**
   * Generate embeddings for a specific table
   */
  private static async generateEmbeddingsForTable(tableName: string, entityType: string): Promise<void> {
    console.log(`📊 Processing ${tableName}...`);

    try {
      // Get all records without embeddings
      const result = await query(`
        SELECT * FROM "${tableName}" 
        WHERE embedding IS NULL 
        AND status = true
        ORDER BY created_at DESC
        LIMIT 100
      `);

      if (result.rows.length === 0) {
        console.log(`ℹ️  No records to process in ${tableName}`);
        return;
      }

      console.log(`📝 Found ${result.rows.length} records to process in ${tableName}`);

      // Process records in batches to avoid overwhelming the API
      const batchSize = 5;
      for (let i = 0; i < result.rows.length; i += batchSize) {
        const batch = result.rows.slice(i, i + batchSize);
        
        for (const record of batch) {
          try {
            // Generate embedding for this record
            const embedding = await vectorSearchService.createBusinessEmbedding(record, entityType);
            
            // Update the record with the embedding
            const idColumn = this.getIdColumn(tableName);
            await query(`
              UPDATE "${tableName}" 
              SET embedding = $1::vector 
              WHERE ${idColumn} = $2
            `, [JSON.stringify(embedding), record[idColumn]]);
            
            console.log(`✅ Generated embedding for ${record.name || record.restaurant_name || record.shop_name || record.club_name || record.item_name || record.facility_name || record.category_name}`);
            
          } catch (error) {
            console.error(`❌ Failed to generate embedding for record ${record.id}:`, error);
            // Continue with next record
          }
        }
        
        // Add delay between batches to respect API rate limits
        if (i + batchSize < result.rows.length) {
          console.log('⏳ Waiting before processing next batch...');
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }

      console.log(`✅ Completed processing ${tableName}`);

    } catch (error) {
      console.error(`❌ Failed to process ${tableName}:`, error);
      throw error;
    }
  }

  /**
   * Get the primary key column name for a table
   */
  private static getIdColumn(tableName: string): string {
    const idColumns: { [key: string]: string } = {
      'Restaurants': 'restaurant_id',
      'Branches': 'branch_id',
      'Shops': 'shop_id',
      'ShopBranches': 'branch_id',
      'Clubs': 'club_id',
      'Items': 'item_id',
      'ShopItems': 'item_id',
      'Categories': 'category_id',
      'ShopCategories': 'category_id',
      'Facilities': 'facility_id'
    };

    return idColumns[tableName] || 'id';
  }

  /**
   * Check vector database health
   */
  static async checkHealth(): Promise<void> {
    console.log('🏥 Checking vector database health...');

    try {
      // Check if pgvector extension is enabled
      const extensionResult = await query(`
        SELECT * FROM pg_extension WHERE extname = 'vector'
      `);

      if (extensionResult.rows.length === 0) {
        throw new Error('pgvector extension is not enabled');
      }

      console.log('✅ pgvector extension is enabled');

      // Check if embedding columns exist
      const tables = ['Restaurants', 'Branches', 'Shops', 'Clubs'];
      for (const table of tables) {
        const columnResult = await query(`
          SELECT column_name 
          FROM information_schema.columns 
          WHERE table_name = $1 AND column_name = 'embedding'
        `, [table]);

        if (columnResult.rows.length > 0) {
          console.log(`✅ Embedding column exists in ${table}`);
        } else {
          console.log(`⚠️  Embedding column missing in ${table}`);
        }
      }

      // Check if there are any embeddings
      const embeddingCount = await query(`
        SELECT COUNT(*) as count FROM "Restaurants" WHERE embedding IS NOT NULL
      `);

      console.log(`📊 Found ${embeddingCount.rows[0].count} restaurants with embeddings`);

      console.log('✅ Vector database health check completed');

    } catch (error) {
      console.error('❌ Vector database health check failed:', error);
      throw error;
    }
  }
}

// Export for use in other scripts
export default VectorDatabaseInitializer;
