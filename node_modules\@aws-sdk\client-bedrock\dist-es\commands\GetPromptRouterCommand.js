import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { GetPromptRouterResponseFilterSensitiveLog, } from "../models/models_0";
import { de_GetPromptRouterCommand, se_GetPromptRouterCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class GetPromptRouterCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockControlPlaneService", "GetPromptRouter", {})
    .n("BedrockClient", "GetPromptRouterCommand")
    .f(void 0, GetPromptRouterResponseFilterSensitiveLog)
    .ser(se_GetPromptRouterCommand)
    .de(de_GetPromptRouterCommand)
    .build() {
}
