import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { CreatePromptRouterRequestFilterSensitiveLog, } from "../models/models_0";
import { de_CreatePromptRouterCommand, se_CreatePromptRouterCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class CreatePromptRouterCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockControlPlaneService", "CreatePromptRouter", {})
    .n("BedrockClient", "CreatePromptRouterCommand")
    .f(CreatePromptRouterRequestFilterSensitiveLog, void 0)
    .ser(se_CreatePromptRouterCommand)
    .de(de_CreatePromptRouterCommand)
    .build() {
}
