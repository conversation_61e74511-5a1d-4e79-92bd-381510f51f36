import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { GetModelInvocationJobResponseFilterSensitiveLog, } from "../models/models_0";
import { de_GetModelInvocationJobCommand, se_GetModelInvocationJobCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class GetModelInvocationJobCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockControlPlaneService", "GetModelInvocationJob", {})
    .n("BedrockClient", "GetModelInvocationJobCommand")
    .f(void 0, GetModelInvocationJobResponseFilterSensitiveLog)
    .ser(se_GetModelInvocationJobCommand)
    .de(de_GetModelInvocationJobCommand)
    .build() {
}
