import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { UpdateGuardrailRequestFilterSensitiveLog, } from "../models/models_0";
import { de_UpdateGuardrailCommand, se_UpdateGuardrailCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class UpdateGuardrailCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockControlPlaneService", "UpdateGuardrail", {})
    .n("BedrockClient", "UpdateGuardrailCommand")
    .f(UpdateGuardrailRequestFilterSensitiveLog, void 0)
    .ser(se_UpdateGuardrailCommand)
    .de(de_UpdateGuardrailCommand)
    .build() {
}
