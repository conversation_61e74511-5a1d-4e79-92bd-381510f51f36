import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { BedrockClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes } from "../BedrockClient";
import { CreateEvaluationJobResponse } from "../models/models_0";
import { CreateEvaluationJobRequest } from "../models/models_1";
/**
 * @public
 */
export type { __MetadataBearer };
export { $Command };
/**
 * @public
 *
 * The input for {@link CreateEvaluationJobCommand}.
 */
export interface CreateEvaluationJobCommandInput extends CreateEvaluationJobRequest {
}
/**
 * @public
 *
 * The output of {@link CreateEvaluationJobCommand}.
 */
export interface CreateEvaluationJobCommandOutput extends CreateEvaluationJobResponse, __MetadataBearer {
}
declare const CreateEvaluationJobCommand_base: {
    new (input: CreateEvaluationJobCommandInput): import("@smithy/smithy-client").CommandImpl<CreateEvaluationJobCommandInput, CreateEvaluationJobCommandOutput, BedrockClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    new (input: CreateEvaluationJobCommandInput): import("@smithy/smithy-client").CommandImpl<CreateEvaluationJobCommandInput, CreateEvaluationJobCommandOutput, BedrockClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
/**
 * <p>Creates an evaluation job.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { BedrockClient, CreateEvaluationJobCommand } from "@aws-sdk/client-bedrock"; // ES Modules import
 * // const { BedrockClient, CreateEvaluationJobCommand } = require("@aws-sdk/client-bedrock"); // CommonJS import
 * const client = new BedrockClient(config);
 * const input = { // CreateEvaluationJobRequest
 *   jobName: "STRING_VALUE", // required
 *   jobDescription: "STRING_VALUE",
 *   clientRequestToken: "STRING_VALUE",
 *   roleArn: "STRING_VALUE", // required
 *   customerEncryptionKeyId: "STRING_VALUE",
 *   jobTags: [ // TagList
 *     { // Tag
 *       key: "STRING_VALUE", // required
 *       value: "STRING_VALUE", // required
 *     },
 *   ],
 *   applicationType: "ModelEvaluation" || "RagEvaluation",
 *   evaluationConfig: { // EvaluationConfig Union: only one key present
 *     automated: { // AutomatedEvaluationConfig
 *       datasetMetricConfigs: [ // EvaluationDatasetMetricConfigs // required
 *         { // EvaluationDatasetMetricConfig
 *           taskType: "Summarization" || "Classification" || "QuestionAndAnswer" || "Generation" || "Custom", // required
 *           dataset: { // EvaluationDataset
 *             name: "STRING_VALUE", // required
 *             datasetLocation: { // EvaluationDatasetLocation Union: only one key present
 *               s3Uri: "STRING_VALUE",
 *             },
 *           },
 *           metricNames: [ // EvaluationMetricNames // required
 *             "STRING_VALUE",
 *           ],
 *         },
 *       ],
 *       evaluatorModelConfig: { // EvaluatorModelConfig Union: only one key present
 *         bedrockEvaluatorModels: [ // BedrockEvaluatorModels
 *           { // BedrockEvaluatorModel
 *             modelIdentifier: "STRING_VALUE", // required
 *           },
 *         ],
 *       },
 *       customMetricConfig: { // AutomatedEvaluationCustomMetricConfig
 *         customMetrics: [ // AutomatedEvaluationCustomMetrics // required
 *           { // AutomatedEvaluationCustomMetricSource Union: only one key present
 *             customMetricDefinition: { // CustomMetricDefinition
 *               name: "STRING_VALUE", // required
 *               instructions: "STRING_VALUE", // required
 *               ratingScale: [ // RatingScale
 *                 { // RatingScaleItem
 *                   definition: "STRING_VALUE", // required
 *                   value: { // RatingScaleItemValue Union: only one key present
 *                     stringValue: "STRING_VALUE",
 *                     floatValue: Number("float"),
 *                   },
 *                 },
 *               ],
 *             },
 *           },
 *         ],
 *         evaluatorModelConfig: { // CustomMetricEvaluatorModelConfig
 *           bedrockEvaluatorModels: [ // CustomMetricBedrockEvaluatorModels // required
 *             { // CustomMetricBedrockEvaluatorModel
 *               modelIdentifier: "STRING_VALUE", // required
 *             },
 *           ],
 *         },
 *       },
 *     },
 *     human: { // HumanEvaluationConfig
 *       humanWorkflowConfig: { // HumanWorkflowConfig
 *         flowDefinitionArn: "STRING_VALUE", // required
 *         instructions: "STRING_VALUE",
 *       },
 *       customMetrics: [ // HumanEvaluationCustomMetrics
 *         { // HumanEvaluationCustomMetric
 *           name: "STRING_VALUE", // required
 *           description: "STRING_VALUE",
 *           ratingMethod: "STRING_VALUE", // required
 *         },
 *       ],
 *       datasetMetricConfigs: [ // required
 *         {
 *           taskType: "Summarization" || "Classification" || "QuestionAndAnswer" || "Generation" || "Custom", // required
 *           dataset: {
 *             name: "STRING_VALUE", // required
 *             datasetLocation: {//  Union: only one key present
 *               s3Uri: "STRING_VALUE",
 *             },
 *           },
 *           metricNames: [ // required
 *             "STRING_VALUE",
 *           ],
 *         },
 *       ],
 *     },
 *   },
 *   inferenceConfig: { // EvaluationInferenceConfig Union: only one key present
 *     models: [ // EvaluationModelConfigs
 *       { // EvaluationModelConfig Union: only one key present
 *         bedrockModel: { // EvaluationBedrockModel
 *           modelIdentifier: "STRING_VALUE", // required
 *           inferenceParams: "STRING_VALUE",
 *           performanceConfig: { // PerformanceConfiguration
 *             latency: "standard" || "optimized",
 *           },
 *         },
 *         precomputedInferenceSource: { // EvaluationPrecomputedInferenceSource
 *           inferenceSourceIdentifier: "STRING_VALUE", // required
 *         },
 *       },
 *     ],
 *     ragConfigs: [ // RagConfigs
 *       { // RAGConfig Union: only one key present
 *         knowledgeBaseConfig: { // KnowledgeBaseConfig Union: only one key present
 *           retrieveConfig: { // RetrieveConfig
 *             knowledgeBaseId: "STRING_VALUE", // required
 *             knowledgeBaseRetrievalConfiguration: { // KnowledgeBaseRetrievalConfiguration
 *               vectorSearchConfiguration: { // KnowledgeBaseVectorSearchConfiguration
 *                 numberOfResults: Number("int"),
 *                 overrideSearchType: "HYBRID" || "SEMANTIC",
 *                 filter: { // RetrievalFilter Union: only one key present
 *                   equals: { // FilterAttribute
 *                     key: "STRING_VALUE", // required
 *                     value: "DOCUMENT_VALUE", // required
 *                   },
 *                   notEquals: {
 *                     key: "STRING_VALUE", // required
 *                     value: "DOCUMENT_VALUE", // required
 *                   },
 *                   greaterThan: {
 *                     key: "STRING_VALUE", // required
 *                     value: "DOCUMENT_VALUE", // required
 *                   },
 *                   greaterThanOrEquals: {
 *                     key: "STRING_VALUE", // required
 *                     value: "DOCUMENT_VALUE", // required
 *                   },
 *                   lessThan: {
 *                     key: "STRING_VALUE", // required
 *                     value: "DOCUMENT_VALUE", // required
 *                   },
 *                   lessThanOrEquals: "<FilterAttribute>",
 *                   in: "<FilterAttribute>",
 *                   notIn: "<FilterAttribute>",
 *                   startsWith: "<FilterAttribute>",
 *                   listContains: "<FilterAttribute>",
 *                   stringContains: "<FilterAttribute>",
 *                   andAll: [ // RetrievalFilterList
 *                     {//  Union: only one key present
 *                       equals: "<FilterAttribute>",
 *                       notEquals: "<FilterAttribute>",
 *                       greaterThan: "<FilterAttribute>",
 *                       greaterThanOrEquals: "<FilterAttribute>",
 *                       lessThan: "<FilterAttribute>",
 *                       lessThanOrEquals: "<FilterAttribute>",
 *                       in: "<FilterAttribute>",
 *                       notIn: "<FilterAttribute>",
 *                       startsWith: "<FilterAttribute>",
 *                       listContains: "<FilterAttribute>",
 *                       stringContains: "<FilterAttribute>",
 *                       andAll: [
 *                         "<RetrievalFilter>",
 *                       ],
 *                       orAll: [
 *                         "<RetrievalFilter>",
 *                       ],
 *                     },
 *                   ],
 *                   orAll: [
 *                     "<RetrievalFilter>",
 *                   ],
 *                 },
 *                 implicitFilterConfiguration: { // ImplicitFilterConfiguration
 *                   metadataAttributes: [ // MetadataAttributeSchemaList // required
 *                     { // MetadataAttributeSchema
 *                       key: "STRING_VALUE", // required
 *                       type: "STRING" || "NUMBER" || "BOOLEAN" || "STRING_LIST", // required
 *                       description: "STRING_VALUE", // required
 *                     },
 *                   ],
 *                   modelArn: "STRING_VALUE", // required
 *                 },
 *                 rerankingConfiguration: { // VectorSearchRerankingConfiguration
 *                   type: "BEDROCK_RERANKING_MODEL", // required
 *                   bedrockRerankingConfiguration: { // VectorSearchBedrockRerankingConfiguration
 *                     modelConfiguration: { // VectorSearchBedrockRerankingModelConfiguration
 *                       modelArn: "STRING_VALUE", // required
 *                       additionalModelRequestFields: { // AdditionalModelRequestFields
 *                         "<keys>": "DOCUMENT_VALUE",
 *                       },
 *                     },
 *                     numberOfRerankedResults: Number("int"),
 *                     metadataConfiguration: { // MetadataConfigurationForReranking
 *                       selectionMode: "SELECTIVE" || "ALL", // required
 *                       selectiveModeConfiguration: { // RerankingMetadataSelectiveModeConfiguration Union: only one key present
 *                         fieldsToInclude: [ // FieldsForReranking
 *                           { // FieldForReranking
 *                             fieldName: "STRING_VALUE", // required
 *                           },
 *                         ],
 *                         fieldsToExclude: [
 *                           {
 *                             fieldName: "STRING_VALUE", // required
 *                           },
 *                         ],
 *                       },
 *                     },
 *                   },
 *                 },
 *               },
 *             },
 *           },
 *           retrieveAndGenerateConfig: { // RetrieveAndGenerateConfiguration
 *             type: "KNOWLEDGE_BASE" || "EXTERNAL_SOURCES", // required
 *             knowledgeBaseConfiguration: { // KnowledgeBaseRetrieveAndGenerateConfiguration
 *               knowledgeBaseId: "STRING_VALUE", // required
 *               modelArn: "STRING_VALUE", // required
 *               retrievalConfiguration: {
 *                 vectorSearchConfiguration: {
 *                   numberOfResults: Number("int"),
 *                   overrideSearchType: "HYBRID" || "SEMANTIC",
 *                   filter: "<RetrievalFilter>",
 *                   implicitFilterConfiguration: {
 *                     metadataAttributes: [ // required
 *                       {
 *                         key: "STRING_VALUE", // required
 *                         type: "STRING" || "NUMBER" || "BOOLEAN" || "STRING_LIST", // required
 *                         description: "STRING_VALUE", // required
 *                       },
 *                     ],
 *                     modelArn: "STRING_VALUE", // required
 *                   },
 *                   rerankingConfiguration: {
 *                     type: "BEDROCK_RERANKING_MODEL", // required
 *                     bedrockRerankingConfiguration: {
 *                       modelConfiguration: {
 *                         modelArn: "STRING_VALUE", // required
 *                         additionalModelRequestFields: {
 *                           "<keys>": "DOCUMENT_VALUE",
 *                         },
 *                       },
 *                       numberOfRerankedResults: Number("int"),
 *                       metadataConfiguration: {
 *                         selectionMode: "SELECTIVE" || "ALL", // required
 *                         selectiveModeConfiguration: {//  Union: only one key present
 *                           fieldsToInclude: [
 *                             {
 *                               fieldName: "STRING_VALUE", // required
 *                             },
 *                           ],
 *                           fieldsToExclude: [
 *                             {
 *                               fieldName: "STRING_VALUE", // required
 *                             },
 *                           ],
 *                         },
 *                       },
 *                     },
 *                   },
 *                 },
 *               },
 *               generationConfiguration: { // GenerationConfiguration
 *                 promptTemplate: { // PromptTemplate
 *                   textPromptTemplate: "STRING_VALUE",
 *                 },
 *                 guardrailConfiguration: { // GuardrailConfiguration
 *                   guardrailId: "STRING_VALUE", // required
 *                   guardrailVersion: "STRING_VALUE", // required
 *                 },
 *                 kbInferenceConfig: { // KbInferenceConfig
 *                   textInferenceConfig: { // TextInferenceConfig
 *                     temperature: Number("float"),
 *                     topP: Number("float"),
 *                     maxTokens: Number("int"),
 *                     stopSequences: [ // RAGStopSequences
 *                       "STRING_VALUE",
 *                     ],
 *                   },
 *                 },
 *                 additionalModelRequestFields: "<AdditionalModelRequestFields>",
 *               },
 *               orchestrationConfiguration: { // OrchestrationConfiguration
 *                 queryTransformationConfiguration: { // QueryTransformationConfiguration
 *                   type: "QUERY_DECOMPOSITION", // required
 *                 },
 *               },
 *             },
 *             externalSourcesConfiguration: { // ExternalSourcesRetrieveAndGenerateConfiguration
 *               modelArn: "STRING_VALUE", // required
 *               sources: [ // ExternalSources // required
 *                 { // ExternalSource
 *                   sourceType: "S3" || "BYTE_CONTENT", // required
 *                   s3Location: { // S3ObjectDoc
 *                     uri: "STRING_VALUE", // required
 *                   },
 *                   byteContent: { // ByteContentDoc
 *                     identifier: "STRING_VALUE", // required
 *                     contentType: "STRING_VALUE", // required
 *                     data: new Uint8Array(), // e.g. Buffer.from("") or new TextEncoder().encode("")                     // required
 *                   },
 *                 },
 *               ],
 *               generationConfiguration: { // ExternalSourcesGenerationConfiguration
 *                 promptTemplate: {
 *                   textPromptTemplate: "STRING_VALUE",
 *                 },
 *                 guardrailConfiguration: {
 *                   guardrailId: "STRING_VALUE", // required
 *                   guardrailVersion: "STRING_VALUE", // required
 *                 },
 *                 kbInferenceConfig: {
 *                   textInferenceConfig: {
 *                     temperature: Number("float"),
 *                     topP: Number("float"),
 *                     maxTokens: Number("int"),
 *                     stopSequences: [
 *                       "STRING_VALUE",
 *                     ],
 *                   },
 *                 },
 *                 additionalModelRequestFields: "<AdditionalModelRequestFields>",
 *               },
 *             },
 *           },
 *         },
 *         precomputedRagSourceConfig: { // EvaluationPrecomputedRagSourceConfig Union: only one key present
 *           retrieveSourceConfig: { // EvaluationPrecomputedRetrieveSourceConfig
 *             ragSourceIdentifier: "STRING_VALUE", // required
 *           },
 *           retrieveAndGenerateSourceConfig: { // EvaluationPrecomputedRetrieveAndGenerateSourceConfig
 *             ragSourceIdentifier: "STRING_VALUE", // required
 *           },
 *         },
 *       },
 *     ],
 *   },
 *   outputDataConfig: { // EvaluationOutputDataConfig
 *     s3Uri: "STRING_VALUE", // required
 *   },
 * };
 * const command = new CreateEvaluationJobCommand(input);
 * const response = await client.send(command);
 * // { // CreateEvaluationJobResponse
 * //   jobArn: "STRING_VALUE", // required
 * // };
 *
 * ```
 *
 * @param CreateEvaluationJobCommandInput - {@link CreateEvaluationJobCommandInput}
 * @returns {@link CreateEvaluationJobCommandOutput}
 * @see {@link CreateEvaluationJobCommandInput} for command's `input` shape.
 * @see {@link CreateEvaluationJobCommandOutput} for command's `response` shape.
 * @see {@link BedrockClientResolvedConfig | config} for BedrockClient's `config` shape.
 *
 * @throws {@link AccessDeniedException} (client fault)
 *  <p>The request is denied because of missing access permissions.</p>
 *
 * @throws {@link ConflictException} (client fault)
 *  <p>Error occurred because of a conflict while performing an operation.</p>
 *
 * @throws {@link InternalServerException} (server fault)
 *  <p>An internal server error occurred. Retry your request.</p>
 *
 * @throws {@link ResourceNotFoundException} (client fault)
 *  <p>The specified resource Amazon Resource Name (ARN) was not found. Check the Amazon Resource Name (ARN) and try your request again.</p>
 *
 * @throws {@link ServiceQuotaExceededException} (client fault)
 *  <p>The number of requests exceeds the service quota. Resubmit your request later.</p>
 *
 * @throws {@link ThrottlingException} (client fault)
 *  <p>The number of requests exceeds the limit. Resubmit your request later.</p>
 *
 * @throws {@link ValidationException} (client fault)
 *  <p>Input validation failed. Check your request parameters and retry the request.</p>
 *
 * @throws {@link BedrockServiceException}
 * <p>Base exception class for all service exceptions from Bedrock service.</p>
 *
 *
 * @public
 */
export declare class CreateEvaluationJobCommand extends CreateEvaluationJobCommand_base {
    /** @internal type navigation helper, not in runtime. */
    protected static __types: {
        api: {
            input: CreateEvaluationJobRequest;
            output: CreateEvaluationJobResponse;
        };
        sdk: {
            input: CreateEvaluationJobCommandInput;
            output: CreateEvaluationJobCommandOutput;
        };
    };
}
