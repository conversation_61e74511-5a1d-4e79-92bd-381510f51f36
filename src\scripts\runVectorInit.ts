#!/usr/bin/env ts-node

import { VectorDatabaseInitializer } from './initializeVectorDatabase';
import { initDatabase } from '../config/database';

/**
 * CLI script to initialize vector database
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'init';

  console.log('🤖 Cravin Concierge Vector Database Setup');
  console.log('==========================================');

  try {
    // Initialize database connection
    await initDatabase();

    switch (command) {
      case 'init':
        console.log('🚀 Running full vector database initialization...');
        await VectorDatabaseInitializer.initialize();
        break;

      case 'embeddings':
        console.log('🧠 Generating embeddings for existing data...');
        await VectorDatabaseInitializer.generateEmbeddingsForExistingData();
        break;

      case 'health':
        console.log('🏥 Checking vector database health...');
        await VectorDatabaseInitializer.checkHealth();
        break;

      case 'help':
      default:
        console.log('Available commands:');
        console.log('  init       - Initialize vector database (default)');
        console.log('  embeddings - Generate embeddings for existing data');
        console.log('  health     - Check vector database health');
        console.log('  help       - Show this help message');
        console.log('');
        console.log('Usage: npm run vector-init [command]');
        console.log('   or: ts-node src/scripts/runVectorInit.ts [command]');
        break;
    }

    console.log('');
    console.log('✅ Script completed successfully!');
    process.exit(0);

  } catch (error) {
    console.error('');
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}
