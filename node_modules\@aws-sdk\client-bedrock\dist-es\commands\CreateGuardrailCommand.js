import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { CreateGuardrailRequestFilterSensitiveLog, } from "../models/models_0";
import { de_CreateGuardrailCommand, se_CreateGuardrailCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class CreateGuardrailCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockControlPlaneService", "CreateGuardrail", {})
    .n("BedrockClient", "CreateGuardrailCommand")
    .f(CreateGuardrailRequestFilterSensitiveLog, void 0)
    .ser(se_CreateGuardrailCommand)
    .de(de_CreateGuardrailCommand)
    .build() {
}
