import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { GetModelCustomizationJobResponseFilterSensitiveLog, } from "../models/models_1";
import { de_GetModelCustomizationJobCommand, se_GetModelCustomizationJobCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class GetModelCustomizationJobCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockControlPlaneService", "GetModelCustomizationJob", {})
    .n("BedrockClient", "GetModelCustomizationJobCommand")
    .f(void 0, GetModelCustomizationJobResponseFilterSensitiveLog)
    .ser(se_GetModelCustomizationJobCommand)
    .de(de_GetModelCustomizationJobCommand)
    .build() {
}
