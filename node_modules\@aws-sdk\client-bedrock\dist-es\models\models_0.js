import { SENSITIVE_STRING } from "@smithy/smithy-client";
import { BedrockServiceException as __BaseException } from "./BedrockServiceException";
export class AccessDeniedException extends __BaseException {
    name = "AccessDeniedException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "AccessDeniedException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, AccessDeniedException.prototype);
    }
}
export const AgreementStatus = {
    AVAILABLE: "AVAILABLE",
    ERROR: "ERROR",
    NOT_AVAILABLE: "NOT_AVAILABLE",
    PENDING: "PENDING",
};
export class InternalServerException extends __BaseException {
    name = "InternalServerException";
    $fault = "server";
    constructor(opts) {
        super({
            name: "InternalServerException",
            $fault: "server",
            ...opts,
        });
        Object.setPrototypeOf(this, InternalServerException.prototype);
    }
}
export class ResourceNotFoundException extends __BaseException {
    name = "ResourceNotFoundException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "ResourceNotFoundException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, ResourceNotFoundException.prototype);
    }
}
export class ThrottlingException extends __BaseException {
    name = "ThrottlingException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "ThrottlingException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, ThrottlingException.prototype);
    }
}
export class ValidationException extends __BaseException {
    name = "ValidationException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "ValidationException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, ValidationException.prototype);
    }
}
export class ConflictException extends __BaseException {
    name = "ConflictException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "ConflictException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, ConflictException.prototype);
    }
}
export var EndpointConfig;
(function (EndpointConfig) {
    EndpointConfig.visit = (value, visitor) => {
        if (value.sageMaker !== undefined)
            return visitor.sageMaker(value.sageMaker);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(EndpointConfig || (EndpointConfig = {}));
export const Status = {
    INCOMPATIBLE_ENDPOINT: "INCOMPATIBLE_ENDPOINT",
    REGISTERED: "REGISTERED",
};
export class ServiceQuotaExceededException extends __BaseException {
    name = "ServiceQuotaExceededException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "ServiceQuotaExceededException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, ServiceQuotaExceededException.prototype);
    }
}
export class ServiceUnavailableException extends __BaseException {
    name = "ServiceUnavailableException";
    $fault = "server";
    constructor(opts) {
        super({
            name: "ServiceUnavailableException",
            $fault: "server",
            ...opts,
        });
        Object.setPrototypeOf(this, ServiceUnavailableException.prototype);
    }
}
export var ModelDataSource;
(function (ModelDataSource) {
    ModelDataSource.visit = (value, visitor) => {
        if (value.s3DataSource !== undefined)
            return visitor.s3DataSource(value.s3DataSource);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(ModelDataSource || (ModelDataSource = {}));
export class TooManyTagsException extends __BaseException {
    name = "TooManyTagsException";
    $fault = "client";
    resourceName;
    constructor(opts) {
        super({
            name: "TooManyTagsException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, TooManyTagsException.prototype);
        this.resourceName = opts.resourceName;
    }
}
export var CustomizationConfig;
(function (CustomizationConfig) {
    CustomizationConfig.visit = (value, visitor) => {
        if (value.distillationConfig !== undefined)
            return visitor.distillationConfig(value.distillationConfig);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(CustomizationConfig || (CustomizationConfig = {}));
export const CustomizationType = {
    CONTINUED_PRE_TRAINING: "CONTINUED_PRE_TRAINING",
    DISTILLATION: "DISTILLATION",
    FINE_TUNING: "FINE_TUNING",
    IMPORTED: "IMPORTED",
};
export const ModelStatus = {
    ACTIVE: "Active",
    CREATING: "Creating",
    FAILED: "Failed",
};
export var InvocationLogSource;
(function (InvocationLogSource) {
    InvocationLogSource.visit = (value, visitor) => {
        if (value.s3Uri !== undefined)
            return visitor.s3Uri(value.s3Uri);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(InvocationLogSource || (InvocationLogSource = {}));
export var RequestMetadataFilters;
(function (RequestMetadataFilters) {
    RequestMetadataFilters.visit = (value, visitor) => {
        if (value.equals !== undefined)
            return visitor.equals(value.equals);
        if (value.notEquals !== undefined)
            return visitor.notEquals(value.notEquals);
        if (value.andAll !== undefined)
            return visitor.andAll(value.andAll);
        if (value.orAll !== undefined)
            return visitor.orAll(value.orAll);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(RequestMetadataFilters || (RequestMetadataFilters = {}));
export const SortModelsBy = {
    CREATION_TIME: "CreationTime",
};
export const SortOrder = {
    ASCENDING: "Ascending",
    DESCENDING: "Descending",
};
export const EvaluationJobStatus = {
    COMPLETED: "Completed",
    DELETING: "Deleting",
    FAILED: "Failed",
    IN_PROGRESS: "InProgress",
    STOPPED: "Stopped",
    STOPPING: "Stopping",
};
export const ApplicationType = {
    MODEL_EVALUATION: "ModelEvaluation",
    RAG_EVALUATION: "RagEvaluation",
};
export var RatingScaleItemValue;
(function (RatingScaleItemValue) {
    RatingScaleItemValue.visit = (value, visitor) => {
        if (value.stringValue !== undefined)
            return visitor.stringValue(value.stringValue);
        if (value.floatValue !== undefined)
            return visitor.floatValue(value.floatValue);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(RatingScaleItemValue || (RatingScaleItemValue = {}));
export var AutomatedEvaluationCustomMetricSource;
(function (AutomatedEvaluationCustomMetricSource) {
    AutomatedEvaluationCustomMetricSource.visit = (value, visitor) => {
        if (value.customMetricDefinition !== undefined)
            return visitor.customMetricDefinition(value.customMetricDefinition);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(AutomatedEvaluationCustomMetricSource || (AutomatedEvaluationCustomMetricSource = {}));
export var EvaluationDatasetLocation;
(function (EvaluationDatasetLocation) {
    EvaluationDatasetLocation.visit = (value, visitor) => {
        if (value.s3Uri !== undefined)
            return visitor.s3Uri(value.s3Uri);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(EvaluationDatasetLocation || (EvaluationDatasetLocation = {}));
export const EvaluationTaskType = {
    CLASSIFICATION: "Classification",
    CUSTOM: "Custom",
    GENERATION: "Generation",
    QUESTION_AND_ANSWER: "QuestionAndAnswer",
    SUMMARIZATION: "Summarization",
};
export var EvaluatorModelConfig;
(function (EvaluatorModelConfig) {
    EvaluatorModelConfig.visit = (value, visitor) => {
        if (value.bedrockEvaluatorModels !== undefined)
            return visitor.bedrockEvaluatorModels(value.bedrockEvaluatorModels);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(EvaluatorModelConfig || (EvaluatorModelConfig = {}));
export var EvaluationConfig;
(function (EvaluationConfig) {
    EvaluationConfig.visit = (value, visitor) => {
        if (value.automated !== undefined)
            return visitor.automated(value.automated);
        if (value.human !== undefined)
            return visitor.human(value.human);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(EvaluationConfig || (EvaluationConfig = {}));
export const PerformanceConfigLatency = {
    OPTIMIZED: "optimized",
    STANDARD: "standard",
};
export var EvaluationModelConfig;
(function (EvaluationModelConfig) {
    EvaluationModelConfig.visit = (value, visitor) => {
        if (value.bedrockModel !== undefined)
            return visitor.bedrockModel(value.bedrockModel);
        if (value.precomputedInferenceSource !== undefined)
            return visitor.precomputedInferenceSource(value.precomputedInferenceSource);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(EvaluationModelConfig || (EvaluationModelConfig = {}));
export const ExternalSourceType = {
    BYTE_CONTENT: "BYTE_CONTENT",
    S3: "S3",
};
export const QueryTransformationType = {
    QUERY_DECOMPOSITION: "QUERY_DECOMPOSITION",
};
export const AttributeType = {
    BOOLEAN: "BOOLEAN",
    NUMBER: "NUMBER",
    STRING: "STRING",
    STRING_LIST: "STRING_LIST",
};
export const SearchType = {
    HYBRID: "HYBRID",
    SEMANTIC: "SEMANTIC",
};
export const RerankingMetadataSelectionMode = {
    ALL: "ALL",
    SELECTIVE: "SELECTIVE",
};
export var RerankingMetadataSelectiveModeConfiguration;
(function (RerankingMetadataSelectiveModeConfiguration) {
    RerankingMetadataSelectiveModeConfiguration.visit = (value, visitor) => {
        if (value.fieldsToInclude !== undefined)
            return visitor.fieldsToInclude(value.fieldsToInclude);
        if (value.fieldsToExclude !== undefined)
            return visitor.fieldsToExclude(value.fieldsToExclude);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(RerankingMetadataSelectiveModeConfiguration || (RerankingMetadataSelectiveModeConfiguration = {}));
export const VectorSearchRerankingConfigurationType = {
    BEDROCK_RERANKING_MODEL: "BEDROCK_RERANKING_MODEL",
};
export const RetrieveAndGenerateType = {
    EXTERNAL_SOURCES: "EXTERNAL_SOURCES",
    KNOWLEDGE_BASE: "KNOWLEDGE_BASE",
};
export var EvaluationPrecomputedRagSourceConfig;
(function (EvaluationPrecomputedRagSourceConfig) {
    EvaluationPrecomputedRagSourceConfig.visit = (value, visitor) => {
        if (value.retrieveSourceConfig !== undefined)
            return visitor.retrieveSourceConfig(value.retrieveSourceConfig);
        if (value.retrieveAndGenerateSourceConfig !== undefined)
            return visitor.retrieveAndGenerateSourceConfig(value.retrieveAndGenerateSourceConfig);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(EvaluationPrecomputedRagSourceConfig || (EvaluationPrecomputedRagSourceConfig = {}));
export const EvaluationJobType = {
    AUTOMATED: "Automated",
    HUMAN: "Human",
};
export const SortJobsBy = {
    CREATION_TIME: "CreationTime",
};
export const GuardrailContentFilterAction = {
    BLOCK: "BLOCK",
    NONE: "NONE",
};
export const GuardrailModality = {
    IMAGE: "IMAGE",
    TEXT: "TEXT",
};
export const GuardrailFilterStrength = {
    HIGH: "HIGH",
    LOW: "LOW",
    MEDIUM: "MEDIUM",
    NONE: "NONE",
};
export const GuardrailContentFilterType = {
    HATE: "HATE",
    INSULTS: "INSULTS",
    MISCONDUCT: "MISCONDUCT",
    PROMPT_ATTACK: "PROMPT_ATTACK",
    SEXUAL: "SEXUAL",
    VIOLENCE: "VIOLENCE",
};
export const GuardrailContentFiltersTierName = {
    CLASSIC: "CLASSIC",
    STANDARD: "STANDARD",
};
export const GuardrailContextualGroundingAction = {
    BLOCK: "BLOCK",
    NONE: "NONE",
};
export const GuardrailContextualGroundingFilterType = {
    GROUNDING: "GROUNDING",
    RELEVANCE: "RELEVANCE",
};
export const GuardrailSensitiveInformationAction = {
    ANONYMIZE: "ANONYMIZE",
    BLOCK: "BLOCK",
    NONE: "NONE",
};
export const GuardrailPiiEntityType = {
    ADDRESS: "ADDRESS",
    AGE: "AGE",
    AWS_ACCESS_KEY: "AWS_ACCESS_KEY",
    AWS_SECRET_KEY: "AWS_SECRET_KEY",
    CA_HEALTH_NUMBER: "CA_HEALTH_NUMBER",
    CA_SOCIAL_INSURANCE_NUMBER: "CA_SOCIAL_INSURANCE_NUMBER",
    CREDIT_DEBIT_CARD_CVV: "CREDIT_DEBIT_CARD_CVV",
    CREDIT_DEBIT_CARD_EXPIRY: "CREDIT_DEBIT_CARD_EXPIRY",
    CREDIT_DEBIT_CARD_NUMBER: "CREDIT_DEBIT_CARD_NUMBER",
    DRIVER_ID: "DRIVER_ID",
    EMAIL: "EMAIL",
    INTERNATIONAL_BANK_ACCOUNT_NUMBER: "INTERNATIONAL_BANK_ACCOUNT_NUMBER",
    IP_ADDRESS: "IP_ADDRESS",
    LICENSE_PLATE: "LICENSE_PLATE",
    MAC_ADDRESS: "MAC_ADDRESS",
    NAME: "NAME",
    PASSWORD: "PASSWORD",
    PHONE: "PHONE",
    PIN: "PIN",
    SWIFT_CODE: "SWIFT_CODE",
    UK_NATIONAL_HEALTH_SERVICE_NUMBER: "UK_NATIONAL_HEALTH_SERVICE_NUMBER",
    UK_NATIONAL_INSURANCE_NUMBER: "UK_NATIONAL_INSURANCE_NUMBER",
    UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER: "UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER",
    URL: "URL",
    USERNAME: "USERNAME",
    US_BANK_ACCOUNT_NUMBER: "US_BANK_ACCOUNT_NUMBER",
    US_BANK_ROUTING_NUMBER: "US_BANK_ROUTING_NUMBER",
    US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER: "US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER",
    US_PASSPORT_NUMBER: "US_PASSPORT_NUMBER",
    US_SOCIAL_SECURITY_NUMBER: "US_SOCIAL_SECURITY_NUMBER",
    VEHICLE_IDENTIFICATION_NUMBER: "VEHICLE_IDENTIFICATION_NUMBER",
};
export const GuardrailTopicsTierName = {
    CLASSIC: "CLASSIC",
    STANDARD: "STANDARD",
};
export const GuardrailTopicAction = {
    BLOCK: "BLOCK",
    NONE: "NONE",
};
export const GuardrailTopicType = {
    DENY: "DENY",
};
export const GuardrailWordAction = {
    BLOCK: "BLOCK",
    NONE: "NONE",
};
export const GuardrailManagedWordsType = {
    PROFANITY: "PROFANITY",
};
export const GuardrailStatus = {
    CREATING: "CREATING",
    DELETING: "DELETING",
    FAILED: "FAILED",
    READY: "READY",
    UPDATING: "UPDATING",
    VERSIONING: "VERSIONING",
};
export var InferenceProfileModelSource;
(function (InferenceProfileModelSource) {
    InferenceProfileModelSource.visit = (value, visitor) => {
        if (value.copyFrom !== undefined)
            return visitor.copyFrom(value.copyFrom);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(InferenceProfileModelSource || (InferenceProfileModelSource = {}));
export const InferenceProfileStatus = {
    ACTIVE: "ACTIVE",
};
export const InferenceProfileType = {
    APPLICATION: "APPLICATION",
    SYSTEM_DEFINED: "SYSTEM_DEFINED",
};
export const ModelCopyJobStatus = {
    COMPLETED: "Completed",
    FAILED: "Failed",
    IN_PROGRESS: "InProgress",
};
export const ModelImportJobStatus = {
    COMPLETED: "Completed",
    FAILED: "Failed",
    IN_PROGRESS: "InProgress",
};
export const S3InputFormat = {
    JSONL: "JSONL",
};
export var ModelInvocationJobInputDataConfig;
(function (ModelInvocationJobInputDataConfig) {
    ModelInvocationJobInputDataConfig.visit = (value, visitor) => {
        if (value.s3InputDataConfig !== undefined)
            return visitor.s3InputDataConfig(value.s3InputDataConfig);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(ModelInvocationJobInputDataConfig || (ModelInvocationJobInputDataConfig = {}));
export var ModelInvocationJobOutputDataConfig;
(function (ModelInvocationJobOutputDataConfig) {
    ModelInvocationJobOutputDataConfig.visit = (value, visitor) => {
        if (value.s3OutputDataConfig !== undefined)
            return visitor.s3OutputDataConfig(value.s3OutputDataConfig);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(ModelInvocationJobOutputDataConfig || (ModelInvocationJobOutputDataConfig = {}));
export const ModelInvocationJobStatus = {
    COMPLETED: "Completed",
    EXPIRED: "Expired",
    FAILED: "Failed",
    IN_PROGRESS: "InProgress",
    PARTIALLY_COMPLETED: "PartiallyCompleted",
    SCHEDULED: "Scheduled",
    STOPPED: "Stopped",
    STOPPING: "Stopping",
    SUBMITTED: "Submitted",
    VALIDATING: "Validating",
};
export const ModelCustomization = {
    CONTINUED_PRE_TRAINING: "CONTINUED_PRE_TRAINING",
    DISTILLATION: "DISTILLATION",
    FINE_TUNING: "FINE_TUNING",
};
export const InferenceType = {
    ON_DEMAND: "ON_DEMAND",
    PROVISIONED: "PROVISIONED",
};
export const ModelModality = {
    EMBEDDING: "EMBEDDING",
    IMAGE: "IMAGE",
    TEXT: "TEXT",
};
export const FoundationModelLifecycleStatus = {
    ACTIVE: "ACTIVE",
    LEGACY: "LEGACY",
};
export const PromptRouterStatus = {
    AVAILABLE: "AVAILABLE",
};
export const PromptRouterType = {
    CUSTOM: "custom",
    DEFAULT: "default",
};
export const CommitmentDuration = {
    ONE_MONTH: "OneMonth",
    SIX_MONTHS: "SixMonths",
};
export const ProvisionedModelStatus = {
    CREATING: "Creating",
    FAILED: "Failed",
    IN_SERVICE: "InService",
    UPDATING: "Updating",
};
export const SortByProvisionedModels = {
    CREATION_TIME: "CreationTime",
};
export const AuthorizationStatus = {
    AUTHORIZED: "AUTHORIZED",
    NOT_AUTHORIZED: "NOT_AUTHORIZED",
};
export const RequestMetadataBaseFiltersFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.equals && { equals: SENSITIVE_STRING }),
    ...(obj.notEquals && { notEquals: SENSITIVE_STRING }),
});
export const RequestMetadataFiltersFilterSensitiveLog = (obj) => {
    if (obj.equals !== undefined)
        return { equals: SENSITIVE_STRING };
    if (obj.notEquals !== undefined)
        return { notEquals: SENSITIVE_STRING };
    if (obj.andAll !== undefined)
        return { andAll: obj.andAll.map((item) => RequestMetadataBaseFiltersFilterSensitiveLog(item)) };
    if (obj.orAll !== undefined)
        return { orAll: obj.orAll.map((item) => RequestMetadataBaseFiltersFilterSensitiveLog(item)) };
    if (obj.$unknown !== undefined)
        return { [obj.$unknown[0]]: "UNKNOWN" };
};
export const InvocationLogsConfigFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.invocationLogSource && { invocationLogSource: obj.invocationLogSource }),
    ...(obj.requestMetadataFilters && {
        requestMetadataFilters: RequestMetadataFiltersFilterSensitiveLog(obj.requestMetadataFilters),
    }),
});
export const TrainingDataConfigFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.invocationLogsConfig && {
        invocationLogsConfig: InvocationLogsConfigFilterSensitiveLog(obj.invocationLogsConfig),
    }),
});
export const GetCustomModelResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.trainingDataConfig && { trainingDataConfig: TrainingDataConfigFilterSensitiveLog(obj.trainingDataConfig) }),
    ...(obj.customizationConfig && { customizationConfig: obj.customizationConfig }),
});
export const BatchDeleteEvaluationJobRequestFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.jobIdentifiers && { jobIdentifiers: SENSITIVE_STRING }),
});
export const BatchDeleteEvaluationJobErrorFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.jobIdentifier && { jobIdentifier: SENSITIVE_STRING }),
});
export const BatchDeleteEvaluationJobItemFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.jobIdentifier && { jobIdentifier: SENSITIVE_STRING }),
});
export const BatchDeleteEvaluationJobResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.errors && { errors: obj.errors.map((item) => BatchDeleteEvaluationJobErrorFilterSensitiveLog(item)) }),
    ...(obj.evaluationJobs && {
        evaluationJobs: obj.evaluationJobs.map((item) => BatchDeleteEvaluationJobItemFilterSensitiveLog(item)),
    }),
});
export const CustomMetricDefinitionFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.name && { name: SENSITIVE_STRING }),
    ...(obj.ratingScale && { ratingScale: obj.ratingScale.map((item) => item) }),
});
export const AutomatedEvaluationCustomMetricSourceFilterSensitiveLog = (obj) => {
    if (obj.customMetricDefinition !== undefined)
        return { customMetricDefinition: SENSITIVE_STRING };
    if (obj.$unknown !== undefined)
        return { [obj.$unknown[0]]: "UNKNOWN" };
};
export const AutomatedEvaluationCustomMetricConfigFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.customMetrics && {
        customMetrics: obj.customMetrics.map((item) => AutomatedEvaluationCustomMetricSourceFilterSensitiveLog(item)),
    }),
});
export const EvaluationDatasetFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.name && { name: SENSITIVE_STRING }),
    ...(obj.datasetLocation && { datasetLocation: obj.datasetLocation }),
});
export const EvaluationDatasetMetricConfigFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.dataset && { dataset: EvaluationDatasetFilterSensitiveLog(obj.dataset) }),
    ...(obj.metricNames && { metricNames: SENSITIVE_STRING }),
});
export const AutomatedEvaluationConfigFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.datasetMetricConfigs && {
        datasetMetricConfigs: obj.datasetMetricConfigs.map((item) => EvaluationDatasetMetricConfigFilterSensitiveLog(item)),
    }),
    ...(obj.evaluatorModelConfig && { evaluatorModelConfig: obj.evaluatorModelConfig }),
    ...(obj.customMetricConfig && {
        customMetricConfig: AutomatedEvaluationCustomMetricConfigFilterSensitiveLog(obj.customMetricConfig),
    }),
});
export const HumanEvaluationCustomMetricFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.name && { name: SENSITIVE_STRING }),
    ...(obj.description && { description: SENSITIVE_STRING }),
});
export const HumanWorkflowConfigFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.instructions && { instructions: SENSITIVE_STRING }),
});
export const HumanEvaluationConfigFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.humanWorkflowConfig && {
        humanWorkflowConfig: HumanWorkflowConfigFilterSensitiveLog(obj.humanWorkflowConfig),
    }),
    ...(obj.customMetrics && {
        customMetrics: obj.customMetrics.map((item) => HumanEvaluationCustomMetricFilterSensitiveLog(item)),
    }),
    ...(obj.datasetMetricConfigs && {
        datasetMetricConfigs: obj.datasetMetricConfigs.map((item) => EvaluationDatasetMetricConfigFilterSensitiveLog(item)),
    }),
});
export const EvaluationConfigFilterSensitiveLog = (obj) => {
    if (obj.automated !== undefined)
        return { automated: AutomatedEvaluationConfigFilterSensitiveLog(obj.automated) };
    if (obj.human !== undefined)
        return { human: HumanEvaluationConfigFilterSensitiveLog(obj.human) };
    if (obj.$unknown !== undefined)
        return { [obj.$unknown[0]]: "UNKNOWN" };
};
export const EvaluationBedrockModelFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.inferenceParams && { inferenceParams: SENSITIVE_STRING }),
});
export const EvaluationModelConfigFilterSensitiveLog = (obj) => {
    if (obj.bedrockModel !== undefined)
        return { bedrockModel: EvaluationBedrockModelFilterSensitiveLog(obj.bedrockModel) };
    if (obj.precomputedInferenceSource !== undefined)
        return { precomputedInferenceSource: obj.precomputedInferenceSource };
    if (obj.$unknown !== undefined)
        return { [obj.$unknown[0]]: "UNKNOWN" };
};
export const PromptTemplateFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.textPromptTemplate && { textPromptTemplate: SENSITIVE_STRING }),
});
export const ExternalSourcesGenerationConfigurationFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.promptTemplate && { promptTemplate: PromptTemplateFilterSensitiveLog(obj.promptTemplate) }),
});
export const ByteContentDocFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.identifier && { identifier: SENSITIVE_STRING }),
    ...(obj.data && { data: SENSITIVE_STRING }),
});
export const ExternalSourceFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.byteContent && { byteContent: ByteContentDocFilterSensitiveLog(obj.byteContent) }),
});
export const ExternalSourcesRetrieveAndGenerateConfigurationFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.sources && { sources: obj.sources.map((item) => ExternalSourceFilterSensitiveLog(item)) }),
    ...(obj.generationConfiguration && {
        generationConfiguration: ExternalSourcesGenerationConfigurationFilterSensitiveLog(obj.generationConfiguration),
    }),
});
export const GenerationConfigurationFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.promptTemplate && { promptTemplate: PromptTemplateFilterSensitiveLog(obj.promptTemplate) }),
});
export const MetadataAttributeSchemaFilterSensitiveLog = (obj) => ({
    ...obj,
});
export const ImplicitFilterConfigurationFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.metadataAttributes && { metadataAttributes: SENSITIVE_STRING }),
});
export const RerankingMetadataSelectiveModeConfigurationFilterSensitiveLog = (obj) => {
    if (obj.fieldsToInclude !== undefined)
        return { fieldsToInclude: SENSITIVE_STRING };
    if (obj.fieldsToExclude !== undefined)
        return { fieldsToExclude: SENSITIVE_STRING };
    if (obj.$unknown !== undefined)
        return { [obj.$unknown[0]]: "UNKNOWN" };
};
export const MetadataConfigurationForRerankingFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.selectiveModeConfiguration && {
        selectiveModeConfiguration: RerankingMetadataSelectiveModeConfigurationFilterSensitiveLog(obj.selectiveModeConfiguration),
    }),
});
export const VectorSearchBedrockRerankingConfigurationFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.metadataConfiguration && {
        metadataConfiguration: MetadataConfigurationForRerankingFilterSensitiveLog(obj.metadataConfiguration),
    }),
});
export const VectorSearchRerankingConfigurationFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.bedrockRerankingConfiguration && {
        bedrockRerankingConfiguration: VectorSearchBedrockRerankingConfigurationFilterSensitiveLog(obj.bedrockRerankingConfiguration),
    }),
});
export const GetEvaluationJobRequestFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.jobIdentifier && { jobIdentifier: SENSITIVE_STRING }),
});
export const StopEvaluationJobRequestFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.jobIdentifier && { jobIdentifier: SENSITIVE_STRING }),
});
export const GuardrailContentFilterConfigFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.inputModalities && { inputModalities: SENSITIVE_STRING }),
    ...(obj.outputModalities && { outputModalities: SENSITIVE_STRING }),
    ...(obj.inputAction && { inputAction: SENSITIVE_STRING }),
    ...(obj.outputAction && { outputAction: SENSITIVE_STRING }),
});
export const GuardrailContentFiltersTierConfigFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.tierName && { tierName: SENSITIVE_STRING }),
});
export const GuardrailContentPolicyConfigFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.filtersConfig && {
        filtersConfig: obj.filtersConfig.map((item) => GuardrailContentFilterConfigFilterSensitiveLog(item)),
    }),
    ...(obj.tierConfig && { tierConfig: GuardrailContentFiltersTierConfigFilterSensitiveLog(obj.tierConfig) }),
});
export const GuardrailContextualGroundingFilterConfigFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.action && { action: SENSITIVE_STRING }),
});
export const GuardrailContextualGroundingPolicyConfigFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.filtersConfig && {
        filtersConfig: obj.filtersConfig.map((item) => GuardrailContextualGroundingFilterConfigFilterSensitiveLog(item)),
    }),
});
export const GuardrailTopicsTierConfigFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.tierName && { tierName: SENSITIVE_STRING }),
});
export const GuardrailTopicConfigFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.name && { name: SENSITIVE_STRING }),
    ...(obj.definition && { definition: SENSITIVE_STRING }),
    ...(obj.examples && { examples: SENSITIVE_STRING }),
    ...(obj.inputAction && { inputAction: SENSITIVE_STRING }),
    ...(obj.outputAction && { outputAction: SENSITIVE_STRING }),
});
export const GuardrailTopicPolicyConfigFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.topicsConfig && {
        topicsConfig: obj.topicsConfig.map((item) => GuardrailTopicConfigFilterSensitiveLog(item)),
    }),
    ...(obj.tierConfig && { tierConfig: GuardrailTopicsTierConfigFilterSensitiveLog(obj.tierConfig) }),
});
export const GuardrailManagedWordsConfigFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.inputAction && { inputAction: SENSITIVE_STRING }),
    ...(obj.outputAction && { outputAction: SENSITIVE_STRING }),
});
export const GuardrailWordConfigFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.inputAction && { inputAction: SENSITIVE_STRING }),
    ...(obj.outputAction && { outputAction: SENSITIVE_STRING }),
});
export const GuardrailWordPolicyConfigFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.wordsConfig && { wordsConfig: obj.wordsConfig.map((item) => GuardrailWordConfigFilterSensitiveLog(item)) }),
    ...(obj.managedWordListsConfig && {
        managedWordListsConfig: obj.managedWordListsConfig.map((item) => GuardrailManagedWordsConfigFilterSensitiveLog(item)),
    }),
});
export const CreateGuardrailRequestFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.name && { name: SENSITIVE_STRING }),
    ...(obj.description && { description: SENSITIVE_STRING }),
    ...(obj.topicPolicyConfig && {
        topicPolicyConfig: GuardrailTopicPolicyConfigFilterSensitiveLog(obj.topicPolicyConfig),
    }),
    ...(obj.contentPolicyConfig && {
        contentPolicyConfig: GuardrailContentPolicyConfigFilterSensitiveLog(obj.contentPolicyConfig),
    }),
    ...(obj.wordPolicyConfig && { wordPolicyConfig: GuardrailWordPolicyConfigFilterSensitiveLog(obj.wordPolicyConfig) }),
    ...(obj.contextualGroundingPolicyConfig && {
        contextualGroundingPolicyConfig: GuardrailContextualGroundingPolicyConfigFilterSensitiveLog(obj.contextualGroundingPolicyConfig),
    }),
    ...(obj.blockedInputMessaging && { blockedInputMessaging: SENSITIVE_STRING }),
    ...(obj.blockedOutputsMessaging && { blockedOutputsMessaging: SENSITIVE_STRING }),
});
export const CreateGuardrailVersionRequestFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.description && { description: SENSITIVE_STRING }),
});
export const GuardrailContentFilterFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.inputModalities && { inputModalities: SENSITIVE_STRING }),
    ...(obj.outputModalities && { outputModalities: SENSITIVE_STRING }),
    ...(obj.inputAction && { inputAction: SENSITIVE_STRING }),
    ...(obj.outputAction && { outputAction: SENSITIVE_STRING }),
});
export const GuardrailContentFiltersTierFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.tierName && { tierName: SENSITIVE_STRING }),
});
export const GuardrailContentPolicyFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.filters && { filters: obj.filters.map((item) => GuardrailContentFilterFilterSensitiveLog(item)) }),
    ...(obj.tier && { tier: GuardrailContentFiltersTierFilterSensitiveLog(obj.tier) }),
});
export const GuardrailContextualGroundingFilterFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.action && { action: SENSITIVE_STRING }),
});
export const GuardrailContextualGroundingPolicyFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.filters && {
        filters: obj.filters.map((item) => GuardrailContextualGroundingFilterFilterSensitiveLog(item)),
    }),
});
export const GuardrailTopicsTierFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.tierName && { tierName: SENSITIVE_STRING }),
});
export const GuardrailTopicFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.name && { name: SENSITIVE_STRING }),
    ...(obj.definition && { definition: SENSITIVE_STRING }),
    ...(obj.examples && { examples: SENSITIVE_STRING }),
    ...(obj.inputAction && { inputAction: SENSITIVE_STRING }),
    ...(obj.outputAction && { outputAction: SENSITIVE_STRING }),
});
export const GuardrailTopicPolicyFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.topics && { topics: obj.topics.map((item) => GuardrailTopicFilterSensitiveLog(item)) }),
    ...(obj.tier && { tier: GuardrailTopicsTierFilterSensitiveLog(obj.tier) }),
});
export const GuardrailManagedWordsFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.inputAction && { inputAction: SENSITIVE_STRING }),
    ...(obj.outputAction && { outputAction: SENSITIVE_STRING }),
});
export const GuardrailWordFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.inputAction && { inputAction: SENSITIVE_STRING }),
    ...(obj.outputAction && { outputAction: SENSITIVE_STRING }),
});
export const GuardrailWordPolicyFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.words && { words: obj.words.map((item) => GuardrailWordFilterSensitiveLog(item)) }),
    ...(obj.managedWordLists && {
        managedWordLists: obj.managedWordLists.map((item) => GuardrailManagedWordsFilterSensitiveLog(item)),
    }),
});
export const GetGuardrailResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.name && { name: SENSITIVE_STRING }),
    ...(obj.description && { description: SENSITIVE_STRING }),
    ...(obj.topicPolicy && { topicPolicy: GuardrailTopicPolicyFilterSensitiveLog(obj.topicPolicy) }),
    ...(obj.contentPolicy && { contentPolicy: GuardrailContentPolicyFilterSensitiveLog(obj.contentPolicy) }),
    ...(obj.wordPolicy && { wordPolicy: GuardrailWordPolicyFilterSensitiveLog(obj.wordPolicy) }),
    ...(obj.contextualGroundingPolicy && {
        contextualGroundingPolicy: GuardrailContextualGroundingPolicyFilterSensitiveLog(obj.contextualGroundingPolicy),
    }),
    ...(obj.statusReasons && { statusReasons: SENSITIVE_STRING }),
    ...(obj.failureRecommendations && { failureRecommendations: SENSITIVE_STRING }),
    ...(obj.blockedInputMessaging && { blockedInputMessaging: SENSITIVE_STRING }),
    ...(obj.blockedOutputsMessaging && { blockedOutputsMessaging: SENSITIVE_STRING }),
});
export const GuardrailSummaryFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.name && { name: SENSITIVE_STRING }),
    ...(obj.description && { description: SENSITIVE_STRING }),
});
export const ListGuardrailsResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.guardrails && { guardrails: obj.guardrails.map((item) => GuardrailSummaryFilterSensitiveLog(item)) }),
});
export const UpdateGuardrailRequestFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.name && { name: SENSITIVE_STRING }),
    ...(obj.description && { description: SENSITIVE_STRING }),
    ...(obj.topicPolicyConfig && {
        topicPolicyConfig: GuardrailTopicPolicyConfigFilterSensitiveLog(obj.topicPolicyConfig),
    }),
    ...(obj.contentPolicyConfig && {
        contentPolicyConfig: GuardrailContentPolicyConfigFilterSensitiveLog(obj.contentPolicyConfig),
    }),
    ...(obj.wordPolicyConfig && { wordPolicyConfig: GuardrailWordPolicyConfigFilterSensitiveLog(obj.wordPolicyConfig) }),
    ...(obj.contextualGroundingPolicyConfig && {
        contextualGroundingPolicyConfig: GuardrailContextualGroundingPolicyConfigFilterSensitiveLog(obj.contextualGroundingPolicyConfig),
    }),
    ...(obj.blockedInputMessaging && { blockedInputMessaging: SENSITIVE_STRING }),
    ...(obj.blockedOutputsMessaging && { blockedOutputsMessaging: SENSITIVE_STRING }),
});
export const CreateInferenceProfileRequestFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.description && { description: SENSITIVE_STRING }),
    ...(obj.modelSource && { modelSource: obj.modelSource }),
});
export const GetInferenceProfileResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.description && { description: SENSITIVE_STRING }),
});
export const InferenceProfileSummaryFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.description && { description: SENSITIVE_STRING }),
});
export const ListInferenceProfilesResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.inferenceProfileSummaries && {
        inferenceProfileSummaries: obj.inferenceProfileSummaries.map((item) => InferenceProfileSummaryFilterSensitiveLog(item)),
    }),
});
export const GetModelInvocationJobResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.message && { message: SENSITIVE_STRING }),
    ...(obj.inputDataConfig && { inputDataConfig: obj.inputDataConfig }),
    ...(obj.outputDataConfig && { outputDataConfig: obj.outputDataConfig }),
});
export const ModelInvocationJobSummaryFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.message && { message: SENSITIVE_STRING }),
    ...(obj.inputDataConfig && { inputDataConfig: obj.inputDataConfig }),
    ...(obj.outputDataConfig && { outputDataConfig: obj.outputDataConfig }),
});
export const ListModelInvocationJobsResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.invocationJobSummaries && {
        invocationJobSummaries: obj.invocationJobSummaries.map((item) => ModelInvocationJobSummaryFilterSensitiveLog(item)),
    }),
});
export const CreatePromptRouterRequestFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.description && { description: SENSITIVE_STRING }),
});
export const GetPromptRouterResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.description && { description: SENSITIVE_STRING }),
});
export const PromptRouterSummaryFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.description && { description: SENSITIVE_STRING }),
});
export const ListPromptRoutersResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.promptRouterSummaries && {
        promptRouterSummaries: obj.promptRouterSummaries.map((item) => PromptRouterSummaryFilterSensitiveLog(item)),
    }),
});
