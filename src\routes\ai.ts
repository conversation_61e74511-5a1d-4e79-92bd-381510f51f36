import { Router } from 'express';
import { ConversationalAIController } from '../controllers/ConversationalAIController';

/**
 * AI routes class for conversational AI bot functionality
 */
class AIRoutes {
  public router: Router;

  constructor() {
    this.router = Router();
    this.initializeRoutes();
  }

  private initializeRoutes(): void {
    // Chat endpoint - main conversational AI interface
    this.router.post('/chat', ConversationalAIController.chat);

    // Context management endpoints
    this.router.get('/context/:sessionId', ConversationalAIController.getContext);
    this.router.delete('/context/:sessionId', ConversationalAIController.clearContext);
    this.router.put('/preferences/:sessionId', ConversationalAIController.updatePreferences);

    // Health check endpoint
    this.router.get('/health', ConversationalAIController.healthCheck);

    // API documentation endpoint
    this.router.get('/', (_req, res) => {
      res.status(200).json({
        success: true,
        message: 'Cravin Concierge AI API',
        version: '1.0.0',
        endpoints: {
          chat: {
            method: 'POST',
            path: '/api/ai/chat',
            description: 'Send a query to the conversational AI bot',
            body: {
              query: 'string (required) - User query',
              sessionId: 'string (optional) - Session identifier',
              userId: 'string (optional) - User identifier',
              userPreferences: {
                location: 'string (optional) - Preferred location',
                priceRange: {
                  min: 'number (optional) - Minimum price',
                  max: 'number (optional) - Maximum price'
                },
                cuisineTypes: 'string[] (optional) - Preferred cuisine types',
                businessTypes: 'string[] (optional) - Preferred business types'
              }
            },
            response: {
              success: 'boolean',
              data: {
                response: 'string - AI generated response',
                sessionId: 'string - Session identifier',
                businessRecommendations: 'array - Recommended businesses',
                confidence: 'number - Response confidence score',
                suggestedFollowUps: 'string[] - Suggested follow-up questions',
                timestamp: 'string - Response timestamp'
              }
            }
          },
          getContext: {
            method: 'GET',
            path: '/api/ai/context/:sessionId',
            description: 'Get conversation context for a session',
            response: {
              success: 'boolean',
              data: {
                sessionId: 'string',
                userId: 'string',
                previousQueries: 'string[]',
                userPreferences: 'object',
                queryCount: 'number'
              }
            }
          },
          clearContext: {
            method: 'DELETE',
            path: '/api/ai/context/:sessionId',
            description: 'Clear conversation context for a session',
            response: {
              success: 'boolean',
              message: 'string'
            }
          },
          updatePreferences: {
            method: 'PUT',
            path: '/api/ai/preferences/:sessionId',
            description: 'Update user preferences for a session',
            body: {
              preferences: {
                location: 'string (optional)',
                priceRange: 'object (optional)',
                cuisineTypes: 'string[] (optional)',
                businessTypes: 'string[] (optional)'
              }
            },
            response: {
              success: 'boolean',
              message: 'string'
            }
          },
          health: {
            method: 'GET',
            path: '/api/ai/health',
            description: 'Check health status of AI services',
            response: {
              success: 'boolean',
              message: 'string',
              data: {
                aiServiceStatus: 'string',
                vectorSearchStatus: 'string',
                bedrockStatus: 'string'
              }
            }
          }
        },
        examples: {
          chatRequest: {
            query: 'I\'m looking for a good Italian restaurant in Dubai Marina',
            sessionId: 'user-session-123',
            userId: 'user-456',
            userPreferences: {
              location: 'Dubai Marina',
              priceRange: { min: 50, max: 200 },
              cuisineTypes: ['Italian']
            }
          },
          chatResponse: {
            success: true,
            data: {
              response: 'I found some great Italian restaurants in Dubai Marina for you! Here are my top recommendations...',
              sessionId: 'user-session-123',
              businessRecommendations: [
                {
                  id: 'restaurant-123',
                  name: 'Bella Vista Italian',
                  description: 'Authentic Italian restaurant with marina views',
                  type: 'restaurant',
                  similarity_score: 0.92,
                  details: {
                    phone_number: '+971-4-123-4567',
                    address: 'Dubai Marina Walk',
                    emirate: 'Dubai',
                    average_spend: 150,
                    menu_type: 'Italian',
                    takes_orders: true
                  }
                }
              ],
              confidence: 0.89,
              suggestedFollowUps: [
                'What are the operating hours?',
                'Do they offer delivery?',
                'Show me more options nearby'
              ],
              timestamp: '2024-01-15T10:30:00.000Z'
            },
            timestamp: '2024-01-15T10:30:00.000Z'
          }
        },
        usage: {
          authentication: 'No authentication required for basic usage',
          rateLimit: 'Standard rate limiting applies',
          sessionManagement: 'Sessions are automatically created and managed',
          dataRetention: 'Conversation context is kept in memory only'
        },
        timestamp: new Date().toISOString()
      });
    });
  }
}

// Export router instance
export const aiRoutes = new AIRoutes().router;
