import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { GetCustomModelResponseFilterSensitiveLog, } from "../models/models_0";
import { de_GetCustomModelCommand, se_GetCustomModelCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class GetCustomModelCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockControlPlaneService", "GetCustomModel", {})
    .n("BedrockClient", "GetCustomModelCommand")
    .f(void 0, GetCustomModelResponseFilterSensitiveLog)
    .ser(se_GetCustomModelCommand)
    .de(de_GetCustomModelCommand)
    .build() {
}
