import { Router } from 'express';
import { ConversationalAIController } from '../controllers/conversationalAI.controller';

/**
 * Conversational AI Routes
 * Handles all routes related to the AI-powered conversational interface
 */
export class ConversationalAIRoutes {
  public router: Router;
  private controller: ConversationalAIController;

  constructor() {
    this.router = Router();
    this.controller = new ConversationalAIController();
    this.initializeRoutes();
  }

  /**
   * Initialize all conversational AI routes
   */
  private initializeRoutes(): void {
    // Main conversational query endpoint
    this.router.post('/query', this.addRequestTiming, (req, res) => {
      this.controller.handleQuery(req, res);
    });

    // Business type specific search
    this.router.post('/search', this.addRequestTiming, (req, res) => {
      this.controller.searchBusinessType(req, res);
    });

    // Get detailed business information
    this.router.get('/business/:businessType/:businessId', this.addRequestTiming, (req, res) => {
      this.controller.getBusinessDetails(req, res);
    });

    // Health check endpoint
    this.router.get('/health', (req, res) => {
      this.controller.healthCheck(req, res);
    });

    // Get available business types
    this.router.get('/business-types', (_req, res) => {
      res.status(200).json({
        success: true,
        data: {
          business_types: [
            {
              type: 'club',
              description: 'Sports clubs and recreational facilities',
              searchable_fields: ['sport_type', 'facility_type', 'location']
            },
            {
              type: 'restaurant',
              description: 'Restaurants and dining establishments',
              searchable_fields: ['cuisine_type', 'price_range', 'location', 'menu_items']
            },
            {
              type: 'shop',
              description: 'Retail shops and commercial services',
              searchable_fields: ['product_type', 'service_type', 'location']
            }
          ]
        }
      });
    });

    // Get search suggestions based on popular queries
    this.router.get('/suggestions', (req, res) => {
      const { type } = req.query;
      
      const suggestions = this.getSearchSuggestions(type as string);
      
      res.status(200).json({
        success: true,
        data: {
          suggestions,
          generated_at: new Date().toISOString()
        }
      });
    });
  }

  /**
   * Middleware to add request timing
   */
  private addRequestTiming(req: any, _res: any, next: any): void {
    req.startTime = Date.now();
    next();
  }

  /**
   * Get search suggestions based on business type
   * @param type - Business type
   * @returns Array of search suggestions
   */
  private getSearchSuggestions(type?: string): string[] {
    const allSuggestions = {
      club: [
        'Find tennis courts near me',
        'Show me swimming pools in Dubai',
        'What football clubs are available?',
        'Find fitness centers with personal trainers',
        'Show me basketball courts for booking',
        'Find yoga studios in my area',
        'What sports clubs offer group classes?'
      ],
      restaurant: [
        'Find Italian restaurants nearby',
        'Show me budget-friendly dining options',
        'What restaurants deliver to my location?',
        'Find restaurants with outdoor seating',
        'Show me vegetarian-friendly restaurants',
        'What are the best seafood restaurants?',
        'Find restaurants open late night'
      ],
      shop: [
        'Find electronics stores near me',
        'Show me clothing boutiques',
        'What grocery stores are open now?',
        'Find pharmacies in my area',
        'Show me home improvement stores',
        'What gift shops are available?',
        'Find bookstores with cafe'
      ],
      general: [
        'What\'s open near me right now?',
        'Find businesses in Dubai Marina',
        'Show me popular places in Downtown',
        'What\'s new in my area?',
        'Find businesses with parking',
        'Show me family-friendly places',
        'What businesses offer delivery?'
      ]
    };

    if (type && allSuggestions[type as keyof typeof allSuggestions]) {
      return allSuggestions[type as keyof typeof allSuggestions];
    }

    return allSuggestions.general;
  }
}

// Export the router instance
export const conversationalAIRoutes = new ConversationalAIRoutes().router;
