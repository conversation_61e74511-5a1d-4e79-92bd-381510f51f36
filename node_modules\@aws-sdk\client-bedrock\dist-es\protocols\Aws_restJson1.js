import { awsExpectUnion as __expectUnion, loadRestJsonErrorCode, parseJsonBody as parseBody, parseJsonErrorBody as parseErrorBody, } from "@aws-sdk/core";
import { requestBuilder as rb } from "@smithy/core";
import { _json, collectBody, decorateServiceException as __decorateServiceException, expectBoolean as __expectBoolean, expectInt32 as __expectInt32, expectNonNull as __expectNonNull, expectObject as __expectObject, expectString as __expectString, limitedParseDouble as __limitedParseDouble, limitedParseFloat32 as __limitedParseFloat32, map, parseRfc3339DateTimeWithOffset as __parseRfc3339DateTimeWithOffset, serializeDateTime as __serializeDateTime, serializeFloat as __serializeFloat, take, withBaseException, } from "@smithy/smithy-client";
import { v4 as generateIdempotencyToken } from "uuid";
import { BedrockServiceException as __BaseException } from "../models/BedrockServiceException";
import { AccessDeniedException, AutomatedEvaluationCustomMetricSource, ConflictException, EvaluationConfig, InternalServerException, RatingScaleItemValue, ResourceNotFoundException, ServiceQuotaExceededException, ServiceUnavailableException, ThrottlingException, TooManyTagsException, ValidationException, } from "../models/models_0";
import { EvaluationInferenceConfig, KnowledgeBaseConfig, RAGConfig, RetrievalFilter, } from "../models/models_1";
export const se_BatchDeleteEvaluationJobCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/evaluation-jobs/batch-delete");
    let body;
    body = JSON.stringify(take(input, {
        jobIdentifiers: (_) => _json(_),
    }));
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_CreateCustomModelCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/custom-models/create-custom-model");
    let body;
    body = JSON.stringify(take(input, {
        clientRequestToken: [true, (_) => _ ?? generateIdempotencyToken()],
        modelKmsKeyArn: [],
        modelName: [],
        modelSourceConfig: (_) => _json(_),
        modelTags: (_) => _json(_),
        roleArn: [],
    }));
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_CreateEvaluationJobCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/evaluation-jobs");
    let body;
    body = JSON.stringify(take(input, {
        applicationType: [],
        clientRequestToken: [true, (_) => _ ?? generateIdempotencyToken()],
        customerEncryptionKeyId: [],
        evaluationConfig: (_) => se_EvaluationConfig(_, context),
        inferenceConfig: (_) => se_EvaluationInferenceConfig(_, context),
        jobDescription: [],
        jobName: [],
        jobTags: (_) => _json(_),
        outputDataConfig: (_) => _json(_),
        roleArn: [],
    }));
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_CreateFoundationModelAgreementCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/create-foundation-model-agreement");
    let body;
    body = JSON.stringify(take(input, {
        modelId: [],
        offerToken: [],
    }));
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_CreateGuardrailCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/guardrails");
    let body;
    body = JSON.stringify(take(input, {
        blockedInputMessaging: [],
        blockedOutputsMessaging: [],
        clientRequestToken: [true, (_) => _ ?? generateIdempotencyToken()],
        contentPolicyConfig: (_) => _json(_),
        contextualGroundingPolicyConfig: (_) => se_GuardrailContextualGroundingPolicyConfig(_, context),
        crossRegionConfig: (_) => _json(_),
        description: [],
        kmsKeyId: [],
        name: [],
        sensitiveInformationPolicyConfig: (_) => _json(_),
        tags: (_) => _json(_),
        topicPolicyConfig: (_) => _json(_),
        wordPolicyConfig: (_) => _json(_),
    }));
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_CreateGuardrailVersionCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/guardrails/{guardrailIdentifier}");
    b.p("guardrailIdentifier", () => input.guardrailIdentifier, "{guardrailIdentifier}", false);
    let body;
    body = JSON.stringify(take(input, {
        clientRequestToken: [true, (_) => _ ?? generateIdempotencyToken()],
        description: [],
    }));
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_CreateInferenceProfileCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/inference-profiles");
    let body;
    body = JSON.stringify(take(input, {
        clientRequestToken: [true, (_) => _ ?? generateIdempotencyToken()],
        description: [],
        inferenceProfileName: [],
        modelSource: (_) => _json(_),
        tags: (_) => _json(_),
    }));
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_CreateMarketplaceModelEndpointCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/marketplace-model/endpoints");
    let body;
    body = JSON.stringify(take(input, {
        acceptEula: [],
        clientRequestToken: [true, (_) => _ ?? generateIdempotencyToken()],
        endpointConfig: (_) => _json(_),
        endpointName: [],
        modelSourceIdentifier: [],
        tags: (_) => _json(_),
    }));
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_CreateModelCopyJobCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/model-copy-jobs");
    let body;
    body = JSON.stringify(take(input, {
        clientRequestToken: [true, (_) => _ ?? generateIdempotencyToken()],
        modelKmsKeyId: [],
        sourceModelArn: [],
        targetModelName: [],
        targetModelTags: (_) => _json(_),
    }));
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_CreateModelCustomizationJobCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/model-customization-jobs");
    let body;
    body = JSON.stringify(take(input, {
        baseModelIdentifier: [],
        clientRequestToken: [true, (_) => _ ?? generateIdempotencyToken()],
        customModelKmsKeyId: [],
        customModelName: [],
        customModelTags: (_) => _json(_),
        customizationConfig: (_) => _json(_),
        customizationType: [],
        hyperParameters: (_) => _json(_),
        jobName: [],
        jobTags: (_) => _json(_),
        outputDataConfig: (_) => _json(_),
        roleArn: [],
        trainingDataConfig: (_) => _json(_),
        validationDataConfig: (_) => _json(_),
        vpcConfig: (_) => _json(_),
    }));
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_CreateModelImportJobCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/model-import-jobs");
    let body;
    body = JSON.stringify(take(input, {
        clientRequestToken: [],
        importedModelKmsKeyId: [],
        importedModelName: [],
        importedModelTags: (_) => _json(_),
        jobName: [],
        jobTags: (_) => _json(_),
        modelDataSource: (_) => _json(_),
        roleArn: [],
        vpcConfig: (_) => _json(_),
    }));
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_CreateModelInvocationJobCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/model-invocation-job");
    let body;
    body = JSON.stringify(take(input, {
        clientRequestToken: [true, (_) => _ ?? generateIdempotencyToken()],
        inputDataConfig: (_) => _json(_),
        jobName: [],
        modelId: [],
        outputDataConfig: (_) => _json(_),
        roleArn: [],
        tags: (_) => _json(_),
        timeoutDurationInHours: [],
        vpcConfig: (_) => _json(_),
    }));
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_CreatePromptRouterCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/prompt-routers");
    let body;
    body = JSON.stringify(take(input, {
        clientRequestToken: [true, (_) => _ ?? generateIdempotencyToken()],
        description: [],
        fallbackModel: (_) => _json(_),
        models: (_) => _json(_),
        promptRouterName: [],
        routingCriteria: (_) => se_RoutingCriteria(_, context),
        tags: (_) => _json(_),
    }));
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_CreateProvisionedModelThroughputCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/provisioned-model-throughput");
    let body;
    body = JSON.stringify(take(input, {
        clientRequestToken: [true, (_) => _ ?? generateIdempotencyToken()],
        commitmentDuration: [],
        modelId: [],
        modelUnits: [],
        provisionedModelName: [],
        tags: (_) => _json(_),
    }));
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_DeleteCustomModelCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/custom-models/{modelIdentifier}");
    b.p("modelIdentifier", () => input.modelIdentifier, "{modelIdentifier}", false);
    let body;
    b.m("DELETE").h(headers).b(body);
    return b.build();
};
export const se_DeleteFoundationModelAgreementCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/delete-foundation-model-agreement");
    let body;
    body = JSON.stringify(take(input, {
        modelId: [],
    }));
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_DeleteGuardrailCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/guardrails/{guardrailIdentifier}");
    b.p("guardrailIdentifier", () => input.guardrailIdentifier, "{guardrailIdentifier}", false);
    const query = map({
        [_gV]: [, input[_gV]],
    });
    let body;
    b.m("DELETE").h(headers).q(query).b(body);
    return b.build();
};
export const se_DeleteImportedModelCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/imported-models/{modelIdentifier}");
    b.p("modelIdentifier", () => input.modelIdentifier, "{modelIdentifier}", false);
    let body;
    b.m("DELETE").h(headers).b(body);
    return b.build();
};
export const se_DeleteInferenceProfileCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/inference-profiles/{inferenceProfileIdentifier}");
    b.p("inferenceProfileIdentifier", () => input.inferenceProfileIdentifier, "{inferenceProfileIdentifier}", false);
    let body;
    b.m("DELETE").h(headers).b(body);
    return b.build();
};
export const se_DeleteMarketplaceModelEndpointCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/marketplace-model/endpoints/{endpointArn}");
    b.p("endpointArn", () => input.endpointArn, "{endpointArn}", false);
    let body;
    b.m("DELETE").h(headers).b(body);
    return b.build();
};
export const se_DeleteModelInvocationLoggingConfigurationCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/logging/modelinvocations");
    let body;
    b.m("DELETE").h(headers).b(body);
    return b.build();
};
export const se_DeletePromptRouterCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/prompt-routers/{promptRouterArn}");
    b.p("promptRouterArn", () => input.promptRouterArn, "{promptRouterArn}", false);
    let body;
    b.m("DELETE").h(headers).b(body);
    return b.build();
};
export const se_DeleteProvisionedModelThroughputCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/provisioned-model-throughput/{provisionedModelId}");
    b.p("provisionedModelId", () => input.provisionedModelId, "{provisionedModelId}", false);
    let body;
    b.m("DELETE").h(headers).b(body);
    return b.build();
};
export const se_DeregisterMarketplaceModelEndpointCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/marketplace-model/endpoints/{endpointArn}/registration");
    b.p("endpointArn", () => input.endpointArn, "{endpointArn}", false);
    let body;
    b.m("DELETE").h(headers).b(body);
    return b.build();
};
export const se_GetCustomModelCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/custom-models/{modelIdentifier}");
    b.p("modelIdentifier", () => input.modelIdentifier, "{modelIdentifier}", false);
    let body;
    b.m("GET").h(headers).b(body);
    return b.build();
};
export const se_GetEvaluationJobCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/evaluation-jobs/{jobIdentifier}");
    b.p("jobIdentifier", () => input.jobIdentifier, "{jobIdentifier}", false);
    let body;
    b.m("GET").h(headers).b(body);
    return b.build();
};
export const se_GetFoundationModelCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/foundation-models/{modelIdentifier}");
    b.p("modelIdentifier", () => input.modelIdentifier, "{modelIdentifier}", false);
    let body;
    b.m("GET").h(headers).b(body);
    return b.build();
};
export const se_GetFoundationModelAvailabilityCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/foundation-model-availability/{modelId}");
    b.p("modelId", () => input.modelId, "{modelId}", false);
    let body;
    b.m("GET").h(headers).b(body);
    return b.build();
};
export const se_GetGuardrailCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/guardrails/{guardrailIdentifier}");
    b.p("guardrailIdentifier", () => input.guardrailIdentifier, "{guardrailIdentifier}", false);
    const query = map({
        [_gV]: [, input[_gV]],
    });
    let body;
    b.m("GET").h(headers).q(query).b(body);
    return b.build();
};
export const se_GetImportedModelCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/imported-models/{modelIdentifier}");
    b.p("modelIdentifier", () => input.modelIdentifier, "{modelIdentifier}", false);
    let body;
    b.m("GET").h(headers).b(body);
    return b.build();
};
export const se_GetInferenceProfileCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/inference-profiles/{inferenceProfileIdentifier}");
    b.p("inferenceProfileIdentifier", () => input.inferenceProfileIdentifier, "{inferenceProfileIdentifier}", false);
    let body;
    b.m("GET").h(headers).b(body);
    return b.build();
};
export const se_GetMarketplaceModelEndpointCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/marketplace-model/endpoints/{endpointArn}");
    b.p("endpointArn", () => input.endpointArn, "{endpointArn}", false);
    let body;
    b.m("GET").h(headers).b(body);
    return b.build();
};
export const se_GetModelCopyJobCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/model-copy-jobs/{jobArn}");
    b.p("jobArn", () => input.jobArn, "{jobArn}", false);
    let body;
    b.m("GET").h(headers).b(body);
    return b.build();
};
export const se_GetModelCustomizationJobCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/model-customization-jobs/{jobIdentifier}");
    b.p("jobIdentifier", () => input.jobIdentifier, "{jobIdentifier}", false);
    let body;
    b.m("GET").h(headers).b(body);
    return b.build();
};
export const se_GetModelImportJobCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/model-import-jobs/{jobIdentifier}");
    b.p("jobIdentifier", () => input.jobIdentifier, "{jobIdentifier}", false);
    let body;
    b.m("GET").h(headers).b(body);
    return b.build();
};
export const se_GetModelInvocationJobCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/model-invocation-job/{jobIdentifier}");
    b.p("jobIdentifier", () => input.jobIdentifier, "{jobIdentifier}", false);
    let body;
    b.m("GET").h(headers).b(body);
    return b.build();
};
export const se_GetModelInvocationLoggingConfigurationCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/logging/modelinvocations");
    let body;
    b.m("GET").h(headers).b(body);
    return b.build();
};
export const se_GetPromptRouterCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/prompt-routers/{promptRouterArn}");
    b.p("promptRouterArn", () => input.promptRouterArn, "{promptRouterArn}", false);
    let body;
    b.m("GET").h(headers).b(body);
    return b.build();
};
export const se_GetProvisionedModelThroughputCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/provisioned-model-throughput/{provisionedModelId}");
    b.p("provisionedModelId", () => input.provisionedModelId, "{provisionedModelId}", false);
    let body;
    b.m("GET").h(headers).b(body);
    return b.build();
};
export const se_GetUseCaseForModelAccessCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/use-case-for-model-access");
    let body;
    b.m("GET").h(headers).b(body);
    return b.build();
};
export const se_ListCustomModelsCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/custom-models");
    const query = map({
        [_cTB]: [() => input.creationTimeBefore !== void 0, () => __serializeDateTime(input[_cTB]).toString()],
        [_cTA]: [() => input.creationTimeAfter !== void 0, () => __serializeDateTime(input[_cTA]).toString()],
        [_nC]: [, input[_nC]],
        [_bMAE]: [, input[_bMAE]],
        [_fMAE]: [, input[_fMAE]],
        [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
        [_nT]: [, input[_nT]],
        [_sB]: [, input[_sB]],
        [_sO]: [, input[_sO]],
        [_iO]: [() => input.isOwned !== void 0, () => input[_iO].toString()],
        [_mS]: [, input[_mS]],
    });
    let body;
    b.m("GET").h(headers).q(query).b(body);
    return b.build();
};
export const se_ListEvaluationJobsCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/evaluation-jobs");
    const query = map({
        [_cTA]: [() => input.creationTimeAfter !== void 0, () => __serializeDateTime(input[_cTA]).toString()],
        [_cTB]: [() => input.creationTimeBefore !== void 0, () => __serializeDateTime(input[_cTB]).toString()],
        [_sE]: [, input[_sE]],
        [_aTE]: [, input[_aTE]],
        [_nC]: [, input[_nC]],
        [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
        [_nT]: [, input[_nT]],
        [_sB]: [, input[_sB]],
        [_sO]: [, input[_sO]],
    });
    let body;
    b.m("GET").h(headers).q(query).b(body);
    return b.build();
};
export const se_ListFoundationModelAgreementOffersCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/list-foundation-model-agreement-offers/{modelId}");
    b.p("modelId", () => input.modelId, "{modelId}", false);
    const query = map({
        [_oT]: [, input[_oT]],
    });
    let body;
    b.m("GET").h(headers).q(query).b(body);
    return b.build();
};
export const se_ListFoundationModelsCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/foundation-models");
    const query = map({
        [_bP]: [, input[_bP]],
        [_bCT]: [, input[_bCT]],
        [_bOM]: [, input[_bOM]],
        [_bIT]: [, input[_bIT]],
    });
    let body;
    b.m("GET").h(headers).q(query).b(body);
    return b.build();
};
export const se_ListGuardrailsCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/guardrails");
    const query = map({
        [_gI]: [, input[_gI]],
        [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
        [_nT]: [, input[_nT]],
    });
    let body;
    b.m("GET").h(headers).q(query).b(body);
    return b.build();
};
export const se_ListImportedModelsCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/imported-models");
    const query = map({
        [_cTB]: [() => input.creationTimeBefore !== void 0, () => __serializeDateTime(input[_cTB]).toString()],
        [_cTA]: [() => input.creationTimeAfter !== void 0, () => __serializeDateTime(input[_cTA]).toString()],
        [_nC]: [, input[_nC]],
        [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
        [_nT]: [, input[_nT]],
        [_sB]: [, input[_sB]],
        [_sO]: [, input[_sO]],
    });
    let body;
    b.m("GET").h(headers).q(query).b(body);
    return b.build();
};
export const se_ListInferenceProfilesCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/inference-profiles");
    const query = map({
        [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
        [_nT]: [, input[_nT]],
        [_t]: [, input[_tE]],
    });
    let body;
    b.m("GET").h(headers).q(query).b(body);
    return b.build();
};
export const se_ListMarketplaceModelEndpointsCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/marketplace-model/endpoints");
    const query = map({
        [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
        [_nT]: [, input[_nT]],
        [_mSI]: [, input[_mSE]],
    });
    let body;
    b.m("GET").h(headers).q(query).b(body);
    return b.build();
};
export const se_ListModelCopyJobsCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/model-copy-jobs");
    const query = map({
        [_cTA]: [() => input.creationTimeAfter !== void 0, () => __serializeDateTime(input[_cTA]).toString()],
        [_cTB]: [() => input.creationTimeBefore !== void 0, () => __serializeDateTime(input[_cTB]).toString()],
        [_sE]: [, input[_sE]],
        [_sAE]: [, input[_sAE]],
        [_sMAE]: [, input[_sMAE]],
        [_oMNC]: [, input[_tMNC]],
        [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
        [_nT]: [, input[_nT]],
        [_sB]: [, input[_sB]],
        [_sO]: [, input[_sO]],
    });
    let body;
    b.m("GET").h(headers).q(query).b(body);
    return b.build();
};
export const se_ListModelCustomizationJobsCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/model-customization-jobs");
    const query = map({
        [_cTA]: [() => input.creationTimeAfter !== void 0, () => __serializeDateTime(input[_cTA]).toString()],
        [_cTB]: [() => input.creationTimeBefore !== void 0, () => __serializeDateTime(input[_cTB]).toString()],
        [_sE]: [, input[_sE]],
        [_nC]: [, input[_nC]],
        [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
        [_nT]: [, input[_nT]],
        [_sB]: [, input[_sB]],
        [_sO]: [, input[_sO]],
    });
    let body;
    b.m("GET").h(headers).q(query).b(body);
    return b.build();
};
export const se_ListModelImportJobsCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/model-import-jobs");
    const query = map({
        [_cTA]: [() => input.creationTimeAfter !== void 0, () => __serializeDateTime(input[_cTA]).toString()],
        [_cTB]: [() => input.creationTimeBefore !== void 0, () => __serializeDateTime(input[_cTB]).toString()],
        [_sE]: [, input[_sE]],
        [_nC]: [, input[_nC]],
        [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
        [_nT]: [, input[_nT]],
        [_sB]: [, input[_sB]],
        [_sO]: [, input[_sO]],
    });
    let body;
    b.m("GET").h(headers).q(query).b(body);
    return b.build();
};
export const se_ListModelInvocationJobsCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/model-invocation-jobs");
    const query = map({
        [_sTA]: [() => input.submitTimeAfter !== void 0, () => __serializeDateTime(input[_sTA]).toString()],
        [_sTB]: [() => input.submitTimeBefore !== void 0, () => __serializeDateTime(input[_sTB]).toString()],
        [_sE]: [, input[_sE]],
        [_nC]: [, input[_nC]],
        [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
        [_nT]: [, input[_nT]],
        [_sB]: [, input[_sB]],
        [_sO]: [, input[_sO]],
    });
    let body;
    b.m("GET").h(headers).q(query).b(body);
    return b.build();
};
export const se_ListPromptRoutersCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/prompt-routers");
    const query = map({
        [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
        [_nT]: [, input[_nT]],
        [_t]: [, input[_t]],
    });
    let body;
    b.m("GET").h(headers).q(query).b(body);
    return b.build();
};
export const se_ListProvisionedModelThroughputsCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/provisioned-model-throughputs");
    const query = map({
        [_cTA]: [() => input.creationTimeAfter !== void 0, () => __serializeDateTime(input[_cTA]).toString()],
        [_cTB]: [() => input.creationTimeBefore !== void 0, () => __serializeDateTime(input[_cTB]).toString()],
        [_sE]: [, input[_sE]],
        [_mAE]: [, input[_mAE]],
        [_nC]: [, input[_nC]],
        [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],
        [_nT]: [, input[_nT]],
        [_sB]: [, input[_sB]],
        [_sO]: [, input[_sO]],
    });
    let body;
    b.m("GET").h(headers).q(query).b(body);
    return b.build();
};
export const se_ListTagsForResourceCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/listTagsForResource");
    let body;
    body = JSON.stringify(take(input, {
        resourceARN: [],
    }));
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_PutModelInvocationLoggingConfigurationCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/logging/modelinvocations");
    let body;
    body = JSON.stringify(take(input, {
        loggingConfig: (_) => _json(_),
    }));
    b.m("PUT").h(headers).b(body);
    return b.build();
};
export const se_PutUseCaseForModelAccessCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/use-case-for-model-access");
    let body;
    body = JSON.stringify(take(input, {
        formData: (_) => context.base64Encoder(_),
    }));
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_RegisterMarketplaceModelEndpointCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/marketplace-model/endpoints/{endpointIdentifier}/registration");
    b.p("endpointIdentifier", () => input.endpointIdentifier, "{endpointIdentifier}", false);
    let body;
    body = JSON.stringify(take(input, {
        modelSourceIdentifier: [],
    }));
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_StopEvaluationJobCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/evaluation-job/{jobIdentifier}/stop");
    b.p("jobIdentifier", () => input.jobIdentifier, "{jobIdentifier}", false);
    let body;
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_StopModelCustomizationJobCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/model-customization-jobs/{jobIdentifier}/stop");
    b.p("jobIdentifier", () => input.jobIdentifier, "{jobIdentifier}", false);
    let body;
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_StopModelInvocationJobCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/model-invocation-job/{jobIdentifier}/stop");
    b.p("jobIdentifier", () => input.jobIdentifier, "{jobIdentifier}", false);
    let body;
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_TagResourceCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/tagResource");
    let body;
    body = JSON.stringify(take(input, {
        resourceARN: [],
        tags: (_) => _json(_),
    }));
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_UntagResourceCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/untagResource");
    let body;
    body = JSON.stringify(take(input, {
        resourceARN: [],
        tagKeys: (_) => _json(_),
    }));
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_UpdateGuardrailCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/guardrails/{guardrailIdentifier}");
    b.p("guardrailIdentifier", () => input.guardrailIdentifier, "{guardrailIdentifier}", false);
    let body;
    body = JSON.stringify(take(input, {
        blockedInputMessaging: [],
        blockedOutputsMessaging: [],
        contentPolicyConfig: (_) => _json(_),
        contextualGroundingPolicyConfig: (_) => se_GuardrailContextualGroundingPolicyConfig(_, context),
        crossRegionConfig: (_) => _json(_),
        description: [],
        kmsKeyId: [],
        name: [],
        sensitiveInformationPolicyConfig: (_) => _json(_),
        topicPolicyConfig: (_) => _json(_),
        wordPolicyConfig: (_) => _json(_),
    }));
    b.m("PUT").h(headers).b(body);
    return b.build();
};
export const se_UpdateMarketplaceModelEndpointCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/marketplace-model/endpoints/{endpointArn}");
    b.p("endpointArn", () => input.endpointArn, "{endpointArn}", false);
    let body;
    body = JSON.stringify(take(input, {
        clientRequestToken: [true, (_) => _ ?? generateIdempotencyToken()],
        endpointConfig: (_) => _json(_),
    }));
    b.m("PATCH").h(headers).b(body);
    return b.build();
};
export const se_UpdateProvisionedModelThroughputCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/json",
    };
    b.bp("/provisioned-model-throughput/{provisionedModelId}");
    b.p("provisionedModelId", () => input.provisionedModelId, "{provisionedModelId}", false);
    let body;
    body = JSON.stringify(take(input, {
        desiredModelId: [],
        desiredProvisionedModelName: [],
    }));
    b.m("PATCH").h(headers).b(body);
    return b.build();
};
export const de_BatchDeleteEvaluationJobCommand = async (output, context) => {
    if (output.statusCode !== 202 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        errors: _json,
        evaluationJobs: _json,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_CreateCustomModelCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        modelArn: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_CreateEvaluationJobCommand = async (output, context) => {
    if (output.statusCode !== 202 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        jobArn: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_CreateFoundationModelAgreementCommand = async (output, context) => {
    if (output.statusCode !== 202 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        modelId: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_CreateGuardrailCommand = async (output, context) => {
    if (output.statusCode !== 202 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        createdAt: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        guardrailArn: __expectString,
        guardrailId: __expectString,
        version: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_CreateGuardrailVersionCommand = async (output, context) => {
    if (output.statusCode !== 202 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        guardrailId: __expectString,
        version: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_CreateInferenceProfileCommand = async (output, context) => {
    if (output.statusCode !== 201 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        inferenceProfileArn: __expectString,
        status: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_CreateMarketplaceModelEndpointCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        marketplaceModelEndpoint: (_) => de_MarketplaceModelEndpoint(_, context),
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_CreateModelCopyJobCommand = async (output, context) => {
    if (output.statusCode !== 201 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        jobArn: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_CreateModelCustomizationJobCommand = async (output, context) => {
    if (output.statusCode !== 201 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        jobArn: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_CreateModelImportJobCommand = async (output, context) => {
    if (output.statusCode !== 201 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        jobArn: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_CreateModelInvocationJobCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        jobArn: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_CreatePromptRouterCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        promptRouterArn: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_CreateProvisionedModelThroughputCommand = async (output, context) => {
    if (output.statusCode !== 201 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        provisionedModelArn: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_DeleteCustomModelCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    await collectBody(output.body, context);
    return contents;
};
export const de_DeleteFoundationModelAgreementCommand = async (output, context) => {
    if (output.statusCode !== 202 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    await collectBody(output.body, context);
    return contents;
};
export const de_DeleteGuardrailCommand = async (output, context) => {
    if (output.statusCode !== 202 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    await collectBody(output.body, context);
    return contents;
};
export const de_DeleteImportedModelCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    await collectBody(output.body, context);
    return contents;
};
export const de_DeleteInferenceProfileCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    await collectBody(output.body, context);
    return contents;
};
export const de_DeleteMarketplaceModelEndpointCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    await collectBody(output.body, context);
    return contents;
};
export const de_DeleteModelInvocationLoggingConfigurationCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    await collectBody(output.body, context);
    return contents;
};
export const de_DeletePromptRouterCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    await collectBody(output.body, context);
    return contents;
};
export const de_DeleteProvisionedModelThroughputCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    await collectBody(output.body, context);
    return contents;
};
export const de_DeregisterMarketplaceModelEndpointCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    await collectBody(output.body, context);
    return contents;
};
export const de_GetCustomModelCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        baseModelArn: __expectString,
        creationTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        customizationConfig: (_) => _json(__expectUnion(_)),
        customizationType: __expectString,
        failureMessage: __expectString,
        hyperParameters: _json,
        jobArn: __expectString,
        jobName: __expectString,
        modelArn: __expectString,
        modelKmsKeyArn: __expectString,
        modelName: __expectString,
        modelStatus: __expectString,
        outputDataConfig: _json,
        trainingDataConfig: _json,
        trainingMetrics: (_) => de_TrainingMetrics(_, context),
        validationDataConfig: _json,
        validationMetrics: (_) => de_ValidationMetrics(_, context),
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_GetEvaluationJobCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        applicationType: __expectString,
        creationTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        customerEncryptionKeyId: __expectString,
        evaluationConfig: (_) => de_EvaluationConfig(__expectUnion(_), context),
        failureMessages: _json,
        inferenceConfig: (_) => de_EvaluationInferenceConfig(__expectUnion(_), context),
        jobArn: __expectString,
        jobDescription: __expectString,
        jobName: __expectString,
        jobType: __expectString,
        lastModifiedTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        outputDataConfig: _json,
        roleArn: __expectString,
        status: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_GetFoundationModelCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        modelDetails: _json,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_GetFoundationModelAvailabilityCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        agreementAvailability: _json,
        authorizationStatus: __expectString,
        entitlementAvailability: __expectString,
        modelId: __expectString,
        regionAvailability: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_GetGuardrailCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        blockedInputMessaging: __expectString,
        blockedOutputsMessaging: __expectString,
        contentPolicy: _json,
        contextualGroundingPolicy: (_) => de_GuardrailContextualGroundingPolicy(_, context),
        createdAt: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        crossRegionDetails: _json,
        description: __expectString,
        failureRecommendations: _json,
        guardrailArn: __expectString,
        guardrailId: __expectString,
        kmsKeyArn: __expectString,
        name: __expectString,
        sensitiveInformationPolicy: _json,
        status: __expectString,
        statusReasons: _json,
        topicPolicy: _json,
        updatedAt: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        version: __expectString,
        wordPolicy: _json,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_GetImportedModelCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        creationTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        customModelUnits: _json,
        instructSupported: __expectBoolean,
        jobArn: __expectString,
        jobName: __expectString,
        modelArchitecture: __expectString,
        modelArn: __expectString,
        modelDataSource: (_) => _json(__expectUnion(_)),
        modelKmsKeyArn: __expectString,
        modelName: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_GetInferenceProfileCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        createdAt: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        description: __expectString,
        inferenceProfileArn: __expectString,
        inferenceProfileId: __expectString,
        inferenceProfileName: __expectString,
        models: _json,
        status: __expectString,
        type: __expectString,
        updatedAt: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_GetMarketplaceModelEndpointCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        marketplaceModelEndpoint: (_) => de_MarketplaceModelEndpoint(_, context),
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_GetModelCopyJobCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        creationTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        failureMessage: __expectString,
        jobArn: __expectString,
        sourceAccountId: __expectString,
        sourceModelArn: __expectString,
        sourceModelName: __expectString,
        status: __expectString,
        targetModelArn: __expectString,
        targetModelKmsKeyArn: __expectString,
        targetModelName: __expectString,
        targetModelTags: _json,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_GetModelCustomizationJobCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        baseModelArn: __expectString,
        clientRequestToken: __expectString,
        creationTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        customizationConfig: (_) => _json(__expectUnion(_)),
        customizationType: __expectString,
        endTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        failureMessage: __expectString,
        hyperParameters: _json,
        jobArn: __expectString,
        jobName: __expectString,
        lastModifiedTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        outputDataConfig: _json,
        outputModelArn: __expectString,
        outputModelKmsKeyArn: __expectString,
        outputModelName: __expectString,
        roleArn: __expectString,
        status: __expectString,
        statusDetails: (_) => de_StatusDetails(_, context),
        trainingDataConfig: _json,
        trainingMetrics: (_) => de_TrainingMetrics(_, context),
        validationDataConfig: _json,
        validationMetrics: (_) => de_ValidationMetrics(_, context),
        vpcConfig: _json,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_GetModelImportJobCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        creationTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        endTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        failureMessage: __expectString,
        importedModelArn: __expectString,
        importedModelKmsKeyArn: __expectString,
        importedModelName: __expectString,
        jobArn: __expectString,
        jobName: __expectString,
        lastModifiedTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        modelDataSource: (_) => _json(__expectUnion(_)),
        roleArn: __expectString,
        status: __expectString,
        vpcConfig: _json,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_GetModelInvocationJobCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        clientRequestToken: __expectString,
        endTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        inputDataConfig: (_) => _json(__expectUnion(_)),
        jobArn: __expectString,
        jobExpirationTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        jobName: __expectString,
        lastModifiedTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        message: __expectString,
        modelId: __expectString,
        outputDataConfig: (_) => _json(__expectUnion(_)),
        roleArn: __expectString,
        status: __expectString,
        submitTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        timeoutDurationInHours: __expectInt32,
        vpcConfig: _json,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_GetModelInvocationLoggingConfigurationCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        loggingConfig: _json,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_GetPromptRouterCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        createdAt: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        description: __expectString,
        fallbackModel: _json,
        models: _json,
        promptRouterArn: __expectString,
        promptRouterName: __expectString,
        routingCriteria: (_) => de_RoutingCriteria(_, context),
        status: __expectString,
        type: __expectString,
        updatedAt: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_GetProvisionedModelThroughputCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        commitmentDuration: __expectString,
        commitmentExpirationTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        creationTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        desiredModelArn: __expectString,
        desiredModelUnits: __expectInt32,
        failureMessage: __expectString,
        foundationModelArn: __expectString,
        lastModifiedTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        modelArn: __expectString,
        modelUnits: __expectInt32,
        provisionedModelArn: __expectString,
        provisionedModelName: __expectString,
        status: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_GetUseCaseForModelAccessCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        formData: context.base64Decoder,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_ListCustomModelsCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        modelSummaries: (_) => de_CustomModelSummaryList(_, context),
        nextToken: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_ListEvaluationJobsCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        jobSummaries: (_) => de_EvaluationSummaries(_, context),
        nextToken: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_ListFoundationModelAgreementOffersCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        modelId: __expectString,
        offers: _json,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_ListFoundationModelsCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        modelSummaries: _json,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_ListGuardrailsCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        guardrails: (_) => de_GuardrailSummaries(_, context),
        nextToken: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_ListImportedModelsCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        modelSummaries: (_) => de_ImportedModelSummaryList(_, context),
        nextToken: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_ListInferenceProfilesCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        inferenceProfileSummaries: (_) => de_InferenceProfileSummaries(_, context),
        nextToken: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_ListMarketplaceModelEndpointsCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        marketplaceModelEndpoints: (_) => de_MarketplaceModelEndpointSummaries(_, context),
        nextToken: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_ListModelCopyJobsCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        modelCopyJobSummaries: (_) => de_ModelCopyJobSummaries(_, context),
        nextToken: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_ListModelCustomizationJobsCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        modelCustomizationJobSummaries: (_) => de_ModelCustomizationJobSummaries(_, context),
        nextToken: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_ListModelImportJobsCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        modelImportJobSummaries: (_) => de_ModelImportJobSummaries(_, context),
        nextToken: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_ListModelInvocationJobsCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        invocationJobSummaries: (_) => de_ModelInvocationJobSummaries(_, context),
        nextToken: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_ListPromptRoutersCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        nextToken: __expectString,
        promptRouterSummaries: (_) => de_PromptRouterSummaries(_, context),
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_ListProvisionedModelThroughputsCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        nextToken: __expectString,
        provisionedModelSummaries: (_) => de_ProvisionedModelSummaries(_, context),
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_ListTagsForResourceCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        tags: _json,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_PutModelInvocationLoggingConfigurationCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    await collectBody(output.body, context);
    return contents;
};
export const de_PutUseCaseForModelAccessCommand = async (output, context) => {
    if (output.statusCode !== 201 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    await collectBody(output.body, context);
    return contents;
};
export const de_RegisterMarketplaceModelEndpointCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        marketplaceModelEndpoint: (_) => de_MarketplaceModelEndpoint(_, context),
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_StopEvaluationJobCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    await collectBody(output.body, context);
    return contents;
};
export const de_StopModelCustomizationJobCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    await collectBody(output.body, context);
    return contents;
};
export const de_StopModelInvocationJobCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    await collectBody(output.body, context);
    return contents;
};
export const de_TagResourceCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    await collectBody(output.body, context);
    return contents;
};
export const de_UntagResourceCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    await collectBody(output.body, context);
    return contents;
};
export const de_UpdateGuardrailCommand = async (output, context) => {
    if (output.statusCode !== 202 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        guardrailArn: __expectString,
        guardrailId: __expectString,
        updatedAt: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        version: __expectString,
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_UpdateMarketplaceModelEndpointCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        marketplaceModelEndpoint: (_) => de_MarketplaceModelEndpoint(_, context),
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_UpdateProvisionedModelThroughputCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    await collectBody(output.body, context);
    return contents;
};
const de_CommandError = async (output, context) => {
    const parsedOutput = {
        ...output,
        body: await parseErrorBody(output.body, context),
    };
    const errorCode = loadRestJsonErrorCode(output, parsedOutput.body);
    switch (errorCode) {
        case "AccessDeniedException":
        case "com.amazonaws.bedrock#AccessDeniedException":
            throw await de_AccessDeniedExceptionRes(parsedOutput, context);
        case "ConflictException":
        case "com.amazonaws.bedrock#ConflictException":
            throw await de_ConflictExceptionRes(parsedOutput, context);
        case "InternalServerException":
        case "com.amazonaws.bedrock#InternalServerException":
            throw await de_InternalServerExceptionRes(parsedOutput, context);
        case "ResourceNotFoundException":
        case "com.amazonaws.bedrock#ResourceNotFoundException":
            throw await de_ResourceNotFoundExceptionRes(parsedOutput, context);
        case "ThrottlingException":
        case "com.amazonaws.bedrock#ThrottlingException":
            throw await de_ThrottlingExceptionRes(parsedOutput, context);
        case "ValidationException":
        case "com.amazonaws.bedrock#ValidationException":
            throw await de_ValidationExceptionRes(parsedOutput, context);
        case "ServiceQuotaExceededException":
        case "com.amazonaws.bedrock#ServiceQuotaExceededException":
            throw await de_ServiceQuotaExceededExceptionRes(parsedOutput, context);
        case "TooManyTagsException":
        case "com.amazonaws.bedrock#TooManyTagsException":
            throw await de_TooManyTagsExceptionRes(parsedOutput, context);
        case "ServiceUnavailableException":
        case "com.amazonaws.bedrock#ServiceUnavailableException":
            throw await de_ServiceUnavailableExceptionRes(parsedOutput, context);
        default:
            const parsedBody = parsedOutput.body;
            return throwDefaultError({
                output,
                parsedBody,
                errorCode,
            });
    }
};
const throwDefaultError = withBaseException(__BaseException);
const de_AccessDeniedExceptionRes = async (parsedOutput, context) => {
    const contents = map({});
    const data = parsedOutput.body;
    const doc = take(data, {
        message: __expectString,
    });
    Object.assign(contents, doc);
    const exception = new AccessDeniedException({
        $metadata: deserializeMetadata(parsedOutput),
        ...contents,
    });
    return __decorateServiceException(exception, parsedOutput.body);
};
const de_ConflictExceptionRes = async (parsedOutput, context) => {
    const contents = map({});
    const data = parsedOutput.body;
    const doc = take(data, {
        message: __expectString,
    });
    Object.assign(contents, doc);
    const exception = new ConflictException({
        $metadata: deserializeMetadata(parsedOutput),
        ...contents,
    });
    return __decorateServiceException(exception, parsedOutput.body);
};
const de_InternalServerExceptionRes = async (parsedOutput, context) => {
    const contents = map({});
    const data = parsedOutput.body;
    const doc = take(data, {
        message: __expectString,
    });
    Object.assign(contents, doc);
    const exception = new InternalServerException({
        $metadata: deserializeMetadata(parsedOutput),
        ...contents,
    });
    return __decorateServiceException(exception, parsedOutput.body);
};
const de_ResourceNotFoundExceptionRes = async (parsedOutput, context) => {
    const contents = map({});
    const data = parsedOutput.body;
    const doc = take(data, {
        message: __expectString,
    });
    Object.assign(contents, doc);
    const exception = new ResourceNotFoundException({
        $metadata: deserializeMetadata(parsedOutput),
        ...contents,
    });
    return __decorateServiceException(exception, parsedOutput.body);
};
const de_ServiceQuotaExceededExceptionRes = async (parsedOutput, context) => {
    const contents = map({});
    const data = parsedOutput.body;
    const doc = take(data, {
        message: __expectString,
    });
    Object.assign(contents, doc);
    const exception = new ServiceQuotaExceededException({
        $metadata: deserializeMetadata(parsedOutput),
        ...contents,
    });
    return __decorateServiceException(exception, parsedOutput.body);
};
const de_ServiceUnavailableExceptionRes = async (parsedOutput, context) => {
    const contents = map({});
    const data = parsedOutput.body;
    const doc = take(data, {
        message: __expectString,
    });
    Object.assign(contents, doc);
    const exception = new ServiceUnavailableException({
        $metadata: deserializeMetadata(parsedOutput),
        ...contents,
    });
    return __decorateServiceException(exception, parsedOutput.body);
};
const de_ThrottlingExceptionRes = async (parsedOutput, context) => {
    const contents = map({});
    const data = parsedOutput.body;
    const doc = take(data, {
        message: __expectString,
    });
    Object.assign(contents, doc);
    const exception = new ThrottlingException({
        $metadata: deserializeMetadata(parsedOutput),
        ...contents,
    });
    return __decorateServiceException(exception, parsedOutput.body);
};
const de_TooManyTagsExceptionRes = async (parsedOutput, context) => {
    const contents = map({});
    const data = parsedOutput.body;
    const doc = take(data, {
        message: __expectString,
        resourceName: __expectString,
    });
    Object.assign(contents, doc);
    const exception = new TooManyTagsException({
        $metadata: deserializeMetadata(parsedOutput),
        ...contents,
    });
    return __decorateServiceException(exception, parsedOutput.body);
};
const de_ValidationExceptionRes = async (parsedOutput, context) => {
    const contents = map({});
    const data = parsedOutput.body;
    const doc = take(data, {
        message: __expectString,
    });
    Object.assign(contents, doc);
    const exception = new ValidationException({
        $metadata: deserializeMetadata(parsedOutput),
        ...contents,
    });
    return __decorateServiceException(exception, parsedOutput.body);
};
const se_AdditionalModelRequestFields = (input, context) => {
    return Object.entries(input).reduce((acc, [key, value]) => {
        if (value === null) {
            return acc;
        }
        acc[key] = se_AdditionalModelRequestFieldsValue(value, context);
        return acc;
    }, {});
};
const se_AdditionalModelRequestFieldsValue = (input, context) => {
    return input;
};
const se_AutomatedEvaluationConfig = (input, context) => {
    return take(input, {
        customMetricConfig: (_) => se_AutomatedEvaluationCustomMetricConfig(_, context),
        datasetMetricConfigs: _json,
        evaluatorModelConfig: _json,
    });
};
const se_AutomatedEvaluationCustomMetricConfig = (input, context) => {
    return take(input, {
        customMetrics: (_) => se_AutomatedEvaluationCustomMetrics(_, context),
        evaluatorModelConfig: _json,
    });
};
const se_AutomatedEvaluationCustomMetrics = (input, context) => {
    return input
        .filter((e) => e != null)
        .map((entry) => {
        return se_AutomatedEvaluationCustomMetricSource(entry, context);
    });
};
const se_AutomatedEvaluationCustomMetricSource = (input, context) => {
    return AutomatedEvaluationCustomMetricSource.visit(input, {
        customMetricDefinition: (value) => ({ customMetricDefinition: se_CustomMetricDefinition(value, context) }),
        _: (name, value) => ({ [name]: value }),
    });
};
const se_ByteContentDoc = (input, context) => {
    return take(input, {
        contentType: [],
        data: context.base64Encoder,
        identifier: [],
    });
};
const se_CustomMetricDefinition = (input, context) => {
    return take(input, {
        instructions: [],
        name: [],
        ratingScale: (_) => se_RatingScale(_, context),
    });
};
const se_EvaluationConfig = (input, context) => {
    return EvaluationConfig.visit(input, {
        automated: (value) => ({ automated: se_AutomatedEvaluationConfig(value, context) }),
        human: (value) => ({ human: _json(value) }),
        _: (name, value) => ({ [name]: value }),
    });
};
const se_EvaluationInferenceConfig = (input, context) => {
    return EvaluationInferenceConfig.visit(input, {
        models: (value) => ({ models: _json(value) }),
        ragConfigs: (value) => ({ ragConfigs: se_RagConfigs(value, context) }),
        _: (name, value) => ({ [name]: value }),
    });
};
const se_ExternalSource = (input, context) => {
    return take(input, {
        byteContent: (_) => se_ByteContentDoc(_, context),
        s3Location: _json,
        sourceType: [],
    });
};
const se_ExternalSources = (input, context) => {
    return input
        .filter((e) => e != null)
        .map((entry) => {
        return se_ExternalSource(entry, context);
    });
};
const se_ExternalSourcesGenerationConfiguration = (input, context) => {
    return take(input, {
        additionalModelRequestFields: (_) => se_AdditionalModelRequestFields(_, context),
        guardrailConfiguration: _json,
        kbInferenceConfig: (_) => se_KbInferenceConfig(_, context),
        promptTemplate: _json,
    });
};
const se_ExternalSourcesRetrieveAndGenerateConfiguration = (input, context) => {
    return take(input, {
        generationConfiguration: (_) => se_ExternalSourcesGenerationConfiguration(_, context),
        modelArn: [],
        sources: (_) => se_ExternalSources(_, context),
    });
};
const se_FilterAttribute = (input, context) => {
    return take(input, {
        key: [],
        value: (_) => se_FilterValue(_, context),
    });
};
const se_FilterValue = (input, context) => {
    return input;
};
const se_GenerationConfiguration = (input, context) => {
    return take(input, {
        additionalModelRequestFields: (_) => se_AdditionalModelRequestFields(_, context),
        guardrailConfiguration: _json,
        kbInferenceConfig: (_) => se_KbInferenceConfig(_, context),
        promptTemplate: _json,
    });
};
const se_GuardrailContextualGroundingFilterConfig = (input, context) => {
    return take(input, {
        action: [],
        enabled: [],
        threshold: __serializeFloat,
        type: [],
    });
};
const se_GuardrailContextualGroundingFiltersConfig = (input, context) => {
    return input
        .filter((e) => e != null)
        .map((entry) => {
        return se_GuardrailContextualGroundingFilterConfig(entry, context);
    });
};
const se_GuardrailContextualGroundingPolicyConfig = (input, context) => {
    return take(input, {
        filtersConfig: (_) => se_GuardrailContextualGroundingFiltersConfig(_, context),
    });
};
const se_KbInferenceConfig = (input, context) => {
    return take(input, {
        textInferenceConfig: (_) => se_TextInferenceConfig(_, context),
    });
};
const se_KnowledgeBaseConfig = (input, context) => {
    return KnowledgeBaseConfig.visit(input, {
        retrieveAndGenerateConfig: (value) => ({
            retrieveAndGenerateConfig: se_RetrieveAndGenerateConfiguration(value, context),
        }),
        retrieveConfig: (value) => ({ retrieveConfig: se_RetrieveConfig(value, context) }),
        _: (name, value) => ({ [name]: value }),
    });
};
const se_KnowledgeBaseRetrievalConfiguration = (input, context) => {
    return take(input, {
        vectorSearchConfiguration: (_) => se_KnowledgeBaseVectorSearchConfiguration(_, context),
    });
};
const se_KnowledgeBaseRetrieveAndGenerateConfiguration = (input, context) => {
    return take(input, {
        generationConfiguration: (_) => se_GenerationConfiguration(_, context),
        knowledgeBaseId: [],
        modelArn: [],
        orchestrationConfiguration: _json,
        retrievalConfiguration: (_) => se_KnowledgeBaseRetrievalConfiguration(_, context),
    });
};
const se_KnowledgeBaseVectorSearchConfiguration = (input, context) => {
    return take(input, {
        filter: (_) => se_RetrievalFilter(_, context),
        implicitFilterConfiguration: _json,
        numberOfResults: [],
        overrideSearchType: [],
        rerankingConfiguration: (_) => se_VectorSearchRerankingConfiguration(_, context),
    });
};
const se_RAGConfig = (input, context) => {
    return RAGConfig.visit(input, {
        knowledgeBaseConfig: (value) => ({ knowledgeBaseConfig: se_KnowledgeBaseConfig(value, context) }),
        precomputedRagSourceConfig: (value) => ({ precomputedRagSourceConfig: _json(value) }),
        _: (name, value) => ({ [name]: value }),
    });
};
const se_RagConfigs = (input, context) => {
    return input
        .filter((e) => e != null)
        .map((entry) => {
        return se_RAGConfig(entry, context);
    });
};
const se_RatingScale = (input, context) => {
    return input
        .filter((e) => e != null)
        .map((entry) => {
        return se_RatingScaleItem(entry, context);
    });
};
const se_RatingScaleItem = (input, context) => {
    return take(input, {
        definition: [],
        value: (_) => se_RatingScaleItemValue(_, context),
    });
};
const se_RatingScaleItemValue = (input, context) => {
    return RatingScaleItemValue.visit(input, {
        floatValue: (value) => ({ floatValue: __serializeFloat(value) }),
        stringValue: (value) => ({ stringValue: value }),
        _: (name, value) => ({ [name]: value }),
    });
};
const se_RetrievalFilter = (input, context) => {
    return RetrievalFilter.visit(input, {
        andAll: (value) => ({ andAll: se_RetrievalFilterList(value, context) }),
        equals: (value) => ({ equals: se_FilterAttribute(value, context) }),
        greaterThan: (value) => ({ greaterThan: se_FilterAttribute(value, context) }),
        greaterThanOrEquals: (value) => ({ greaterThanOrEquals: se_FilterAttribute(value, context) }),
        in: (value) => ({ in: se_FilterAttribute(value, context) }),
        lessThan: (value) => ({ lessThan: se_FilterAttribute(value, context) }),
        lessThanOrEquals: (value) => ({ lessThanOrEquals: se_FilterAttribute(value, context) }),
        listContains: (value) => ({ listContains: se_FilterAttribute(value, context) }),
        notEquals: (value) => ({ notEquals: se_FilterAttribute(value, context) }),
        notIn: (value) => ({ notIn: se_FilterAttribute(value, context) }),
        orAll: (value) => ({ orAll: se_RetrievalFilterList(value, context) }),
        startsWith: (value) => ({ startsWith: se_FilterAttribute(value, context) }),
        stringContains: (value) => ({ stringContains: se_FilterAttribute(value, context) }),
        _: (name, value) => ({ [name]: value }),
    });
};
const se_RetrievalFilterList = (input, context) => {
    return input
        .filter((e) => e != null)
        .map((entry) => {
        return se_RetrievalFilter(entry, context);
    });
};
const se_RetrieveAndGenerateConfiguration = (input, context) => {
    return take(input, {
        externalSourcesConfiguration: (_) => se_ExternalSourcesRetrieveAndGenerateConfiguration(_, context),
        knowledgeBaseConfiguration: (_) => se_KnowledgeBaseRetrieveAndGenerateConfiguration(_, context),
        type: [],
    });
};
const se_RetrieveConfig = (input, context) => {
    return take(input, {
        knowledgeBaseId: [],
        knowledgeBaseRetrievalConfiguration: (_) => se_KnowledgeBaseRetrievalConfiguration(_, context),
    });
};
const se_RoutingCriteria = (input, context) => {
    return take(input, {
        responseQualityDifference: __serializeFloat,
    });
};
const se_TextInferenceConfig = (input, context) => {
    return take(input, {
        maxTokens: [],
        stopSequences: _json,
        temperature: __serializeFloat,
        topP: __serializeFloat,
    });
};
const se_VectorSearchBedrockRerankingConfiguration = (input, context) => {
    return take(input, {
        metadataConfiguration: _json,
        modelConfiguration: (_) => se_VectorSearchBedrockRerankingModelConfiguration(_, context),
        numberOfRerankedResults: [],
    });
};
const se_VectorSearchBedrockRerankingModelConfiguration = (input, context) => {
    return take(input, {
        additionalModelRequestFields: (_) => se_AdditionalModelRequestFields(_, context),
        modelArn: [],
    });
};
const se_VectorSearchRerankingConfiguration = (input, context) => {
    return take(input, {
        bedrockRerankingConfiguration: (_) => se_VectorSearchBedrockRerankingConfiguration(_, context),
        type: [],
    });
};
const de_AdditionalModelRequestFields = (output, context) => {
    return Object.entries(output).reduce((acc, [key, value]) => {
        if (value === null) {
            return acc;
        }
        acc[key] = de_AdditionalModelRequestFieldsValue(value, context);
        return acc;
    }, {});
};
const de_AdditionalModelRequestFieldsValue = (output, context) => {
    return output;
};
const de_AutomatedEvaluationConfig = (output, context) => {
    return take(output, {
        customMetricConfig: (_) => de_AutomatedEvaluationCustomMetricConfig(_, context),
        datasetMetricConfigs: _json,
        evaluatorModelConfig: (_) => _json(__expectUnion(_)),
    });
};
const de_AutomatedEvaluationCustomMetricConfig = (output, context) => {
    return take(output, {
        customMetrics: (_) => de_AutomatedEvaluationCustomMetrics(_, context),
        evaluatorModelConfig: _json,
    });
};
const de_AutomatedEvaluationCustomMetrics = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_AutomatedEvaluationCustomMetricSource(__expectUnion(entry), context);
    });
    return retVal;
};
const de_AutomatedEvaluationCustomMetricSource = (output, context) => {
    if (output.customMetricDefinition != null) {
        return {
            customMetricDefinition: de_CustomMetricDefinition(output.customMetricDefinition, context),
        };
    }
    return { $unknown: Object.entries(output)[0] };
};
const de_ByteContentDoc = (output, context) => {
    return take(output, {
        contentType: __expectString,
        data: context.base64Decoder,
        identifier: __expectString,
    });
};
const de_CustomMetricDefinition = (output, context) => {
    return take(output, {
        instructions: __expectString,
        name: __expectString,
        ratingScale: (_) => de_RatingScale(_, context),
    });
};
const de_CustomModelSummary = (output, context) => {
    return take(output, {
        baseModelArn: __expectString,
        baseModelName: __expectString,
        creationTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        customizationType: __expectString,
        modelArn: __expectString,
        modelName: __expectString,
        modelStatus: __expectString,
        ownerAccountId: __expectString,
    });
};
const de_CustomModelSummaryList = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_CustomModelSummary(entry, context);
    });
    return retVal;
};
const de_DataProcessingDetails = (output, context) => {
    return take(output, {
        creationTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        lastModifiedTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        status: __expectString,
    });
};
const de_EvaluationConfig = (output, context) => {
    if (output.automated != null) {
        return {
            automated: de_AutomatedEvaluationConfig(output.automated, context),
        };
    }
    if (output.human != null) {
        return {
            human: _json(output.human),
        };
    }
    return { $unknown: Object.entries(output)[0] };
};
const de_EvaluationInferenceConfig = (output, context) => {
    if (output.models != null) {
        return {
            models: _json(output.models),
        };
    }
    if (output.ragConfigs != null) {
        return {
            ragConfigs: de_RagConfigs(output.ragConfigs, context),
        };
    }
    return { $unknown: Object.entries(output)[0] };
};
const de_EvaluationSummaries = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_EvaluationSummary(entry, context);
    });
    return retVal;
};
const de_EvaluationSummary = (output, context) => {
    return take(output, {
        applicationType: __expectString,
        creationTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        customMetricsEvaluatorModelIdentifiers: _json,
        evaluationTaskTypes: _json,
        evaluatorModelIdentifiers: _json,
        inferenceConfigSummary: _json,
        jobArn: __expectString,
        jobName: __expectString,
        jobType: __expectString,
        modelIdentifiers: _json,
        ragIdentifiers: _json,
        status: __expectString,
    });
};
const de_ExternalSource = (output, context) => {
    return take(output, {
        byteContent: (_) => de_ByteContentDoc(_, context),
        s3Location: _json,
        sourceType: __expectString,
    });
};
const de_ExternalSources = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_ExternalSource(entry, context);
    });
    return retVal;
};
const de_ExternalSourcesGenerationConfiguration = (output, context) => {
    return take(output, {
        additionalModelRequestFields: (_) => de_AdditionalModelRequestFields(_, context),
        guardrailConfiguration: _json,
        kbInferenceConfig: (_) => de_KbInferenceConfig(_, context),
        promptTemplate: _json,
    });
};
const de_ExternalSourcesRetrieveAndGenerateConfiguration = (output, context) => {
    return take(output, {
        generationConfiguration: (_) => de_ExternalSourcesGenerationConfiguration(_, context),
        modelArn: __expectString,
        sources: (_) => de_ExternalSources(_, context),
    });
};
const de_FilterAttribute = (output, context) => {
    return take(output, {
        key: __expectString,
        value: (_) => de_FilterValue(_, context),
    });
};
const de_FilterValue = (output, context) => {
    return output;
};
const de_GenerationConfiguration = (output, context) => {
    return take(output, {
        additionalModelRequestFields: (_) => de_AdditionalModelRequestFields(_, context),
        guardrailConfiguration: _json,
        kbInferenceConfig: (_) => de_KbInferenceConfig(_, context),
        promptTemplate: _json,
    });
};
const de_GuardrailContextualGroundingFilter = (output, context) => {
    return take(output, {
        action: __expectString,
        enabled: __expectBoolean,
        threshold: __limitedParseDouble,
        type: __expectString,
    });
};
const de_GuardrailContextualGroundingFilters = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_GuardrailContextualGroundingFilter(entry, context);
    });
    return retVal;
};
const de_GuardrailContextualGroundingPolicy = (output, context) => {
    return take(output, {
        filters: (_) => de_GuardrailContextualGroundingFilters(_, context),
    });
};
const de_GuardrailSummaries = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_GuardrailSummary(entry, context);
    });
    return retVal;
};
const de_GuardrailSummary = (output, context) => {
    return take(output, {
        arn: __expectString,
        createdAt: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        crossRegionDetails: _json,
        description: __expectString,
        id: __expectString,
        name: __expectString,
        status: __expectString,
        updatedAt: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        version: __expectString,
    });
};
const de_ImportedModelSummary = (output, context) => {
    return take(output, {
        creationTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        instructSupported: __expectBoolean,
        modelArchitecture: __expectString,
        modelArn: __expectString,
        modelName: __expectString,
    });
};
const de_ImportedModelSummaryList = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_ImportedModelSummary(entry, context);
    });
    return retVal;
};
const de_InferenceProfileSummaries = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_InferenceProfileSummary(entry, context);
    });
    return retVal;
};
const de_InferenceProfileSummary = (output, context) => {
    return take(output, {
        createdAt: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        description: __expectString,
        inferenceProfileArn: __expectString,
        inferenceProfileId: __expectString,
        inferenceProfileName: __expectString,
        models: _json,
        status: __expectString,
        type: __expectString,
        updatedAt: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
    });
};
const de_KbInferenceConfig = (output, context) => {
    return take(output, {
        textInferenceConfig: (_) => de_TextInferenceConfig(_, context),
    });
};
const de_KnowledgeBaseConfig = (output, context) => {
    if (output.retrieveAndGenerateConfig != null) {
        return {
            retrieveAndGenerateConfig: de_RetrieveAndGenerateConfiguration(output.retrieveAndGenerateConfig, context),
        };
    }
    if (output.retrieveConfig != null) {
        return {
            retrieveConfig: de_RetrieveConfig(output.retrieveConfig, context),
        };
    }
    return { $unknown: Object.entries(output)[0] };
};
const de_KnowledgeBaseRetrievalConfiguration = (output, context) => {
    return take(output, {
        vectorSearchConfiguration: (_) => de_KnowledgeBaseVectorSearchConfiguration(_, context),
    });
};
const de_KnowledgeBaseRetrieveAndGenerateConfiguration = (output, context) => {
    return take(output, {
        generationConfiguration: (_) => de_GenerationConfiguration(_, context),
        knowledgeBaseId: __expectString,
        modelArn: __expectString,
        orchestrationConfiguration: _json,
        retrievalConfiguration: (_) => de_KnowledgeBaseRetrievalConfiguration(_, context),
    });
};
const de_KnowledgeBaseVectorSearchConfiguration = (output, context) => {
    return take(output, {
        filter: (_) => de_RetrievalFilter(__expectUnion(_), context),
        implicitFilterConfiguration: _json,
        numberOfResults: __expectInt32,
        overrideSearchType: __expectString,
        rerankingConfiguration: (_) => de_VectorSearchRerankingConfiguration(_, context),
    });
};
const de_MarketplaceModelEndpoint = (output, context) => {
    return take(output, {
        createdAt: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        endpointArn: __expectString,
        endpointConfig: (_) => _json(__expectUnion(_)),
        endpointStatus: __expectString,
        endpointStatusMessage: __expectString,
        modelSourceIdentifier: __expectString,
        status: __expectString,
        statusMessage: __expectString,
        updatedAt: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
    });
};
const de_MarketplaceModelEndpointSummaries = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_MarketplaceModelEndpointSummary(entry, context);
    });
    return retVal;
};
const de_MarketplaceModelEndpointSummary = (output, context) => {
    return take(output, {
        createdAt: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        endpointArn: __expectString,
        modelSourceIdentifier: __expectString,
        status: __expectString,
        statusMessage: __expectString,
        updatedAt: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
    });
};
const de_ModelCopyJobSummaries = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_ModelCopyJobSummary(entry, context);
    });
    return retVal;
};
const de_ModelCopyJobSummary = (output, context) => {
    return take(output, {
        creationTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        failureMessage: __expectString,
        jobArn: __expectString,
        sourceAccountId: __expectString,
        sourceModelArn: __expectString,
        sourceModelName: __expectString,
        status: __expectString,
        targetModelArn: __expectString,
        targetModelKmsKeyArn: __expectString,
        targetModelName: __expectString,
        targetModelTags: _json,
    });
};
const de_ModelCustomizationJobSummaries = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_ModelCustomizationJobSummary(entry, context);
    });
    return retVal;
};
const de_ModelCustomizationJobSummary = (output, context) => {
    return take(output, {
        baseModelArn: __expectString,
        creationTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        customModelArn: __expectString,
        customModelName: __expectString,
        customizationType: __expectString,
        endTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        jobArn: __expectString,
        jobName: __expectString,
        lastModifiedTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        status: __expectString,
        statusDetails: (_) => de_StatusDetails(_, context),
    });
};
const de_ModelImportJobSummaries = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_ModelImportJobSummary(entry, context);
    });
    return retVal;
};
const de_ModelImportJobSummary = (output, context) => {
    return take(output, {
        creationTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        endTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        importedModelArn: __expectString,
        importedModelName: __expectString,
        jobArn: __expectString,
        jobName: __expectString,
        lastModifiedTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        status: __expectString,
    });
};
const de_ModelInvocationJobSummaries = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_ModelInvocationJobSummary(entry, context);
    });
    return retVal;
};
const de_ModelInvocationJobSummary = (output, context) => {
    return take(output, {
        clientRequestToken: __expectString,
        endTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        inputDataConfig: (_) => _json(__expectUnion(_)),
        jobArn: __expectString,
        jobExpirationTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        jobName: __expectString,
        lastModifiedTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        message: __expectString,
        modelId: __expectString,
        outputDataConfig: (_) => _json(__expectUnion(_)),
        roleArn: __expectString,
        status: __expectString,
        submitTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        timeoutDurationInHours: __expectInt32,
        vpcConfig: _json,
    });
};
const de_PromptRouterSummaries = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_PromptRouterSummary(entry, context);
    });
    return retVal;
};
const de_PromptRouterSummary = (output, context) => {
    return take(output, {
        createdAt: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        description: __expectString,
        fallbackModel: _json,
        models: _json,
        promptRouterArn: __expectString,
        promptRouterName: __expectString,
        routingCriteria: (_) => de_RoutingCriteria(_, context),
        status: __expectString,
        type: __expectString,
        updatedAt: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
    });
};
const de_ProvisionedModelSummaries = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_ProvisionedModelSummary(entry, context);
    });
    return retVal;
};
const de_ProvisionedModelSummary = (output, context) => {
    return take(output, {
        commitmentDuration: __expectString,
        commitmentExpirationTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        creationTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        desiredModelArn: __expectString,
        desiredModelUnits: __expectInt32,
        foundationModelArn: __expectString,
        lastModifiedTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        modelArn: __expectString,
        modelUnits: __expectInt32,
        provisionedModelArn: __expectString,
        provisionedModelName: __expectString,
        status: __expectString,
    });
};
const de_RAGConfig = (output, context) => {
    if (output.knowledgeBaseConfig != null) {
        return {
            knowledgeBaseConfig: de_KnowledgeBaseConfig(__expectUnion(output.knowledgeBaseConfig), context),
        };
    }
    if (output.precomputedRagSourceConfig != null) {
        return {
            precomputedRagSourceConfig: _json(__expectUnion(output.precomputedRagSourceConfig)),
        };
    }
    return { $unknown: Object.entries(output)[0] };
};
const de_RagConfigs = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_RAGConfig(__expectUnion(entry), context);
    });
    return retVal;
};
const de_RatingScale = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_RatingScaleItem(entry, context);
    });
    return retVal;
};
const de_RatingScaleItem = (output, context) => {
    return take(output, {
        definition: __expectString,
        value: (_) => de_RatingScaleItemValue(__expectUnion(_), context),
    });
};
const de_RatingScaleItemValue = (output, context) => {
    if (__limitedParseFloat32(output.floatValue) !== undefined) {
        return { floatValue: __limitedParseFloat32(output.floatValue) };
    }
    if (__expectString(output.stringValue) !== undefined) {
        return { stringValue: __expectString(output.stringValue) };
    }
    return { $unknown: Object.entries(output)[0] };
};
const de_RetrievalFilter = (output, context) => {
    if (output.andAll != null) {
        return {
            andAll: de_RetrievalFilterList(output.andAll, context),
        };
    }
    if (output.equals != null) {
        return {
            equals: de_FilterAttribute(output.equals, context),
        };
    }
    if (output.greaterThan != null) {
        return {
            greaterThan: de_FilterAttribute(output.greaterThan, context),
        };
    }
    if (output.greaterThanOrEquals != null) {
        return {
            greaterThanOrEquals: de_FilterAttribute(output.greaterThanOrEquals, context),
        };
    }
    if (output.in != null) {
        return {
            in: de_FilterAttribute(output.in, context),
        };
    }
    if (output.lessThan != null) {
        return {
            lessThan: de_FilterAttribute(output.lessThan, context),
        };
    }
    if (output.lessThanOrEquals != null) {
        return {
            lessThanOrEquals: de_FilterAttribute(output.lessThanOrEquals, context),
        };
    }
    if (output.listContains != null) {
        return {
            listContains: de_FilterAttribute(output.listContains, context),
        };
    }
    if (output.notEquals != null) {
        return {
            notEquals: de_FilterAttribute(output.notEquals, context),
        };
    }
    if (output.notIn != null) {
        return {
            notIn: de_FilterAttribute(output.notIn, context),
        };
    }
    if (output.orAll != null) {
        return {
            orAll: de_RetrievalFilterList(output.orAll, context),
        };
    }
    if (output.startsWith != null) {
        return {
            startsWith: de_FilterAttribute(output.startsWith, context),
        };
    }
    if (output.stringContains != null) {
        return {
            stringContains: de_FilterAttribute(output.stringContains, context),
        };
    }
    return { $unknown: Object.entries(output)[0] };
};
const de_RetrievalFilterList = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_RetrievalFilter(__expectUnion(entry), context);
    });
    return retVal;
};
const de_RetrieveAndGenerateConfiguration = (output, context) => {
    return take(output, {
        externalSourcesConfiguration: (_) => de_ExternalSourcesRetrieveAndGenerateConfiguration(_, context),
        knowledgeBaseConfiguration: (_) => de_KnowledgeBaseRetrieveAndGenerateConfiguration(_, context),
        type: __expectString,
    });
};
const de_RetrieveConfig = (output, context) => {
    return take(output, {
        knowledgeBaseId: __expectString,
        knowledgeBaseRetrievalConfiguration: (_) => de_KnowledgeBaseRetrievalConfiguration(_, context),
    });
};
const de_RoutingCriteria = (output, context) => {
    return take(output, {
        responseQualityDifference: __limitedParseDouble,
    });
};
const de_StatusDetails = (output, context) => {
    return take(output, {
        dataProcessingDetails: (_) => de_DataProcessingDetails(_, context),
        trainingDetails: (_) => de_TrainingDetails(_, context),
        validationDetails: (_) => de_ValidationDetails(_, context),
    });
};
const de_TextInferenceConfig = (output, context) => {
    return take(output, {
        maxTokens: __expectInt32,
        stopSequences: _json,
        temperature: __limitedParseFloat32,
        topP: __limitedParseFloat32,
    });
};
const de_TrainingDetails = (output, context) => {
    return take(output, {
        creationTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        lastModifiedTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        status: __expectString,
    });
};
const de_TrainingMetrics = (output, context) => {
    return take(output, {
        trainingLoss: __limitedParseFloat32,
    });
};
const de_ValidationDetails = (output, context) => {
    return take(output, {
        creationTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        lastModifiedTime: (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)),
        status: __expectString,
    });
};
const de_ValidationMetrics = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_ValidatorMetric(entry, context);
    });
    return retVal;
};
const de_ValidatorMetric = (output, context) => {
    return take(output, {
        validationLoss: __limitedParseFloat32,
    });
};
const de_VectorSearchBedrockRerankingConfiguration = (output, context) => {
    return take(output, {
        metadataConfiguration: _json,
        modelConfiguration: (_) => de_VectorSearchBedrockRerankingModelConfiguration(_, context),
        numberOfRerankedResults: __expectInt32,
    });
};
const de_VectorSearchBedrockRerankingModelConfiguration = (output, context) => {
    return take(output, {
        additionalModelRequestFields: (_) => de_AdditionalModelRequestFields(_, context),
        modelArn: __expectString,
    });
};
const de_VectorSearchRerankingConfiguration = (output, context) => {
    return take(output, {
        bedrockRerankingConfiguration: (_) => de_VectorSearchBedrockRerankingConfiguration(_, context),
        type: __expectString,
    });
};
const deserializeMetadata = (output) => ({
    httpStatusCode: output.statusCode,
    requestId: output.headers["x-amzn-requestid"] ?? output.headers["x-amzn-request-id"] ?? output.headers["x-amz-request-id"],
    extendedRequestId: output.headers["x-amz-id-2"],
    cfId: output.headers["x-amz-cf-id"],
});
const collectBodyString = (streamBody, context) => collectBody(streamBody, context).then((body) => context.utf8Encoder(body));
const _aTE = "applicationTypeEquals";
const _bCT = "byCustomizationType";
const _bIT = "byInferenceType";
const _bMAE = "baseModelArnEquals";
const _bOM = "byOutputModality";
const _bP = "byProvider";
const _cTA = "creationTimeAfter";
const _cTB = "creationTimeBefore";
const _fMAE = "foundationModelArnEquals";
const _gI = "guardrailIdentifier";
const _gV = "guardrailVersion";
const _iO = "isOwned";
const _mAE = "modelArnEquals";
const _mR = "maxResults";
const _mS = "modelStatus";
const _mSE = "modelSourceEquals";
const _mSI = "modelSourceIdentifier";
const _nC = "nameContains";
const _nT = "nextToken";
const _oMNC = "outputModelNameContains";
const _oT = "offerType";
const _sAE = "sourceAccountEquals";
const _sB = "sortBy";
const _sE = "statusEquals";
const _sMAE = "sourceModelArnEquals";
const _sO = "sortOrder";
const _sTA = "submitTimeAfter";
const _sTB = "submitTimeBefore";
const _t = "type";
const _tE = "typeEquals";
const _tMNC = "targetModelNameContains";
