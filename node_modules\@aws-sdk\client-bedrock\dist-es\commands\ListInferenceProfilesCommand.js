import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { ListInferenceProfilesResponseFilterSensitiveLog, } from "../models/models_0";
import { de_ListInferenceProfilesCommand, se_ListInferenceProfilesCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class ListInferenceProfilesCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockControlPlaneService", "ListInferenceProfiles", {})
    .n("BedrockClient", "ListInferenceProfilesCommand")
    .f(void 0, ListInferenceProfilesResponseFilterSensitiveLog)
    .ser(se_ListInferenceProfilesCommand)
    .de(de_ListInferenceProfilesCommand)
    .build() {
}
