import { Pool } from 'pg';
import { query } from '../config/database';
import { config } from '../config/environment';
import { VectorSearchResult, ConversationQuery } from '../models/business.models';

/**
 * Vector Search Service for finding relevant business information using pgvector
 */
export class VectorSearchService {
  private readonly similarityThreshold: number;
  private readonly maxResults: number;

  constructor() {
    this.similarityThreshold = config.vectorSearch.similarityThreshold;
    this.maxResults = config.vectorSearch.maxResults;
  }

  /**
   * Search for relevant businesses using vector similarity
   * @param queryVector - The query vector (embeddings)
   * @param businessType - Optional filter for business type
   * @param location - Optional location filter
   * @returns Array of relevant business results
   */
  async searchSimilarBusinesses(
    queryVector: number[],
    businessType?: 'club' | 'restaurant' | 'shop',
    location?: string
  ): Promise<VectorSearchResult[]> {
    try {
      let searchQuery = `
        SELECT 
          id,
          content,
          metadata,
          1 - (embedding <=> $1::vector) as similarity_score
        FROM business_embeddings 
        WHERE 1 - (embedding <=> $1::vector) > $2
      `;
      
      const queryParams: any[] = [JSON.stringify(queryVector), this.similarityThreshold];
      let paramIndex = 3;

      // Add business type filter if specified
      if (businessType) {
        searchQuery += ` AND metadata->>'type' = $${paramIndex}`;
        queryParams.push(businessType);
        paramIndex++;
      }

      // Add location filter if specified
      if (location) {
        searchQuery += ` AND (metadata->>'location' ILIKE $${paramIndex} OR metadata->>'emirate' ILIKE $${paramIndex})`;
        queryParams.push(`%${location}%`);
        paramIndex++;
      }

      searchQuery += ` ORDER BY similarity_score DESC LIMIT $${paramIndex}`;
      queryParams.push(this.maxResults);

      const result = await query(searchQuery, queryParams);
      
      return result.rows.map(row => ({
        id: row.id,
        content: row.content,
        metadata: row.metadata,
        similarity_score: parseFloat(row.similarity_score)
      }));
    } catch (error) {
      console.error('Error in vector search:', error);
      throw new Error('Failed to perform vector search');
    }
  }

  /**
   * Search for clubs with specific facilities or sports
   * @param queryVector - The query vector
   * @param sportType - Optional sport type filter
   * @returns Array of relevant club results
   */
  async searchClubs(queryVector: number[], sportType?: string): Promise<VectorSearchResult[]> {
    try {
      let searchQuery = `
        SELECT 
          be.id,
          be.content,
          be.metadata,
          1 - (be.embedding <=> $1::vector) as similarity_score
        FROM business_embeddings be
        WHERE be.metadata->>'type' = 'club'
        AND 1 - (be.embedding <=> $1::vector) > $2
      `;
      
      const queryParams: any[] = [JSON.stringify(queryVector), this.similarityThreshold];
      let paramIndex = 3;

      // Add sport type filter if specified
      if (sportType) {
        searchQuery += ` AND (be.content ILIKE $${paramIndex} OR be.metadata->>'tags' ILIKE $${paramIndex})`;
        queryParams.push(`%${sportType}%`);
        paramIndex++;
      }

      searchQuery += ` ORDER BY similarity_score DESC LIMIT $${paramIndex}`;
      queryParams.push(this.maxResults);

      const result = await query(searchQuery, queryParams);
      
      return result.rows.map(row => ({
        id: row.id,
        content: row.content,
        metadata: row.metadata,
        similarity_score: parseFloat(row.similarity_score)
      }));
    } catch (error) {
      console.error('Error searching clubs:', error);
      throw new Error('Failed to search clubs');
    }
  }

  /**
   * Search for restaurants with specific cuisine or menu items
   * @param queryVector - The query vector
   * @param cuisineType - Optional cuisine type filter
   * @param priceRange - Optional price range filter
   * @returns Array of relevant restaurant results
   */
  async searchRestaurants(
    queryVector: number[], 
    cuisineType?: string, 
    priceRange?: 'low' | 'medium' | 'high'
  ): Promise<VectorSearchResult[]> {
    try {
      let searchQuery = `
        SELECT 
          be.id,
          be.content,
          be.metadata,
          1 - (be.embedding <=> $1::vector) as similarity_score
        FROM business_embeddings be
        WHERE be.metadata->>'type' = 'restaurant'
        AND 1 - (be.embedding <=> $1::vector) > $2
      `;
      
      const queryParams: any[] = [JSON.stringify(queryVector), this.similarityThreshold];
      let paramIndex = 3;

      // Add cuisine type filter if specified
      if (cuisineType) {
        searchQuery += ` AND (be.content ILIKE $${paramIndex} OR be.metadata->>'tags' ILIKE $${paramIndex})`;
        queryParams.push(`%${cuisineType}%`);
        paramIndex++;
      }

      // Add price range filter if specified
      if (priceRange) {
        const priceConditions = {
          low: 'be.metadata->>\'average_spend\' < \'100\'',
          medium: 'be.metadata->>\'average_spend\' BETWEEN \'100\' AND \'300\'',
          high: 'be.metadata->>\'average_spend\' > \'300\''
        };
        searchQuery += ` AND ${priceConditions[priceRange]}`;
      }

      searchQuery += ` ORDER BY similarity_score DESC LIMIT $${paramIndex}`;
      queryParams.push(this.maxResults);

      const result = await query(searchQuery, queryParams);
      
      return result.rows.map(row => ({
        id: row.id,
        content: row.content,
        metadata: row.metadata,
        similarity_score: parseFloat(row.similarity_score)
      }));
    } catch (error) {
      console.error('Error searching restaurants:', error);
      throw new Error('Failed to search restaurants');
    }
  }

  /**
   * Search for shops with specific products or services
   * @param queryVector - The query vector
   * @param productType - Optional product type filter
   * @returns Array of relevant shop results
   */
  async searchShops(queryVector: number[], productType?: string): Promise<VectorSearchResult[]> {
    try {
      let searchQuery = `
        SELECT 
          be.id,
          be.content,
          be.metadata,
          1 - (be.embedding <=> $1::vector) as similarity_score
        FROM business_embeddings be
        WHERE be.metadata->>'type' = 'shop'
        AND 1 - (be.embedding <=> $1::vector) > $2
      `;
      
      const queryParams: any[] = [JSON.stringify(queryVector), this.similarityThreshold];
      let paramIndex = 3;

      // Add product type filter if specified
      if (productType) {
        searchQuery += ` AND (be.content ILIKE $${paramIndex} OR be.metadata->>'tags' ILIKE $${paramIndex})`;
        queryParams.push(`%${productType}%`);
        paramIndex++;
      }

      searchQuery += ` ORDER BY similarity_score DESC LIMIT $${paramIndex}`;
      queryParams.push(this.maxResults);

      const result = await query(searchQuery, queryParams);
      
      return result.rows.map(row => ({
        id: row.id,
        content: row.content,
        metadata: row.metadata,
        similarity_score: parseFloat(row.similarity_score)
      }));
    } catch (error) {
      console.error('Error searching shops:', error);
      throw new Error('Failed to search shops');
    }
  }

  /**
   * Get business details by ID
   * @param businessId - The business ID
   * @param businessType - The business type
   * @returns Business details
   */
  async getBusinessDetails(businessId: string, businessType: 'club' | 'restaurant' | 'shop'): Promise<any> {
    try {
      let tableName: string;
      let idColumn: string;

      switch (businessType) {
        case 'club':
          tableName = 'Clubs';
          idColumn = 'club_id';
          break;
        case 'restaurant':
          tableName = 'Restaurants';
          idColumn = 'restaurant_id';
          break;
        case 'shop':
          tableName = 'Shops';
          idColumn = 'shop_id';
          break;
        default:
          throw new Error('Invalid business type');
      }

      const result = await query(`SELECT * FROM "${tableName}" WHERE ${idColumn} = $1`, [businessId]);
      return result.rows[0] || null;
    } catch (error) {
      console.error('Error getting business details:', error);
      throw new Error('Failed to get business details');
    }
  }

  /**
   * Initialize the business_embeddings table if it doesn't exist
   */
  async initializeEmbeddingsTable(): Promise<void> {
    try {
      const createTableQuery = `
        CREATE TABLE IF NOT EXISTS business_embeddings (
          id VARCHAR PRIMARY KEY,
          content TEXT NOT NULL,
          metadata JSONB NOT NULL,
          embedding vector(${config.vectorSearch.embeddingDimensions}) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS business_embeddings_embedding_idx 
        ON business_embeddings USING ivfflat (embedding vector_cosine_ops);
        
        CREATE INDEX IF NOT EXISTS business_embeddings_metadata_idx 
        ON business_embeddings USING gin (metadata);
      `;
      
      await query(createTableQuery);
      console.log('✅ Business embeddings table initialized');
    } catch (error) {
      console.error('Error initializing embeddings table:', error);
      throw new Error('Failed to initialize embeddings table');
    }
  }
}
