import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { GetEvaluationJobRequestFilterSensitiveLog } from "../models/models_0";
import { GetEvaluationJobResponseFilterSensitiveLog } from "../models/models_1";
import { de_GetEvaluationJobCommand, se_GetEvaluationJobCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class GetEvaluationJobCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockControlPlaneService", "GetEvaluationJob", {})
    .n("BedrockClient", "GetEvaluationJobCommand")
    .f(GetEvaluationJobRequestFilterSensitiveLog, GetEvaluationJobResponseFilterSensitiveLog)
    .ser(se_GetEvaluationJobCommand)
    .de(de_GetEvaluationJobCommand)
    .build() {
}
