import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { ListModelInvocationJobsResponseFilterSensitiveLog, } from "../models/models_0";
import { de_ListModelInvocationJobsCommand, se_ListModelInvocationJobsCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class ListModelInvocationJobsCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockControlPlaneService", "ListModelInvocationJobs", {})
    .n("BedrockClient", "ListModelInvocationJobsCommand")
    .f(void 0, ListModelInvocationJobsResponseFilterSensitiveLog)
    .ser(se_ListModelInvocationJobsCommand)
    .de(de_ListModelInvocationJobsCommand)
    .build() {
}
