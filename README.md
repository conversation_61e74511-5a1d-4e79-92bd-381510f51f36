# Cravin Concierge - Conversational AI Bot

A professional conversational AI bot for the Cravin Concierge application that provides intelligent responses about local businesses using vector search capabilities and AWS Bedrock AI models.

## Features

- **Conversational AI Interface**: Natural language processing using AWS Bedrock (Claude 3 Sonnet)
- **Vector Similarity Search**: PostgreSQL with pgvector extension for finding relevant business information
- **Multi-Business Support**: Sports clubs, restaurants, and commerce shops
- **Professional Responses**: Contextually appropriate, conversational responses
- **TypeScript & Express.js**: Built with proper MVC architecture
- **Real-time Search**: Fast vector-based similarity search for business discovery

## Prerequisites

- Node.js (>= 16.0.0)
- PostgreSQL with pgvector extension
- AWS Account with Bedrock access
- npm (>= 8.0.0)

## Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/Just-Cravin/Cravin-Concierge.git
   cd Cravin-Concierge
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Set up PostgreSQL with pgvector**
   ```sql
   -- Connect to your PostgreSQL database
   CREATE EXTENSION IF NOT EXISTS vector;
   
   -- Run the schema.sql file to create tables
   \i schema.sql
   ```

5. **Configure AWS Bedrock**
   - Ensure you have AWS credentials configured
   - Enable access to Claude 3 Sonnet and Titan Embeddings models in AWS Bedrock
   - Update your .env file with AWS credentials

## Configuration

### Environment Variables

Key environment variables you need to configure:

```env
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/cravin_concierge

# AWS Bedrock
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0

# Vector Search
VECTOR_SIMILARITY_THRESHOLD=0.7
VECTOR_MAX_RESULTS=5
EMBEDDING_DIMENSIONS=1536
```

## Usage

### Development

```bash
# Start development server with hot reload
npm run dev

# Build the project
npm run build

# Start production server
npm start
```

### API Endpoints

#### Conversational AI

**POST** `/api/ai/query`
```json
{
  "message": "Find me Italian restaurants in Dubai Marina",
  "user_location": {
    "emirate": "Dubai",
    "address": "Dubai Marina"
  },
  "preferences": {
    "business_type": "restaurant",
    "cuisine_type": "Italian",
    "price_range": "medium"
  }
}
```

**POST** `/api/ai/search`
```json
{
  "type": "restaurant",
  "query": "seafood restaurants",
  "location": "Dubai",
  "filters": {
    "cuisine_type": "seafood",
    "price_range": "high"
  }
}
```

**GET** `/api/ai/business/:businessType/:businessId`

**GET** `/api/ai/health` - Health check for AI services

**GET** `/api/ai/business-types` - Get available business types

**GET** `/api/ai/suggestions?type=restaurant` - Get search suggestions

### Response Format

```json
{
  "success": true,
  "data": {
    "response": "I found several excellent Italian restaurants in Dubai Marina...",
    "relevant_businesses": [
      {
        "id": "restaurant_123",
        "content": "Authentic Italian cuisine with waterfront views...",
        "metadata": {
          "type": "restaurant",
          "name": "Marina Italian Bistro",
          "location": "Dubai Marina",
          "tags": ["Italian", "Seafood", "Fine Dining"]
        },
        "similarity_score": 0.89
      }
    ],
    "suggestions": [
      "What are the opening hours?",
      "Do they take reservations?",
      "Show me the menu"
    ],
    "context_id": "ctx_1234567890_abc123"
  },
  "metadata": {
    "query_processed_at": "2024-01-15T10:30:00Z",
    "businesses_found": 3,
    "response_time": 1250
  }
}
```

## Architecture

### MVC Structure

```
src/
├── controllers/          # Request handlers
│   └── conversationalAI.controller.ts
├── services/            # Business logic
│   ├── vectorSearch.service.ts
│   ├── bedrock.service.ts
│   └── responseFormatter.service.ts
├── models/              # Data models and interfaces
│   └── business.models.ts
├── routes/              # API routes
│   └── conversationalAI.routes.ts
├── config/              # Configuration
│   ├── database.ts
│   └── environment.ts
└── middleware/          # Express middleware
```

### Key Components

1. **VectorSearchService**: Handles pgvector similarity search
2. **BedrockService**: Manages AWS Bedrock AI interactions
3. **ResponseFormatterService**: Formats responses for professional output
4. **ConversationalAIController**: Orchestrates the AI conversation flow

## Database Schema

The application uses the existing database schema with three main business categories:

- **Clubs**: Sports clubs and recreational facilities
- **Restaurants**: Dining establishments with branches, menus, and items
- **Shops**: Retail and commercial services

Vector embeddings are stored in a separate `business_embeddings` table for efficient similarity search.

## AI Models

- **Text Generation**: Claude 3 Sonnet (anthropic.claude-3-sonnet-20240229-v1:0)
- **Embeddings**: Amazon Titan Embeddings (amazon.titan-embed-text-v1)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

ISC License

## Support

For support and questions, please open an issue in the GitHub repository.
