import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { ListPromptRoutersResponseFilterSensitiveLog, } from "../models/models_0";
import { de_ListPromptRoutersCommand, se_ListPromptRoutersCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class ListPromptRoutersCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockControlPlaneService", "ListPromptRouters", {})
    .n("BedrockClient", "ListPromptRoutersCommand")
    .f(void 0, ListPromptRoutersResponseFilterSensitiveLog)
    .ser(se_ListPromptRoutersCommand)
    .de(de_ListPromptRoutersCommand)
    .build() {
}
