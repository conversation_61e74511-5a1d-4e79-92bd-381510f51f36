
FLOW: toNumber
input: x, options
IF not string
  END x
ELSE_IF should skip
  END x
ELSE_IF 0
  END 0
ELSE_IF hex is supported AND x is hex
  END int of x of base 16
ELSE_IF possible e notation
  FOLLOW: resolve enotation (x, trimmed x, options)
ELSE
  IF match numeric pattern
    separate sign, leading zeros, pure number
    IF x doesn't starts with "[+-]0."
      END number(x)
    IF leading zeros are not allowed 
      IF leading zeros > 1
        #00.1
        END x
      ELSE_IF leading zeros == 1 AND decimal is not adjacent to leading zeros
        #06.5
        #but not 0.65, .65, 6.0
        END x
    ELSE_IF str has only zeros
      END 0
    ELSE
      parse x to number
      IF parsed x == 0 or -0
        END parsed x
      ELSE_IF parsed x is eNotation
        IF conversion to enotation is allowed
          END parsed x
        ELSE
          END x
      ELSE_IF floating number
        IF parsed x is 0
          END parsed x 
        ELSE_IF parsed x == number without leading 0s
          #0.456. 0.79000
          END parsed x
        ELSE_IF parsed x is negative AND == parsed x == number without leading 0s
          END parsed x
        ELSE
          END x
      ELSE_IF leading 0s are present
        IF parsed x == x without leading 0s
          END parsed x
        ELSE
          END x
      ELSE
        IF parsed x == x (consider sign)
          END parsed x
        ELSE
          END x

  ELSE
    END x



FLOW: resolve enotation
input: x, trimmed x, options
IF eNotation has not to be evaluated
  END x
IF match eNotation pattern
  extract sign, eChar, leading zeros
  find if eChar adjacent to leading zeros

  IF leading zeros > 1 AND eChar adjacent to leading zeros
    # 00e, -00e
    END x
  ELSE_IF exp is `0e`, `0.e`, `-0.e`, `-0e`
    END number(x); 
  ELSE_IF leading zeros are allowed but eChar is not adjacent to leading zeros
    # -003e2
    remove leading zeros
    END number(x)
  ELSE
    END x
ELSE
  END x