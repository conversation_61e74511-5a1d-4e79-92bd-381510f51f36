import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_DeleteProvisionedModelThroughputCommand, se_DeleteProvisionedModelThroughputCommand, } from "../protocols/Aws_restJson1";
export { $Command };
export class DeleteProvisionedModelThroughputCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockControlPlaneService", "DeleteProvisionedModelThroughput", {})
    .n("BedrockClient", "DeleteProvisionedModelThroughputCommand")
    .f(void 0, void 0)
    .ser(se_DeleteProvisionedModelThroughputCommand)
    .de(de_DeleteProvisionedModelThroughputCommand)
    .build() {
}
